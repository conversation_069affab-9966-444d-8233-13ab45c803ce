# 打印项目信息和小组成员信息，用于标识项目内容和团队成员
print("大作业，内容为风电功率预测模型，使用MLP模型，数据来源为Open-Meteo API，小组成员：胡智军202431050258，王子强202431050259，" \
"董墨202431040280，邬亚辰 202431050514，陈思同 202431050073")  # 输出项目基本信息和所有小组成员的姓名学号

# 导入所需的库和模块 - 系统和工具类库
import os, warnings, requests, pandas as pd, numpy as np, tensorflow as tf, threading, time  # 导入操作系统、警告处理、HTTP请求、数据处理、数值计算、深度学习、多线程、时间处理库
from tensorflow import keras  # 从TensorFlow中导入Keras深度学习高级API，用于构建神经网络模型
from sklearn.preprocessing import MinMaxScaler, StandardScaler  # 从scikit-learn导入数据标准化工具，用于特征缩放
from sklearn.model_selection import train_test_split  # 从scikit-learn导入数据集分割工具，用于划分训练集和验证集
from datetime import datetime, timedelta  # 导入日期时间处理工具，用于时间计算和格式化
import matplotlib.pyplot as plt, tkinter as tk  # 导入matplotlib绘图库和tkinter图形用户界面库
from tkinter import messagebox, ttk, font  # 从tkinter导入消息框、主题化组件和字体模块
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg  # 导入matplotlib的tkinter后端，用于在GUI中嵌入图表
import matplotlib  # 导入matplotlib主模块，用于全局配置

# 环境配置 - 设置运行环境参数
os.environ['TF_CPP_MIN_LOG_LEVEL'] = '3'  # 设置TensorFlow日志级别为3，只显示ERROR级别信息，隐藏INFO和WARNING
warnings.filterwarnings('ignore')  # 忽略所有Python警告信息，保持输出清洁
matplotlib.rcParams['font.family'] = ['DejaVu Sans', 'SimHei', 'Microsoft YaHei']  # 设置matplotlib默认字体族，支持中文显示

# 尝试导入可选依赖库，如果缺失则提示安装
try:  # 使用try-except结构处理可能的导入错误
    from tkcalendar import Calendar  # 导入第三方日历组件，用于日期选择界面
    from tqdm import tqdm  # 导入进度条组件，用于显示训练进度
except ImportError as e:  # 捕获导入错误异常
    raise ImportError(f"请先安装依赖：pip install tkcalendar tqdm")  # 抛出自定义错误信息，提示用户安装缺失的依赖包

# 设置matplotlib中文字体支持
plt.rcParams['font.sans-serif'] = ['Microsoft YaHei', 'SimHei', 'Arial', 'sans-serif']  # 设置中文字体优先级列表，确保图表中文正常显示
plt.rcParams['axes.unicode_minus'] = False  # 解决matplotlib中负号显示为方块的问题

# 界面主题配置字典 - 定义整个应用的视觉风格
THEME = {
    'bg_primary': '#f0f0f0',      # 主背景色（浅灰色），用于主要界面背景
    'bg_secondary': '#ffffff',    # 次背景色（纯白色），用于卡片和面板背景
    'bg_accent': '#e3f2fd',      # 强调背景色（浅蓝色），用于突出显示区域
    'text_primary': '#212121',    # 主文字色（深灰色），用于主要文本内容
    'text_secondary': '#757575',  # 次文字色（中灰色），用于辅助说明文字
    'accent_color': '#2196f3',    # 强调色（蓝色），用于按钮和重要元素
    'success_color': '#4caf50',   # 成功色（绿色），用于成功状态提示
    'warning_color': '#ff9800',   # 警告色（橙色），用于警告状态提示
    'error_color': '#f44336',     # 错误色（红色），用于错误状态提示
    'font_family': 'Microsoft YaHei',  # 统一字体族，确保界面字体一致性
    'font_size': 13               # 基础字体大小，用于界面文字显示
}

# ======================== 数据获取与处理模块 ========================
def fetch_weather(end_dt, gui_callback=None):
    """
    从Open-Meteo API获取天气数据的核心函数
    参数:
        end_dt: 结束日期（datetime对象），指定数据获取的截止时间
        gui_callback: GUI回调函数（可选），用于在界面中实时更新数据获取状态
    返回:
        成功时返回DataFrame格式的天气数据，失败时返回错误信息字符串
    """
    start_dt = end_dt - timedelta(days=29)  # 计算开始日期，向前推29天，确保获取30天的数据用于模型训练
    start_str, end_str = start_dt.strftime('%Y-%m-%d'), end_dt.strftime('%Y-%m-%d')  # 将日期对象格式化为API要求的字符串格式
    cache_dir = r"D:\.cursor\03_数据缓存"  # 定义数据缓存目录路径，用于存储已下载的气象数据
    os.makedirs(cache_dir, exist_ok=True)  # 创建缓存目录，如果目录已存在则不报错
    cache_file = os.path.join(cache_dir, f"weather_cache_{start_str}_{end_str}.csv")  # 构建缓存文件的完整路径，文件名包含日期范围

    if os.path.exists(cache_file):  # 检查缓存文件是否已存在，避免重复下载相同数据
        return pd.read_csv(cache_file, parse_dates=['timestamp'])  # 从缓存文件读取数据并解析时间戳列为日期格式

    # 构建API请求参数字典，指定获取的地理位置和气象要素
    params = {'latitude': 30.67, 'longitude': 104.06, 'start_date': start_str, 'end_date': end_str,  # 设置成都地区的经纬度坐标和时间范围
              'hourly': "temperature_2m,relative_humidity_2m,pressure_msl", 'timezone': "Asia/Shanghai"}  # 指定需要的气象参数和时区

    try:  # 使用异常处理确保API请求的稳定性
        if gui_callback: gui_callback("🌐 正在从API获取气象数据...\n")  # 如果提供了GUI回调函数，则更新界面显示当前状态
        data = requests.get("https://archive-api.open-meteo.com/v1/archive", params=params, timeout=10).json()  # 发送HTTP GET请求到Open-Meteo API并解析JSON响应
        h = data["hourly"]  # 从API响应中提取小时级气象数据部分
        df = pd.DataFrame({"timestamp": pd.to_datetime(h["time"]), "temperature": h["temperature_2m"],  # 创建DataFrame，包含时间戳和2米高度温度
                          "humidity": h["relative_humidity_2m"], "pressure": h["pressure_msl"]})  # 添加相对湿度和海平面气压数据
        df["hour"], df["month"], df["altitude"] = df["timestamp"].dt.hour, df["timestamp"].dt.month, 850  # 从时间戳提取小时和月份信息，设置海拔高度为850米（成都平均海拔）
        df.to_csv(cache_file, index=False)  # 将获取的数据保存到缓存文件，不保存行索引
        if gui_callback: gui_callback(f"💾 已缓存气象数据: {os.path.basename(cache_file)}\n")  # 通过回调函数通知界面数据已缓存
        return df  # 返回处理好的气象数据DataFrame
    except Exception as e:  # 捕获所有可能的异常（网络错误、API错误、数据解析错误等）
        return f"API_ERROR: {e}"  # 返回包含错误信息的字符串，便于上层函数处理

def gen_features(df):
    """
    根据气象数据生成风电特征的核心算法函数
    参数:
        df: 包含基础气象数据的DataFrame（温度、湿度、气压等）
    返回:
        添加了完整风电特征的DataFrame，包含风速、风向、功率等计算结果
    """
    n, i = len(df), np.arange(len(df))  # 获取数据总长度n和连续索引数组i，用于后续的时间序列计算
    hour, month = df["hour"].values, df["month"].values  # 提取小时和月份数组，用于计算日变化和季节变化模式

    # 风速生成算法 - 基于物理模型和统计规律
    base_ws = 8 + 3 * np.sin(2 * np.pi * i / 100)  # 生成基础风速模式，8m/s基准值加上周期性波动（100小时周期）
    diurnal = 1 + 0.3 * np.sin(2 * np.pi * hour / 24)  # 计算日变化因子，模拟一天内风速的自然变化规律
    seasonal = 1 + 0.2 * np.sin(2 * np.pi * (month - 1) / 12)  # 计算季节变化因子，模拟一年内风速的季节性变化
    ws = np.maximum(0, base_ws * diurnal * seasonal + np.random.normal(0, 1.5, n))  # 综合计算最终风速，应用各种因子并添加随机噪声，确保风速非负

    # 其他关键风电特征计算
    wd = (240 + 30 * np.sin(2 * np.pi * (month - 1) / 12) + np.random.normal(0, 30, n)) % 360  # 计算风向，基准240度加季节性变化和随机扰动，取模360确保在0-360度范围内
    ti = np.maximum(0.05, 0.25 - 0.015 * ws + np.random.normal(0, 0.02, n))  # 计算湍流强度，基于风速的负相关关系加随机噪声，最小值限制为0.05
    ad = (df["pressure"].values / 1013) * (288 / (df["temperature"].values + 273))  # 根据理想气体定律计算空气密度，使用标准大气压和温度进行归一化

    # 风电机组功率计算 - 基于典型风电机组功率曲线
    p = np.zeros(n)  # 初始化功率数组，所有值设为0
    mask2, mask3 = (ws >= 3) & (ws < 12), (ws >= 12) & (ws < 25)  # 定义风速区间掩码：切入风速3m/s到额定风速12m/s，额定风速12m/s到切出风速25m/s
    p[mask2] = 3.5 * (ws[mask2] - 3) ** 2 / 81  # 中风速区间使用二次函数计算功率，模拟风电机组功率曲线的上升段
    p[mask3] = 3.5  # 高风速区间设置为额定功率3.5MW，模拟功率曲线的平坦段
    p = np.maximum(0, p * ad * (1 - 0.1 * ti) + np.random.normal(0, 0.1, n))  # 应用空气密度修正和湍流影响，添加功率波动噪声，确保功率非负

    # 功率变化率计算 - 用于捕捉功率动态特性
    pcr = np.zeros(n)  # 初始化功率变化率数组
    pcr[1:] = (p[1:] - p[:-1]) / np.maximum(p[:-1], 0.1)  # 计算相邻时刻功率变化率，分母最小值限制为0.1避免除零错误

    # 构建并返回包含所有特征的完整DataFrame
    return pd.DataFrame({
        "timestamp": df["timestamp"], "wind_speed": ws, "wind_direction": wd, "turbulence_intensity": ti,  # 时间戳和风电核心参数
        "temperature": df["temperature"], "pressure": df["pressure"], "humidity": df["humidity"],  # 原始气象参数
        "altitude": df["altitude"], "hour": hour, "month": month, "power": p,  # 地理和时间特征，目标功率值
        "power_change_rate": pcr, "air_density": ad, "day": df["timestamp"].dt.date  # 衍生特征：功率变化率、空气密度、日期
    })

def daily_agg(df):
    """
    将小时级数据聚合为每日平均值的数据预处理函数
    参数:
        df: 包含小时级风电和气象数据的DataFrame
    返回:
        按日聚合后的DataFrame，每行代表一天的平均值
    """
    cols = ['wind_speed','wind_direction','turbulence_intensity','temperature','pressure','humidity','altitude','hour','month','power','air_density','power_change_rate']  # 定义需要聚合的所有数值列名
    ddf = df.groupby('day').agg({k:'mean' for k in cols}).reset_index()  # 按天分组，对每列计算平均值，重置索引使day列变为普通列
    ddf['timestamp'] = pd.to_datetime(ddf['day'])  # 将day列转换为timestamp列，保持时间戳格式的一致性
    return ddf  # 返回聚合后的日级数据DataFrame

def prepare_seq(df, input_days=7, pred_days=[1,2,3,7]):
    """
    准备时间序列数据用于MLP模型训练的核心数据预处理函数
    参数:
        df: 日级聚合后的DataFrame，包含所有特征和目标变量
        input_days: 输入序列长度（天数），默认7天，即用过去7天的数据预测未来
        pred_days: 预测天数列表，默认[1,2,3,7]，即预测未来1、2、3、7天的功率
    返回:
        X序列（输入特征数组）, y标签（目标值数组）, X缩放器, y缩放器
    """
    feats = ['wind_speed','wind_direction','turbulence_intensity','temperature','pressure','humidity','altitude','hour','month','air_density','power_change_rate']  # 定义用于预测的特征列表，不包含目标变量power
    sx, sy = StandardScaler(), MinMaxScaler()  # 创建特征标准化器（零均值单位方差）和目标值归一化器（0-1范围）
    X, y = sx.fit_transform(df[feats]), sy.fit_transform(df[['power']])  # 对特征进行标准化，对目标功率值进行归一化处理
    Xs, ys = [], []  # 初始化序列数据和标签列表
    for i in range(len(df)-input_days-max(pred_days)+1):  # 遍历所有可能的序列起始位置，确保有足够的数据用于输入和预测
        Xs.append(X[i:i+input_days].flatten())  # 提取连续input_days天的特征数据，展平为一维数组作为MLP输入
        ys.append([y[i+input_days+d-1][0] for d in pred_days])  # 提取对应预测天数的功率值作为标签
    return np.array(Xs), np.array(ys), sx, sy  # 转换为numpy数组并返回数据和缩放器

def print_daily_weather_table(df):
    """
    在控制台打印格式化的每日气象和风电数据表格
    参数:
        df: 包含每日聚合数据的DataFrame
    功能:
        以表格形式在控制台输出所有特征的每日平均值，便于数据检查和调试
    """
    columns = [("日期", "timestamp", 12), ("风速(m/s)", "wind_speed", 10), ("风向(°)", "wind_direction", 10),  # 定义表格列：显示名称、数据列名、列宽
               ("湍流强度", "turbulence_intensity", 10), ("温度(℃)", "temperature", 10), ("气压(hPa)", "pressure", 10),  # 气象参数列定义
               ("湿度(%)", "humidity", 8), ("海拔(m)", "altitude", 8), ("小时", "hour", 6), ("月份", "month", 6),  # 环境参数列定义
               ("空气密度", "air_density", 10), ("功率变化率", "power_change_rate", 12), ("功率(MW)", "power", 10)]  # 计算参数和目标值列定义
    header = "|" + "|".join([f"{col[0]:^{col[2]}}" for col in columns]) + "|"  # 构建表头，每列标题居中对齐
    sep = "+" + "+".join(["-"*col[2] for col in columns]) + "+"  # 构建分隔线，使用横线和加号
    print(sep + "\n" + header + "\n" + sep)  # 打印上分隔线、表头和下分隔线
    for _, row in df.iterrows():  # 遍历DataFrame的每一行数据
        vals = []  # 初始化当前行的值列表
        for col in columns:  # 遍历每一列的定义
            key, width = col[1], col[2]  # 获取列名和列宽
            if key == "timestamp":  # 如果是时间戳列
                val = str(row[key].date()) if hasattr(row[key], 'date') else str(row[key])  # 提取日期部分或转换为字符串
            else:  # 其他数值列
                val = f"{row[key]:.2f}" if isinstance(row[key], float) else str(row[key])  # 浮点数保留2位小数，其他类型转字符串
            vals.append(f"{val:^{width}}")  # 将格式化后的值居中对齐并添加到列表
        print("|" + "|".join(vals) + "|")  # 打印当前行，用竖线分隔各列
    print(sep)  # 打印底部分隔线

class WindPowerPredictionSystemMLP:
    """
    风电功率预测系统的MLP模型封装类
    功能:
        封装MLP神经网络模型，提供统一的预测接口
    """
    def __init__(self):
        """初始化预测系统，创建空的模型容器"""
        self.model = None  # 初始化模型为空，将在训练过程中创建和赋值

class TkinterLoggerCallback(keras.callbacks.Callback):
    """
    自定义的Keras训练回调类，用于在GUI界面中实时显示训练进度
    继承自keras.callbacks.Callback，重写训练过程中的关键方法
    """
    def __init__(self, gui_instance, X_val=None, y_val=None, model=None, stop_event=None):
        """
        初始化回调函数
        参数:
            gui_instance: GUI主界面实例，用于更新界面显示
            X_val: 验证集输入数据，用于实时预测展示
            y_val: 验证集标签数据，用于对比真实值和预测值
            model: 训练中的模型引用，用于获取预测结果
            stop_event: 线程停止事件，用于中断训练
        """
        super().__init__()  # 调用父类初始化方法
        self.X_val, self.y_val, self._model_ref, self.stop_event, self.gui_instance = X_val, y_val, model, stop_event, gui_instance  # 保存所有传入参数
        self.losses, self.val_losses = [], []  # 初始化损失值记录列表，用于绘制训练曲线

    def on_epoch_end(self, epoch, logs=None):
        """
        每个训练轮次结束时的回调方法
        参数:
            epoch: 当前轮次编号（从0开始）
            logs: 包含训练指标的字典（损失值、准确率等）
        """
        if logs is None: logs = {}  # 如果logs为空则初始化为空字典
        msg = f"第{epoch+1}轮 - 损失: {logs.get('loss', 0):.4f}, 验证损失: {logs.get('val_loss', 0):.4f}\n"  # 格式化训练信息，轮次从1开始显示
        self.gui_instance._safe_gui_update(self.gui_instance._update_log_text, msg)  # 线程安全地更新GUI日志文本
        self.losses.append(logs.get('loss'))  # 记录当前轮次的训练损失
        self.val_losses.append(logs.get('val_loss'))  # 记录当前轮次的验证损失
        self.gui_instance.root.after(0, self.gui_instance._update_training_plot, self.losses, self.val_losses)  # 异步更新训练损失曲线图

        if self.X_val is not None and self.y_val is not None and self._model_ref is not None:  # 如果提供了验证数据和模型引用
            try:  # 尝试进行实时预测展示
                y_pred = self._model_ref.predict(self.X_val[0:1], verbose=0)[0]  # 使用当前模型对第一个验证样本进行预测
                y_true_str = ', '.join([f"{v:.3f}" for v in self.y_val[0]])  # 格式化真实值字符串
                y_pred_str = ', '.join([f"{v:.3f}" for v in y_pred])  # 格式化预测值字符串
                self.gui_instance._safe_gui_update(self.gui_instance._update_log_text, f"  真实: [{y_true_str}] 预测: [{y_pred_str}]\n")  # 在界面显示对比结果
            except: pass  # 忽略预测过程中的任何异常，避免中断训练

        if self.stop_event and self.stop_event.is_set():  # 检查是否收到停止训练的信号
            self._model_ref.stop_training = True  # 设置模型停止训练标志

# ======================== 主界面与交互逻辑 ========================
class WindPowerGUI:
    """
    风电功率预测系统的图形用户界面主类
    功能:
        提供完整的GUI界面，包括数据输入、模型训练、结果展示等功能
        集成数据获取、特征工程、模型训练、预测展示的完整工作流程
    """
    def __init__(self, root):
        """
        初始化GUI主界面
        参数:
            root: Tkinter主窗口对象
        """
        self.root = root  # 保存主窗口引用
        self._setup_window()  # 设置窗口基本属性（标题、大小、位置等）
        self._setup_styles()  # 配置界面样式主题
        self._create_main_layout()  # 创建主要布局结构（滚动容器等）
        self._create_widgets()  # 创建所有界面组件（按钮、文本框、图表等）
        self._init_variables()  # 初始化内部变量和状态

    def _setup_window(self):
        """
        设置主窗口的基本属性和外观
        功能:
            配置窗口标题、大小、位置、背景色、可调整性等基本属性
        """
        self.root.title("🌪️ 风电功率预测系统（MLP版）")  # 设置窗口标题，包含表情符号增加视觉效果
        screen_width, screen_height = self.root.winfo_screenwidth(), self.root.winfo_screenheight()  # 获取屏幕分辨率
        window_width, window_height = int(screen_width * 0.9), int(screen_height * 0.9)  # 设置窗口大小为屏幕的90%
        x, y = (screen_width - window_width) // 2, (screen_height - window_height) // 2  # 计算窗口居中位置
        self.root.geometry(f"{window_width}x{window_height}+{x}+{y}")  # 设置窗口大小和位置
        self.root.configure(bg=THEME['bg_primary'])  # 设置窗口背景色为主题色
        self.root.resizable(True, True)  # 允许用户调整窗口大小
        self.root.minsize(800, 600)  # 设置窗口最小尺寸，确保界面元素正常显示

    def _setup_styles(self):
        """
        配置ttk组件的样式主题
        功能:
            定义按钮、标签框等组件的颜色、字体、边框等视觉样式
        """
        style = ttk.Style()  # 创建样式管理器
        style.theme_use('clam')  # 使用clam主题作为基础样式

        # 配置不同类型按钮的样式
        for btn_type, color in [('Accent', THEME['accent_color']), ('Success', THEME['success_color']), ('Danger', THEME['error_color'])]:  # 遍历按钮类型和对应颜色
            style.configure(f'{btn_type}.TButton', background=color, foreground='white',  # 设置按钮背景色和前景色
                           font=(THEME['font_family'], THEME['font_size'], 'bold'), padding=(10, 5))  # 设置字体和内边距

        style.map('Accent.TButton', background=[('active', '#1976d2')])  # 设置强调按钮的悬停效果
        style.configure('Card.TLabelframe', background=THEME['bg_secondary'], relief='flat', borderwidth=1)  # 配置卡片式标签框样式
        style.configure('Card.TLabelframe.Label', background=THEME['bg_secondary'],  # 配置标签框标题样式
                       foreground=THEME['text_primary'], font=(THEME['font_family'], THEME['font_size'], 'bold'))

    def _create_main_layout(self):
        """
        创建主要的布局结构，包括滚动容器
        功能:
            设置可滚动的主界面布局，支持内容超出窗口时的垂直滚动
        """
        self.root.grid_rowconfigure(0, weight=1)  # 设置主窗口第0行可扩展，权重为1
        self.root.grid_columnconfigure(0, weight=1)  # 设置主窗口第0列可扩展，权重为1

        self.main_container = tk.Frame(self.root, bg=THEME['bg_primary'])  # 创建主容器框架，设置背景色
        self.main_container.grid(row=0, column=0, sticky="nsew", padx=5, pady=5)  # 将主容器放置在窗口中，四边对齐并设置边距
        self.main_container.grid_rowconfigure(0, weight=1)  # 设置主容器第0行可扩展
        self.main_container.grid_columnconfigure(0, weight=1)  # 设置主容器第0列可扩展

        self.main_canvas = tk.Canvas(self.main_container, bg=THEME['bg_primary'], highlightthickness=0)  # 创建画布组件，用于实现滚动功能
        self.main_scrollbar = ttk.Scrollbar(self.main_container, orient="vertical", command=self.main_canvas.yview)  # 创建垂直滚动条
        self.main_canvas.configure(yscrollcommand=self.main_scrollbar.set)  # 将画布的垂直滚动与滚动条关联
        self.main_canvas.grid(row=0, column=0, sticky="nsew")  # 将画布放置在主容器中，占满整个区域
        self.main_scrollbar.grid(row=0, column=1, sticky="ns")  # 将滚动条放置在右侧，垂直拉伸

        self.main_frame = tk.Frame(self.main_canvas, bg=THEME['bg_primary'])  # 创建实际内容框架，放置在画布上
        self.canvas_window = self.main_canvas.create_window((0, 0), window=self.main_frame, anchor="nw")  # 在画布上创建窗口，承载内容框架

        def configure_scroll_region(event):
            """配置滚动区域大小的回调函数"""
            self.main_canvas.configure(scrollregion=self.main_canvas.bbox("all"))  # 根据内容大小更新滚动区域
        def configure_canvas_width(event):
            """配置画布宽度的回调函数"""
            self.main_canvas.itemconfig(self.canvas_window, width=event.width)  # 根据画布宽度调整内容框架宽度

        self.main_frame.bind("<Configure>", configure_scroll_region)  # 绑定内容框架大小变化事件
        self.main_canvas.bind("<Configure>", configure_canvas_width)  # 绑定画布大小变化事件
        self.main_canvas.bind_all("<MouseWheel>", lambda e: self.main_canvas.yview_scroll(int(-1*(e.delta/120)), "units"))  # 绑定鼠标滚轮事件，实现滚动功能

    def _create_widgets(self):
        """
        创建所有界面组件的主函数
        功能:
            调用各个子函数创建标题区、日历面板、内容网格等界面元素
        """
        self.main_frame.grid_rowconfigure(2, weight=1)  # 设置主框架第2行（内容区）可扩展
        self.main_frame.grid_columnconfigure(0, weight=1)  # 设置主框架第0列可扩展
        self._create_top_section()  # 创建顶部标题和控制按钮区域
        self._create_calendar_panel()  # 创建日历选择面板
        self._create_content_grid()  # 创建主要内容网格（日志、图表等）

    def _create_top_section(self):
        """
        创建顶部区域，包括标题和控制按钮
        功能:
            显示系统标题、副标题和主要操作按钮（历史预测、当前预测、强行结束）
        """
        top_frame = tk.Frame(self.main_frame, bg=THEME['bg_primary'])  # 创建顶部框架容器
        top_frame.grid(row=0, column=0, sticky="ew", pady=(0, 3))  # 将顶部框架放置在主框架第0行，水平拉伸

        title_container = tk.Frame(top_frame, bg=THEME['bg_primary'])  # 创建标题容器
        title_container.pack(pady=(0, 5))  # 将标题容器打包，设置底部边距
        tk.Label(title_container, text="🌪️ 风电功率预测系统（MLP版）", font=(THEME['font_family'], 18, 'bold'), bg=THEME['bg_primary'], fg=THEME['text_primary']).pack()  # 创建主标题标签
        tk.Label(title_container, text="基于深度学习的智能风电功率预测平台", font=(THEME['font_family'], 13), bg=THEME['bg_primary'], fg=THEME['text_secondary']).pack()  # 创建副标题标签

        control_frame = ttk.LabelFrame(top_frame, text="🎛️ 控制面板", style='Card.TLabelframe')  # 创建控制面板标签框
        control_frame.pack(fill=tk.X, pady=2)  # 将控制面板水平填充，设置垂直边距
        btn_frame = tk.Frame(control_frame, bg=THEME['bg_secondary'])  # 创建按钮容器框架
        btn_frame.pack(pady=5)  # 将按钮容器打包，设置垂直边距

        # 批量创建控制按钮
        for text, cmd, style in [("📊 历史预测", self.hist_test, 'Accent.TButton'),  # 历史预测按钮：蓝色强调样式
                                ("⚡ 当前预测", self.now_test, 'Success.TButton'),  # 当前预测按钮：绿色成功样式
                                ("⛔ 强行结束", self.force_stop, 'Danger.TButton')]:  # 强行结束按钮：红色危险样式
            ttk.Button(btn_frame, text=text, command=cmd, style=style).pack(side=tk.LEFT, padx=5)  # 创建按钮并水平排列，设置水平间距

    def _create_content_grid(self):
        """
        创建主要内容网格布局
        功能:
            创建左右分栏布局，左侧显示日志和结果，右侧显示训练曲线和预测图表
        """
        content_frame = tk.Frame(self.main_frame, bg=THEME['bg_primary'])  # 创建内容主框架
        content_frame.grid(row=2, column=0, sticky="nsew", pady=2)  # 将内容框架放置在主框架第2行，四边对齐
        content_frame.grid_rowconfigure(0, weight=1); content_frame.grid_columnconfigure(0, weight=2); content_frame.grid_columnconfigure(1, weight=3)  # 设置行列权重：左列权重2，右列权重3
        left_frame = tk.Frame(content_frame, bg=THEME['bg_primary'])  # 创建左侧框架，用于放置日志和结果面板
        left_frame.grid(row=0, column=0, sticky="nsew", padx=(0, 2))  # 将左侧框架放置在第0列，右侧留边距
        left_frame.grid_rowconfigure(0, weight=2); left_frame.grid_rowconfigure(1, weight=1); left_frame.grid_columnconfigure(0, weight=1)  # 设置左侧框架布局：上部权重2，下部权重1
        right_frame = tk.Frame(content_frame, bg=THEME['bg_primary'])  # 创建右侧框架，用于放置图表面板
        right_frame.grid(row=0, column=1, sticky="nsew", padx=(2, 0))  # 将右侧框架放置在第1列，左侧留边距
        right_frame.grid_rowconfigure(0, weight=1); right_frame.grid_rowconfigure(1, weight=1); right_frame.grid_columnconfigure(0, weight=1)  # 设置右侧框架布局：上下等权重
        self._create_log_panel_compact(left_frame, 0, 0); self._create_result_panel_compact(left_frame, 1, 0)  # 在左侧创建日志面板（上）和结果面板（下）
        self._create_training_plot_panel_compact(right_frame, 0, 0); self._create_prediction_plot_panel_compact(right_frame, 1, 0)  # 在右侧创建训练图表面板（上）和预测图表面板（下）

    def _create_log_panel_compact(self, parent, row, col):
        """
        创建紧凑型训练日志面板
        参数:
            parent: 父容器
            row, col: 在父容器中的行列位置
        功能:
            创建带滚动条的文本框，用于显示训练过程中的实时日志信息
        """
        frame = ttk.LabelFrame(parent, text="📋 训练日志", style='Card.TLabelframe')  # 创建带标题的标签框
        frame.grid(row=row, column=col, sticky="nsew", padx=1, pady=1)  # 将标签框放置在指定位置，四边对齐
        container = tk.Frame(frame, bg=THEME['bg_secondary']); container.pack(fill=tk.BOTH, expand=True, padx=2, pady=2)  # 创建内容容器并填充整个标签框
        self.text_log = tk.Text(container, font=(THEME['font_family'], 13), bg='#f8f9fa', fg=THEME['text_primary'], relief='flat', wrap=tk.WORD)  # 创建文本框，设置字体、颜色、自动换行
        scroll = ttk.Scrollbar(container, orient="vertical", command=self.text_log.yview); self.text_log.configure(yscrollcommand=scroll.set)  # 创建垂直滚动条并与文本框关联
        self.text_log.pack(side=tk.LEFT, fill=tk.BOTH, expand=True); scroll.pack(side=tk.RIGHT, fill=tk.Y)  # 文本框左侧填充，滚动条右侧垂直填充

    def _create_training_plot_panel_compact(self, parent, row, col):
        """
        创建紧凑型训练损失曲线面板
        参数:
            parent: 父容器
            row, col: 在父容器中的行列位置
        功能:
            创建matplotlib图表，用于实时显示训练和验证损失曲线
        """
        self.frame_train_plot = ttk.LabelFrame(parent, text="📈 训练损失", style='Card.TLabelframe')  # 创建训练图表标签框
        self.frame_train_plot.grid(row=row, column=col, sticky="nsew", padx=1, pady=1)  # 将标签框放置在指定位置
        self.fig_train_plot, self.ax_train_plot = plt.subplots(figsize=(10, 5), facecolor=THEME['bg_secondary']); self.ax_train_plot.set_facecolor('#f8f9fa')  # 创建matplotlib图形和轴对象，设置背景色
        self.canvas_train_plot = FigureCanvasTkAgg(self.fig_train_plot, master=self.frame_train_plot)  # 创建matplotlib的tkinter画布
        self.canvas_train_plot.get_tk_widget().pack(fill=tk.BOTH, expand=True, padx=2, pady=2)  # 将画布组件打包并填充整个标签框

    def _create_result_panel_compact(self, parent, row, col):
        """
        创建紧凑型预测结果面板
        参数:
            parent: 父容器
            row, col: 在父容器中的行列位置
        功能:
            创建带滚动条的文本框，用于显示预测结果的表格数据
        """
        frame = ttk.LabelFrame(parent, text="📊 预测结果", style='Card.TLabelframe')  # 创建预测结果标签框
        frame.grid(row=row, column=col, sticky="nsew", padx=1, pady=1)  # 将标签框放置在指定位置
        container = tk.Frame(frame, bg=THEME['bg_secondary']); container.pack(fill=tk.BOTH, expand=True, padx=2, pady=2)  # 创建内容容器
        self.text_result = tk.Text(container, font=('Consolas', 13), bg='#f8f9fa', fg=THEME['text_primary'], relief='flat', wrap=tk.NONE)  # 创建文本框，使用等宽字体，不自动换行以保持表格格式
        scroll = ttk.Scrollbar(container, orient="vertical", command=self.text_result.yview); self.text_result.configure(yscrollcommand=scroll.set)  # 创建滚动条
        self.text_result.pack(side=tk.LEFT, fill=tk.BOTH, expand=True); scroll.pack(side=tk.RIGHT, fill=tk.Y)  # 布局文本框和滚动条

    def _create_prediction_plot_panel_compact(self, parent, row, col):
        """
        创建紧凑型预测可视化面板
        参数:
            parent: 父容器
            row, col: 在父容器中的行列位置
        功能:
            创建matplotlib图表，用于可视化预测结果与真实值的对比
        """
        self.frame_plot = ttk.LabelFrame(parent, text="🎯 预测可视化", style='Card.TLabelframe')  # 创建预测图表标签框
        self.frame_plot.grid(row=row, column=col, sticky="nsew", padx=1, pady=1)  # 将标签框放置在指定位置
        self.fig_pred_plot, self.ax_pred_plot = plt.subplots(figsize=(10, 5), facecolor=THEME['bg_secondary']); self.ax_pred_plot.set_facecolor('#f8f9fa')  # 创建matplotlib图形和轴对象
        self.canvas_pred_plot = FigureCanvasTkAgg(self.fig_pred_plot, master=self.frame_plot)  # 创建matplotlib的tkinter画布
        self.canvas_pred_plot.get_tk_widget().pack(fill=tk.BOTH, expand=True, padx=2, pady=2)  # 将画布组件打包并填充整个标签框

    def _create_calendar_panel(self):
        """
        创建日历选择面板
        功能:
            创建日历组件，用于用户选择历史预测的目标日期
            默认隐藏，只有在点击"历史预测"按钮时才显示
        """
        self.frame_calendar = ttk.LabelFrame(self.main_frame, text="📅 历史预测时间选择", style='Card.TLabelframe')  # 创建日历面板标签框
        tip_frame = tk.Frame(self.frame_calendar, bg=THEME['bg_secondary']); tip_frame.pack(fill=tk.X, padx=3, pady=1)  # 创建提示信息框架
        tk.Label(tip_frame, text="💡 请选择日期进行历史预测", font=(THEME['font_family'], 7), bg=THEME['bg_secondary'], fg=THEME['accent_color']).pack(anchor=tk.W)  # 创建使用提示标签
        cal_container = tk.Frame(self.frame_calendar, bg=THEME['bg_secondary']); cal_container.pack(fill=tk.BOTH, expand=True, padx=3, pady=1)  # 创建日历容器框架
        today = datetime.today(); last_month, last_year = (today.month - 1, today.year) if today.month > 1 else (12, today.year - 1)  # 获取当前日期，计算上个月的年月
        self.cal = Calendar(cal_container, selectmode='day', year=last_year, month=last_month, day=1, date_pattern='yyyy-mm-dd',  # 创建日历组件，默认显示上个月
                           font=(THEME['font_family'], 13), background=THEME['bg_secondary'], foreground=THEME['text_primary'],  # 设置日历字体和颜色
                           selectbackground=THEME['accent_color'], selectforeground='white'); self.cal.pack(pady=2)  # 设置选中日期的颜色并打包
        btn_frame = tk.Frame(self.frame_calendar, bg=THEME['bg_secondary']); btn_frame.pack(pady=2)  # 创建按钮框架
        ttk.Button(btn_frame, text="✅ 确定", command=self.on_calendar_ok, style='Success.TButton').pack(); self.frame_calendar.grid_remove()  # 创建确定按钮并隐藏整个日历面板

    def _init_variables(self):
        """
        初始化所有内部变量和状态
        功能:
            设置窗口关闭事件处理、创建预测系统实例、初始化数据变量、设置训练状态等
        """
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)  # 绑定窗口关闭事件处理函数，确保资源正确释放
        self.predictor = WindPowerPredictionSystemMLP()  # 创建风电功率预测系统实例
        self.weather_df = self.df = self.X_seq = self.y = None  # 初始化数据变量：原始气象数据、处理后数据、输入序列、标签数据
        self.scaler_X = self.scaler_y = None  # 初始化数据缩放器：特征缩放器和目标值缩放器
        self.model_trained = False  # 初始化模型训练状态标志
        self.train_thread = None  # 初始化训练线程引用
        self.stop_event = threading.Event()  # 创建线程停止事件，用于控制训练过程的中断

    # ======================== 按钮事件处理模块 ========================
    def hist_test(self):
        """
        历史预测按钮点击事件处理函数
        功能:
            显示日历选择面板，让用户选择要进行历史预测的目标日期
        """
        self.frame_calendar.grid(row=1, column=0, sticky="ew", pady=2)  # 显示日历选择面板，放置在主框架第1行，水平拉伸

    def on_calendar_ok(self):
        """
        日历确定按钮事件处理函数
        功能:
            获取用户选择的日期，隐藏日历面板，启动历史预测流程
        """
        try:
            selected_time = pd.to_datetime(self.cal.get_date())  # 获取用户在日历中选择的日期并转换为pandas时间格式
            self.frame_calendar.grid_remove()  # 隐藏日历选择面板
            self.start_train_and_predict(selected_time, mode="hist")  # 启动训练和历史预测流程
        except:
            messagebox.showerror("错误", "日期格式错误")  # 如果日期解析失败，显示错误对话框

    def now_test(self):
        """
        当前预测按钮事件处理函数
        功能:
            获取当前时间，启动实时预测流程
        """
        now = datetime.now().replace(minute=0, second=0, microsecond=0)  # 获取当前时间并将分秒微秒重置为0，对齐到整点
        if now.minute >= 30: now += timedelta(hours=1)  # 如果当前分钟数大于等于30，则进位到下一小时（处理半点情况）
        self.start_train_and_predict(now, mode="now")  # 启动训练和当前预测流程

    def force_stop(self):
        """
        强制停止按钮事件处理函数
        功能:
            中断正在进行的训练过程，用于紧急停止或用户取消操作
        """
        if self.train_thread and self.train_thread.is_alive():  # 检查训练线程是否存在且正在运行
            self.stop_event.set()  # 设置停止事件标志，通知训练线程停止
            self._safe_gui_update(self._update_log_text, "⚠️ 已请求强行结束训练！\n")  # 在界面日志中显示停止请求信息
        else:
            self._safe_gui_update(self._update_log_text, "⚠️ 当前没有正在训练的任务。\n")  # 如果没有活跃的训练任务，提示用户

    def start_train_and_predict(self, selected_time, mode):
        """
        启动训练和预测的线程管理函数
        参数:
            selected_time: 用户选择的目标时间
            mode: 预测模式（"hist"历史预测 或 "now"当前预测）
        功能:
            清理界面、创建并启动后台训练线程，避免阻塞GUI界面
        """
        for func in [self._clear_log_text, self._clear_result_text, self._clear_plot_frames]: self._safe_gui_update(func)  # 清理所有界面显示内容
        self.stop_event.clear(); self._safe_gui_update(self._update_log_text, "📡 正在获取气象数据...\n")  # 清除停止标志并显示开始信息
        self.train_thread = threading.Thread(target=self.train_and_predict, args=(selected_time, mode), daemon=False)  # 创建训练线程，非守护线程确保完整执行
        self.train_thread.start()  # 启动训练线程

    def train_and_predict(self, selected_time, mode):
        """
        训练与预测的主流程函数（在后台线程中执行）
        参数:
            selected_time: 目标预测时间
            mode: 预测模式
        功能:
            完整的机器学习工作流程：数据获取→特征工程→模型训练→预测展示
        """
        self.stop_event.clear()  # 清除停止事件标志
        try:
            # 数据获取阶段
            fetch_end_time = selected_time + timedelta(days=7) if mode == "hist" else selected_time  # 历史模式需要额外7天数据用于验证，当前模式只需要到当前时间
            self.weather_df = fetch_weather(fetch_end_time, lambda msg: self._safe_gui_update(self._update_log_text, msg))  # 调用气象数据获取函数，传入GUI更新回调

            if isinstance(self.weather_df, str) and self.weather_df.startswith("API_ERROR"):  # 检查是否返回错误信息
                self._safe_gui_update(self._update_log_text, f"❌ 气象数据获取失败: {self.weather_df}\n")
                return
            if hasattr(self.weather_df, 'empty') and self.weather_df.empty:  # 检查数据是否为空
                self._safe_gui_update(self._update_log_text, "❌ 气象数据为空！\n")
                return

            self._safe_gui_update(self._update_log_text, f"✅ 已获取{len(self.weather_df)}条气象数据\n")  # 显示数据获取成功信息

            # 特征工程阶段
            self._safe_gui_update(self._update_log_text, "🔧 正在处理特征...\n")  # 显示特征处理开始信息
            self.df = daily_agg(gen_features(self.weather_df)).dropna(subset=['power'])  # 生成风电特征并聚合为日级数据，删除功率缺失的行
            print("\n【每日气象与风电特征均值表】")  # 在控制台打印表格标题
            print_daily_weather_table(self.df)  # 在控制台打印详细的数据表格

            self._safe_gui_update(self._update_log_text, f"✅ 已聚合为{len(self.df)}天数据\n")  # 显示特征处理完成信息

            # 序列数据准备阶段
            self.X_seq, self.y, self.scaler_X, self.scaler_y = prepare_seq(self.df)  # 准备时间序列数据和标签，获取数据缩放器
            nan_mask = ~np.isnan(self.y).any(axis=1)  # 创建掩码，过滤包含NaN值的样本
            self.X_seq, self.y = self.X_seq[nan_mask], self.y[nan_mask]  # 应用掩码，移除无效样本

            if len(self.X_seq) == 0:  # 检查是否有有效的训练数据
                self._safe_gui_update(self._update_log_text, "❌ 数据量不足，无法训练！\n")
                return

            # 模型训练阶段
            self._safe_gui_update(self._update_log_text, "🧠 正在训练MLP模型...\n")  # 显示模型训练开始信息
            train_idx, val_idx = train_test_split(np.arange(len(self.X_seq)), test_size=0.2, random_state=42)  # 划分训练集和验证集索引，80%训练20%验证
            X_val, y_val = self.X_seq[val_idx], self.y[val_idx]  # 提取验证集数据

            # 构建MLP神经网络模型
            self.predictor.model = keras.Sequential([
                keras.layers.Input(shape=(self.X_seq.shape[1],)),  # 输入层，形状为特征维度
                keras.layers.Dense(128, activation='relu'),  # 第一隐藏层，128个神经元，ReLU激活函数
                keras.layers.Dense(64, activation='relu'),   # 第二隐藏层，64个神经元，ReLU激活函数
                keras.layers.Dense(4, activation='linear')   # 输出层，4个神经元对应4个预测天数，线性激活函数
            ])
            self.predictor.model.compile(optimizer='adam', loss='mse')  # 编译模型，使用Adam优化器和均方误差损失函数

            training_callback = TkinterLoggerCallback(self, X_val=X_val, y_val=y_val,  # 创建自定义训练回调，用于GUI更新
                                                    model=self.predictor.model, stop_event=self.stop_event)

            # 分阶段训练循环（3个阶段，每阶段10轮）
            for bar_idx in range(3):  # 进行3个训练阶段
                with tqdm(total=10, desc=f'训练进度 {bar_idx+1}/3', ncols=50) as pbar:  # 创建进度条
                    class BarCallback(keras.callbacks.Callback):  # 定义进度条更新回调类
                        def on_epoch_end(self, epoch, logs=None): pbar.update(1)  # 每轮结束时更新进度条
                    self.predictor.model.fit(self.X_seq[train_idx], self.y[train_idx], validation_data=(X_val, y_val),  # 训练模型
                                           epochs=bar_idx*10+10, initial_epoch=bar_idx*10, batch_size=8, verbose=0,  # 设置轮次、批次大小等参数
                                           callbacks=[training_callback, BarCallback()])  # 使用自定义回调

            self.model_trained = True  # 设置模型训练完成标志
            self._safe_gui_update(self._update_log_text, "✅ 训练完成！\n")  # 显示训练完成信息
            self.predict_at_time(selected_time, mode)  # 调用预测函数

        except Exception as e:  # 捕获所有异常
            import traceback  # 导入异常追踪模块
            self._safe_gui_update(self._update_log_text, f"❌ 异常: {e}\n{traceback.format_exc()}\n")  # 显示详细的异常信息
    # ======================== 预测与结果展示模块 ========================
    def predict_at_time(self, user_time, mode):
        """
        在指定时间点进行功率预测的核心函数
        参数:
            user_time: 用户指定的预测基准时间
            mode: 预测模式（"hist"或"now"）
        功能:
            使用训练好的模型对指定时间点进行多天预测，并展示结果
        """
        df, input_days, pred_days = self.df, 7, [1,2,3,7]  # 获取数据、设置输入天数和预测天数列表
        user_day = user_time.date()  # 提取用户指定时间的日期部分

        # 查找目标日期在数据中的索引位置
        idx = df.index[df['timestamp'].dt.date == user_day]  # 在数据中查找匹配的日期索引
        if len(idx) == 0:  # 如果没有找到精确匹配的日期
            all_days = df['timestamp'].dt.date  # 获取所有可用日期
            nearest_idx = pd.Series([abs((d - user_day).days) for d in all_days]).idxmin()  # 找到最接近的日期索引
            nearest_time = df['timestamp'].iloc[nearest_idx]  # 获取最接近的时间
            self._safe_gui_update(self._update_result_text, f"未找到该日期数据，已对齐到：{nearest_time.date()}\n")  # 提示用户已对齐到最近日期
            idx = nearest_idx  # 使用最接近的日期索引
        else:
            idx = idx[0]  # 使用找到的精确匹配索引

        if idx < input_days - 1:  # 检查是否有足够的历史数据用于预测
            self._safe_gui_update(self._update_result_text, f"数据不足{input_days}天，无法预测\n")  # 提示数据不足
            return

        # 执行预测计算
        seq_features = ['wind_speed','wind_direction','turbulence_intensity','temperature','pressure','humidity','altitude','hour','month','air_density','power_change_rate']  # 定义用于预测的特征列表
        X_input = self.scaler_X.transform(df.iloc[idx-input_days+1:idx+1][seq_features]).flatten().reshape(1, -1)  # 提取输入序列特征并标准化，展平为模型输入格式
        y_pred = self.scaler_y.inverse_transform(self.predictor.model.predict(X_input)[0].reshape(-1,1)).flatten()  # 使用模型预测并反标准化得到实际功率值

        # 计算预测置信区间（简单的±15%区间）
        ci_lower = y_pred * 0.85  # 置信区间下界：预测值的85%
        ci_upper = y_pred * 1.15  # 置信区间上界：预测值的115%

        self._safe_gui_update(self._clear_result_text)  # 清空结果显示区域

        # 根据预测模式展示不同格式的结果
        if mode == "hist":  # 历史预测模式：可以对比真实值
            y_true = [df['power'].iloc[idx + d] if idx + d < len(df) else None for d in pred_days]  # 提取对应预测天数的真实功率值
            self._display_hist_results(pred_days, y_true, y_pred, ci_lower, ci_upper)  # 显示包含真实值对比的历史预测结果
        else:  # 当前预测模式：只显示预测值
            self._display_now_results(pred_days, y_pred, ci_lower, ci_upper)  # 显示当前预测结果
            y_true = [None] * len(pred_days)  # 当前预测没有真实值，设置为None

        self.root.after(0, self._update_plot, y_true, y_pred, pred_days)  # 异步更新预测结果图表

    def _display_hist_results(self, pred_days, y_true, y_pred, ci_lower, ci_upper):
        """
        显示历史预测结果的表格格式函数
        参数:
            pred_days: 预测天数列表
            y_true: 真实功率值列表
            y_pred: 预测功率值列表
            ci_lower, ci_upper: 置信区间下界和上界
        功能:
            在GUI和控制台同时显示包含真实值对比的详细预测结果表格
        """
        header = "天数后 | 真实值(MW) | 预测值(MW) | 置信区间下界 | 置信区间上界 | 误差(MW)\n"  # 定义表格标题行
        self._safe_gui_update(self._update_result_text, header + "-"*75 + "\n")  # 在GUI中显示表头和分隔线
        print("\n================ 预测结果表格 ================\n天数后 | 真实值(MW) | 预测值(MW) | 置信区间下界 | 置信区间上界 | 误差(MW)\n" + "-"*75)  # 在控制台打印表头
        for i, d in enumerate(pred_days):  # 遍历每个预测天数
            if y_true[i] is not None:  # 如果有真实值数据
                error = abs(y_true[i] - y_pred[i])  # 计算预测误差（绝对值）
                line = f"{d:2d}天后 | {y_true[i]:9.3f} | {y_pred[i]:9.3f} | {ci_lower[i]:11.3f} | {ci_upper[i]:11.3f} | {error:7.3f}"  # 格式化完整的结果行
            else:  # 如果没有真实值数据
                line = f"{d:2d}天后 |   无数据   | {y_pred[i]:9.3f} | {ci_lower[i]:11.3f} | {ci_upper[i]:11.3f} |   N/A"  # 格式化无真实值的结果行
            self._safe_gui_update(self._update_result_text, line + "\n"); print(line)  # 同时在GUI和控制台显示结果行
        print("="*50)  # 在控制台打印结束分隔线

    def _display_now_results(self, pred_days, y_pred, ci_lower, ci_upper):
        """
        显示当前预测结果的表格格式函数
        参数:
            pred_days: 预测天数列表
            y_pred: 预测功率值列表
            ci_lower, ci_upper: 置信区间下界和上界
        功能:
            在GUI和控制台同时显示当前预测结果表格（不包含真实值对比）
        """
        header = "天数后 | 预测值(MW) | 置信区间下界 | 置信区间上界\n"  # 定义表格标题行（不包含真实值和误差列）
        self._safe_gui_update(self._update_result_text, header + "-"*55 + "\n")  # 在GUI中显示表头和分隔线
        print("\n================ 预测结果表格 ================\n天数后 | 预测值(MW) | 置信区间下界 | 置信区间上界\n" + "-"*55)  # 在控制台打印表头
        for i, d in enumerate(pred_days):  # 遍历每个预测天数
            line = f"{d:2d}天后 | {y_pred[i]:9.3f} | {ci_lower[i]:11.3f} | {ci_upper[i]:11.3f}"  # 格式化预测结果行
            self._safe_gui_update(self._update_result_text, line + "\n"); print(line)  # 同时在GUI和控制台显示结果行
        print("="*40)  # 在控制台打印结束分隔线
    # ======================== 可视化模块 ========================
    def _update_plot(self, y_true, y_pred, pred_days):
        """
        更新预测结果对比图表
        参数:
            y_true: 真实功率值列表（历史预测模式有值，当前预测模式为None）
            y_pred: 预测功率值列表
            pred_days: 预测天数列表
        功能:
            在右下角图表中绘制真实值与预测值的对比曲线和散点图
        """
        self.ax_pred_plot.clear()  # 清空图表内容
        x_true, y_true_filtered = zip(*[(i+1, v) for i, v in enumerate(y_true) if v is not None]) if any(v is not None for v in y_true) else ([], [])  # 提取有效的真实值数据点和对应的x坐标
        x_pred = list(range(1, len(pred_days)+1))  # 创建预测值的x坐标（1到预测天数）
        if x_true:  # 如果有真实值数据
            self.ax_pred_plot.scatter(x_true, y_true_filtered, color=THEME['accent_color'], label='真实值', s=30, alpha=0.8)  # 绘制真实值散点图
            self.ax_pred_plot.plot(x_true, y_true_filtered, color=THEME['accent_color'], alpha=0.5, linewidth=1.5)  # 绘制真实值连线
        self.ax_pred_plot.scatter(x_pred, y_pred, color=THEME['success_color'], label='预测值', s=30, alpha=0.8)  # 绘制预测值散点图
        self.ax_pred_plot.plot(x_pred, y_pred, color=THEME['success_color'], alpha=0.5, linewidth=1.5)  # 绘制预测值连线
        self.ax_pred_plot.set_xlabel('天数', fontsize=13, color=THEME['text_primary'])  # 设置x轴标签
        self.ax_pred_plot.set_ylabel('功率(MW)', fontsize=13, color=THEME['text_primary'])  # 设置y轴标签
        self.ax_pred_plot.set_title('预测对比', fontsize=15, color=THEME['text_primary'])  # 设置图表标题
        self.ax_pred_plot.legend(fontsize=13); self.ax_pred_plot.grid(True, alpha=0.3); self.ax_pred_plot.tick_params(labelsize=13)  # 设置图例、网格和刻度标签
        self.canvas_pred_plot.draw()  # 重绘图表

    def _update_training_plot(self, losses, val_losses):
        """
        更新训练损失曲线图表
        参数:
            losses: 训练损失值列表
            val_losses: 验证损失值列表
        功能:
            在右上角图表中实时显示训练过程中的损失变化曲线
        """
        self.ax_train_plot.clear()  # 清空图表内容
        epochs = range(1, len(losses) + 1)  # 创建轮次坐标（从1开始）
        self.ax_train_plot.plot(epochs, losses, label='训练损失', color=THEME['accent_color'], linewidth=1.5)  # 绘制训练损失曲线
        self.ax_train_plot.plot(epochs, val_losses, label='验证损失', color=THEME['warning_color'], linewidth=1.5)  # 绘制验证损失曲线
        self.ax_train_plot.set_title('训练损失', fontsize=15, color=THEME['text_primary'])  # 设置图表标题
        self.ax_train_plot.set_xlabel('轮次', fontsize=13, color=THEME['text_primary'])  # 设置x轴标签
        self.ax_train_plot.set_ylabel('损失', fontsize=13, color=THEME['text_primary'])  # 设置y轴标签
        self.ax_train_plot.legend(fontsize=13); self.ax_train_plot.grid(True, alpha=0.3); self.ax_train_plot.tick_params(labelsize=13)  # 设置图例、网格和刻度标签
        self.canvas_train_plot.draw()  # 重绘图表

    # ======================== GUI辅助方法模块 ========================
    def _safe_gui_update(self, func, *args, **kwargs):
        """
        线程安全的GUI更新函数
        参数:
            func: 要在主线程中执行的GUI更新函数
            *args, **kwargs: 传递给函数的参数
        功能:
            将GUI更新操作调度到主线程执行，避免多线程访问GUI组件的问题
        """
        self.root.after(0, func, *args, **kwargs)  # 使用tkinter的after方法将操作调度到主线程

    def _update_log_text(self, text):
        """
        更新训练日志文本框内容
        参数:
            text: 要添加的日志文本
        功能:
            在日志文本框末尾追加新内容并自动滚动到底部
        """
        self.text_log.insert(tk.END, text); self.text_log.see(tk.END)  # 在文本框末尾插入文本并滚动到底部

    def _clear_log_text(self):
        """清空训练日志文本框的所有内容"""
        self.text_log.delete(1.0, tk.END)  # 删除文本框中从第1行第0列到末尾的所有内容

    def _update_result_text(self, text):
        """
        更新预测结果文本框内容
        参数:
            text: 要添加的结果文本
        功能:
            在结果文本框末尾追加新内容
        """
        self.text_result.insert(tk.END, text)  # 在结果文本框末尾插入文本

    def _clear_result_text(self):
        """清空预测结果文本框的所有内容"""
        self.text_result.delete(1.0, tk.END)  # 删除结果文本框中的所有内容

    def _clear_plot_frames(self):
        """
        清空所有图表并重置为初始状态
        功能:
            清除图表内容，重新设置标题和样式，为新的绘图做准备
        """
        for ax, title in [(self.ax_pred_plot, '预测结果'), (self.ax_train_plot, '训练损失')]:  # 遍历预测图表和训练图表
            ax.clear(); ax.set_title(title, fontsize=14, color=THEME['text_primary'], pad=20)  # 清空图表内容并重新设置标题
            ax.set_facecolor('#f8f9fa'); ax.grid(True, alpha=0.3)  # 设置图表背景色为浅灰色，启用半透明网格
        self.canvas_pred_plot.draw(); self.canvas_train_plot.draw()  # 重绘两个图表画布

    # ======================== 资源释放模块 ========================
    def on_closing(self):
        """
        窗口关闭事件处理函数
        功能:
            安全关闭应用程序，正确释放所有资源（线程、图表、内存等）
        """
        try:  # 尝试安全停止训练线程
            if self.train_thread and self.train_thread.is_alive():  # 检查训练线程是否存在且正在运行
                self.stop_event.set(); time.sleep(0.1); self.train_thread.join(timeout=5.0)  # 设置停止事件，短暂等待，然后等待线程结束（最多5秒）
        except Exception as e:
            print(f"线程处理异常: {e}")  # 如果线程处理出现异常，打印错误信息
        try:  # 尝试释放matplotlib相关资源
            plt.close('all')  # 关闭所有matplotlib图形窗口，释放内存
            for attr in ['canvas_pred_plot', 'fig_pred_plot', 'ax_pred_plot', 'canvas_train_plot', 'fig_train_plot', 'ax_train_plot']:
                setattr(self, attr, None)  # 将所有图表对象引用设置为None，帮助垃圾回收
        except Exception as e:
            print(f"资源释放异常: {e}")  # 如果资源释放出现异常，打印错误信息
        self.root.destroy()  # 销毁tkinter主窗口，完全退出应用程序

# ======================== 程序入口模块 ========================
if __name__ == "__main__":  # 检查是否作为主程序直接运行（而不是被导入）
    root = tk.Tk()  # 创建tkinter主窗口对象，这是GUI应用的根容器
    app = WindPowerGUI(root)  # 创建风电功率预测GUI应用实例，传入主窗口对象
    root.mainloop()  # 启动tkinter的主事件循环，开始运行GUI应用程序，等待用户交互



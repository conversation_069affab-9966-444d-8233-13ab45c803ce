import requests
import pandas as pd
import numpy as np
import tensorflow as tf
from tensorflow import keras
from sklearn.preprocessing import MinMaxScaler, StandardScaler
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score
from sklearn.model_selection import train_test_split
from datetime import datetime, timedelta
import os
import matplotlib.pyplot as plt
import threading
import tkinter as tk
from tkinter import messagebox
from tkinter import ttk
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
import time
# 新增日历
try:
    from tkcalendar import Calendar
except ImportError:
    raise ImportError("请先安装 tkcalendar：pip install tkcalendar")
# TCN支持
# 需要先安装: pip install keras-tcn
from tcn import TCN

plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial', 'Microsoft YaHei', 'sans-serif']  # 依次尝试这些字体
plt.rcParams['axes.unicode_minus'] = False  # 正常显示负号

def fetch_current_weather_data(dt):
    """获取指定时间点的气象数据（只取该小时）"""
    url = "https://archive-api.open-meteo.com/v1/archive"
    date_str = dt.strftime('%Y-%m-%d')
    hour = dt.hour
    params = {
        "latitude": 30.67,
        "longitude": 104.06,
        "start_date": date_str,
        "end_date": date_str,
        "hourly": "temperature_2m,relative_humidity_2m,pressure_msl",
        "timezone": "Asia/Shanghai"
    }
    response = requests.get(url, params=params)
    data = response.json()
    df = pd.DataFrame({
        "timestamp": pd.to_datetime(data["hourly"]["time"]),
        "temperature": data["hourly"]["temperature_2m"],
        "humidity": data["hourly"]["relative_humidity_2m"],
        "pressure": data["hourly"]["pressure_msl"]
    })
    df = df[df['timestamp'].dt.hour == hour]
    df["hour"] = df["timestamp"].dt.hour
    df["month"] = df["timestamp"].dt.month
    df["altitude"] = 850
    return df.reset_index(drop=True)

def fetch_month_weather_data(year, month):
    """获取指定年月的3天气象数据（测试用）"""
    from calendar import monthrange
    start_date = f"{year}-{month:02d}-01"
    end_date = f"{year}-{month:02d}-03"  # 只取3天
    url = "https://archive-api.open-meteo.com/v1/archive"
    params = {
        "latitude": 30.67,
        "longitude": 104.06,
        "start_date": start_date,
        "end_date": end_date,
        "hourly": "temperature_2m,relative_humidity_2m,pressure_msl",
        "timezone": "Asia/Shanghai"
    }
    try:
        response = requests.get(url, params=params, timeout=10)
        response.raise_for_status()
        data = response.json()
        if "hourly" not in data:
            return f"API_ERROR: 响应内容无hourly字段，原始响应: {data}"
    except Exception as e:
        return f"API_ERROR: {e}"
    df = pd.DataFrame({
        "timestamp": pd.to_datetime(data["hourly"]["time"]),
        "temperature": data["hourly"]["temperature_2m"],
        "humidity": data["hourly"]["relative_humidity_2m"],
        "pressure": data["hourly"]["pressure_msl"]
    })
    df["hour"] = df["timestamp"].dt.hour
    df["month"] = df["timestamp"].dt.month
    df["altitude"] = 850
    return df.reset_index(drop=True)

def fetch_past_30days_weather_data(end_dt):
    """获取以end_dt为原点，过去30天的气象数据"""
    from calendar import monthrange
    start_dt = end_dt - timedelta(days=29)  # 获取30天数据
    start_date = start_dt.strftime('%Y-%m-%d')
    end_date = end_dt.strftime('%Y-%m-%d')
    url = "https://archive-api.open-meteo.com/v1/archive"
    params = {
        "latitude": 30.67,
        "longitude": 104.06,
        "start_date": start_date,
        "end_date": end_date,
        "hourly": "temperature_2m,relative_humidity_2m,pressure_msl",
        "timezone": "Asia/Shanghai"
    }
    print(f"DEBUG: params dictionary before request: {params}") # NEW DEBUG LINE
    print(f"DEBUG: API请求日期范围: {start_date} 到 {end_date}") # Debug log
    try:
        response = requests.get(url, params=params, timeout=10)
        response.raise_for_status()
        data = response.json()
        if "hourly" not in data:
            print(f"DEBUG: API响应无hourly字段: {data}") # Debug log
            return f"API_ERROR: 响应内容无hourly字段，原始响应: {data}"
        print(f'DEBUG: API返回原始小时数据条数: {len(data.get("hourly", {}).get("time", []))}') # Debug log
    except Exception as e:
        print(f"DEBUG: API请求失败: {e}") # Debug log
        return f"API_ERROR: {e}"
    df = pd.DataFrame({
        "timestamp": pd.to_datetime(data["hourly"]["time"]),
        "temperature": data["hourly"]["temperature_2m"],
        "humidity": data["hourly"]["relative_humidity_2m"],
        "pressure": data["hourly"]["pressure_msl"]
    })
    df["hour"] = df["timestamp"].dt.hour
    df["month"] = df["timestamp"].dt.month
    df["altitude"] = 850
    return df.reset_index(drop=True)

def fetch_past_7days_weather_data(end_dt):
    start_dt = end_dt - timedelta(days=7)
    start_date = start_dt.strftime('%Y-%m-%d')
    end_date = end_dt.strftime('%Y-%m-%d')
    url = "https://archive-api.open-meteo.com/v1/archive"
    params = {
        "latitude": 30.67,
        "longitude": 104.06,
        "start_date": start_date,
        "end_date": end_date,
        "hourly": "temperature_2m,relative_humidity_2m,pressure_msl",
        "timezone": "Asia/Shanghai"
    }
    try:
        response = requests.get(url, params=params, timeout=10)
        response.raise_for_status()
        data = response.json()
        if "hourly" not in data:
            return f"API_ERROR: 响应内容无hourly字段，原始响应: {data}"
    except Exception as e:
        return f"API_ERROR: {e}"
    df = pd.DataFrame({
        "timestamp": pd.to_datetime(data["hourly"]["time"]),
        "temperature": data["hourly"]["temperature_2m"],
        "humidity": data["hourly"]["relative_humidity_2m"],
        "pressure": data["hourly"]["pressure_msl"]
    })
    df["hour"] = df["timestamp"].dt.hour
    df["month"] = df["timestamp"].dt.month
    df["day"] = df["timestamp"].dt.date
    df["altitude"] = 850
    return df.reset_index(drop=True)

def daily_aggregate(df):
    # 先生成风电功率等特征
    predictor = WindPowerPredictionSystem()
    df = predictor.generate_wind_power_data(df)
    # 按天聚合
    agg_dict = {
        'wind_speed': 'mean',
        'wind_direction': 'mean',
        'turbulence_intensity': 'mean',
        'temperature': 'mean',
        'pressure': 'mean',
        'humidity': 'mean',
        'altitude': 'mean',
        'hour': 'mean',
        'month': 'mean',
        'power': 'mean',
        'air_density': 'mean',
        'power_change_rate': 'mean'
    }
    daily_df = df.groupby('day').agg(agg_dict).reset_index()
    daily_df['timestamp'] = pd.to_datetime(daily_df['day'])
    return daily_df

def prepare_sequences_daily(df, input_days=7, pred_days=[1,2,3,7]):
    seq_features = ['wind_speed', 'wind_direction', 'turbulence_intensity', 'temperature', 'pressure', 'humidity', 'altitude', 'hour', 'month', 'air_density', 'power_change_rate']
    scaler_X = StandardScaler()
    scaler_y = MinMaxScaler()
    X = scaler_X.fit_transform(df[seq_features])
    y = scaler_y.fit_transform(df[['power']])
    X_seq, y_seq = [], []
    for i in range(len(df) - input_days - max(pred_days) + 1):
        X_seq.append(X[i:i+input_days])
        y_targets = [y[i+input_days+d-1][0] for d in pred_days]
        y_seq.append(y_targets)
    return np.array(X_seq), np.array(y_seq), scaler_X, scaler_y

class WindPowerPredictionSystem:
    def __init__(self, seq_length=12, prediction_horizon=4):
        self.seq_length = seq_length
        self.prediction_horizon = prediction_horizon
        self.wind_scaler = MinMaxScaler()
        self.env_scaler = StandardScaler()
        self.power_scaler = MinMaxScaler()
        self.model = None

    def create_hybrid_model(self, seq_features, env_features):
        # LSTM分支
        lstm_input = keras.layers.Input(shape=(self.seq_length, seq_features), name='lstm_input')
        
        # 双向LSTM层
        lstm_layer1 = keras.layers.Bidirectional(keras.layers.LSTM(128, return_sequences=True))(lstm_input)
        lstm_layer1 = keras.layers.Dropout(0.2)(lstm_layer1)
        
        # 注意力机制
        attention = keras.layers.MultiHeadAttention(num_heads=4, key_dim=32)(lstm_layer1, lstm_layer1)
        attention = keras.layers.LayerNormalization()(attention + lstm_layer1)
        
        lstm_layer2 = keras.layers.Bidirectional(keras.layers.LSTM(64, return_sequences=False))(attention)
        lstm_layer2 = keras.layers.Dropout(0.2)(lstm_layer2)
        lstm_output = keras.layers.Dense(32, activation='relu')(lstm_layer2)

        # MLP分支
        mlp_input = keras.layers.Input(shape=(env_features,), name='mlp_input')
        
        # 深度MLP
        mlp_layer1 = keras.layers.Dense(128, activation='relu')(mlp_input)
        mlp_layer1 = keras.layers.BatchNormalization()(mlp_layer1)
        mlp_layer1 = keras.layers.Dropout(0.3)(mlp_layer1)
        
        mlp_layer2 = keras.layers.Dense(64, activation='relu')(mlp_layer1)
        mlp_layer2 = keras.layers.BatchNormalization()(mlp_layer2)
        mlp_layer2 = keras.layers.Dropout(0.2)(mlp_layer2)
        
        mlp_layer3 = keras.layers.Dense(32, activation='relu')(mlp_layer2)
        mlp_layer3 = keras.layers.BatchNormalization()(mlp_layer3)
        mlp_layer3 = keras.layers.Dropout(0.1)(mlp_layer3)

        # 特征融合
        combined = keras.layers.concatenate([lstm_output, mlp_layer3])
        
        # 融合层
        fusion_layer = keras.layers.Dense(128, activation='relu')(combined)
        fusion_layer = keras.layers.BatchNormalization()(fusion_layer)
        fusion_layer = keras.layers.Dropout(0.2)(fusion_layer)
        
        fusion_layer = keras.layers.Dense(64, activation='relu')(fusion_layer)
        fusion_layer = keras.layers.BatchNormalization()(fusion_layer)
        fusion_layer = keras.layers.Dropout(0.2)(fusion_layer)

        # 输出层
        output = keras.layers.Dense(self.prediction_horizon, activation='linear', name='power_output')(fusion_layer)
        
        # 构建模型
        model = keras.Model(inputs=[lstm_input, mlp_input], outputs=output)
        
        # 使用AdamW优化器
        optimizer = keras.optimizers.AdamW(
            learning_rate=0.001,
            weight_decay=0.001
        )
        
        # 编译模型
        model.compile(
            optimizer=optimizer,
            loss='huber',
            metrics=['mae', 'mse']
        )
        
        return model

    def generate_wind_power_data(self, weather_df):
        data = []
        n_samples = len(weather_df)

        for i in range(n_samples):
            timestamp = weather_df["timestamp"].iloc[i]
            temperature = weather_df["temperature"].iloc[i]
            pressure = weather_df["pressure"].iloc[i]
            humidity = weather_df["humidity"].iloc[i]
            hour = weather_df["hour"].iloc[i]
            month = weather_df["month"].iloc[i]
            altitude = weather_df["altitude"].iloc[i]

            # 改进风速生成逻辑，考虑昼夜变化和季节影响
            base_wind_speed = 8 + 3 * np.sin(2 * np.pi * i / 100)
            diurnal_factor = 1 + 0.3 * np.sin(2 * np.pi * hour / 24)
            seasonal_factor = 1 + 0.2 * np.sin(2 * np.pi * (month - 1) / 12)
            wind_speed = max(0, base_wind_speed * diurnal_factor * seasonal_factor + np.random.normal(0, 1.5))
            
            # 改进风向生成，考虑季节变化
            base_direction = 240 + 30 * np.sin(2 * np.pi * (month - 1) / 12)
            wind_direction = (base_direction + np.random.normal(0, 30)) % 360
            
            # 改进湍流强度计算
            turbulence_intensity = max(0.05, 0.25 - 0.015 * wind_speed + np.random.normal(0, 0.02))
            
            # 计算空气密度
            air_density = (pressure / 1013) * (288 / (temperature + 273))
            
            # 计算功率
            if wind_speed < 3:
                power = 0
            elif wind_speed < 12:
                power = 3.5 * (wind_speed - 3) ** 2 / 81
            elif wind_speed < 25:
                power = 3.5
            else:
                power = 0

            # 考虑空气密度影响
            power *= air_density
            
            # 考虑湍流影响
            power *= (1 - 0.1 * turbulence_intensity)
            
            # 添加随机噪声
            power += np.random.normal(0, 0.1)
            power = max(0, power)

            # 计算功率变化率
            power_change_rate = 0
            if i > 0:
                prev_power = data[-1]["power"]
                power_change_rate = (power - prev_power) / max(prev_power, 0.1)

            data.append({
                "timestamp": timestamp,
                "wind_speed": wind_speed,
                "wind_direction": wind_direction,
                "turbulence_intensity": turbulence_intensity,
                "temperature": temperature,
                "pressure": pressure,
                "humidity": humidity,
                "altitude": altitude,
                "hour": hour,
                "month": month,
                "power": power,
                "power_change_rate": power_change_rate,
                "air_density": air_density,
                "day": timestamp.date()  # 新增 day 字段，保证后续 groupby 正常
            })

        return pd.DataFrame(data)

    def prepare_sequences(self, df):
        seq_features = ['wind_speed', 'wind_direction', 'turbulence_intensity']
        env_features = ['temperature', 'pressure', 'humidity', 'altitude', 'hour', 'month']
        df_seq_scaled = df[seq_features].copy()
        df_seq_scaled[seq_features] = self.wind_scaler.fit_transform(df[seq_features])
        df_env_scaled = df[env_features].copy()
        df_env_scaled[env_features] = self.env_scaler.fit_transform(df[env_features])
        power_scaled = self.power_scaler.fit_transform(df[['power']])
        X_seq, X_env, y = [], [], []
        for i in range(len(df) - self.seq_length - self.prediction_horizon + 1):
            seq_data = df_seq_scaled.iloc[i:i+self.seq_length][seq_features].values
            X_seq.append(seq_data)
            env_data = df_env_scaled.iloc[i+self.seq_length-1][env_features].values
            X_env.append(env_data)
            target_data = power_scaled[i+self.seq_length:i+self.seq_length+self.prediction_horizon].flatten()
            y.append(target_data)
        return np.array(X_seq), np.array(X_env), np.array(y)

    def train_model(self, X_seq, X_env, y, extra_callbacks=[]):
        import os
        os.makedirs('logs', exist_ok=True)
        indices = np.arange(len(X_seq))
        train_idx, test_idx = train_test_split(indices, test_size=0.2, random_state=42)
        X_seq_train, X_seq_test = X_seq[train_idx], X_seq[test_idx]
        X_env_train, X_env_test = X_env[train_idx], X_env[test_idx]
        y_train, y_test = y[train_idx], y[test_idx]

        # 创建验证集
        train_idx, val_idx = train_test_split(train_idx, test_size=0.2, random_state=42)
        X_seq_train, X_seq_val = X_seq[train_idx], X_seq[val_idx]
        X_env_train, X_env_val = X_env[train_idx], X_env[val_idx]
        y_train, y_val = y[train_idx], y[val_idx]

        self.model = self.create_hybrid_model(X_seq.shape[2], X_env.shape[1])

        # 定义回调函数
        callbacks = [
            # 早停
            keras.callbacks.EarlyStopping(
                monitor='val_loss',
                patience=15,
                restore_best_weights=True,
                verbose=1
            ),
            # 学习率调度
            keras.callbacks.ReduceLROnPlateau(
                monitor='val_loss',
                factor=0.5,
                patience=8,
                min_lr=1e-6,
                verbose=1
            ),
            # 模型检查点
            keras.callbacks.ModelCheckpoint(
                'best_model.h5',
                monitor='val_loss',
                save_best_only=True,
                verbose=1
            ),
            # TensorBoard
            keras.callbacks.TensorBoard(
                log_dir='./logs',
                histogram_freq=1
            )
        ]

        # 添加额外回调函数
        callbacks.extend(extra_callbacks)

        # 训练模型
        history = self.model.fit(
            [X_seq_train, X_env_train], y_train,
            validation_data=([X_seq_val, X_env_val], y_val),
            epochs=30,  # 训练轮数改为30
            batch_size=32,
            verbose=1,
            callbacks=callbacks
        )

        return history, (X_seq_test, X_env_test, y_test)

    def evaluate_model(self, X_seq_test, X_env_test, y_test):
        y_pred = self.model.predict([X_seq_test, X_env_test])
        y_test_original = self.power_scaler.inverse_transform(y_test)
        y_pred_original = self.power_scaler.inverse_transform(y_pred)
        metrics = {}
        for step in range(self.prediction_horizon):
            metrics[f"Step_{step+1}"] = {
                'RMSE': np.sqrt(mean_squared_error(y_test_original[:, step], y_pred_original[:, step])),
                'MAE': mean_absolute_error(y_test_original[:, step], y_pred_original[:, step]),
                'R2': r2_score(y_test_original[:, step], y_pred_original[:, step])
            }
        return metrics, y_test_original, y_pred_original

class TkinterLoggerCallback(keras.callbacks.Callback):
    def __init__(self, gui_instance, X_val=None, y_val=None, model=None, stop_event=None):
        super().__init__()
        self.X_val = X_val
        self.y_val = y_val
        self._model_ref = model  # 避免与Callback的model属性冲突
        self.stop_event = stop_event
        self.gui_instance = gui_instance  # 引用GUI实例
        self.losses = []
        self.val_losses = []

    def on_epoch_end(self, epoch, logs=None):
        if logs is None: logs = {}
        msg = f"第{epoch+1}轮 - 损失: {logs.get('loss', float('nan')):.4f}, 验证损失: {logs.get('val_loss', float('nan')):.4f}\n"
        self.gui_instance._safe_gui_update(self.gui_instance._update_log_text, msg)
        self.losses.append(logs.get('loss'))
        self.val_losses.append(logs.get('val_loss'))
        self.gui_instance.root.after(0, self.gui_instance._update_training_plot, self.losses, self.val_losses)
        if self.X_val is not None and self.y_val is not None and self._model_ref is not None:
            try:
                x_seq = self.X_val[0:1]
                y_true = self.y_val[0]
                y_pred = self._model_ref.predict(x_seq, verbose=0)[0]
                y_true_str = ', '.join([f"{v:.3f}" for v in y_true])
                y_pred_str = ', '.join([f"{v:.3f}" for v in y_pred])
                self.gui_instance._safe_gui_update(self.gui_instance._update_log_text, f"  真实功率: [{y_true_str}]\n  预测功率: [{y_pred_str}]\n")
            except Exception as e:
                self.gui_instance._safe_gui_update(self.gui_instance._update_log_text, f"  [预测显示异常: {e}]\n")
        if self.stop_event and self.stop_event.is_set():
            self._model_ref.stop_training = True

class WindPowerGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("风电功率预测交互界面（优化版）")
        self.root.geometry("1000x900")
        self.root.resizable(True, True)

        # 新增主Canvas和Scrollbar，实现全局滚动
        main_canvas = tk.Canvas(self.root)
        main_scrollbar = ttk.Scrollbar(self.root, orient="vertical", command=main_canvas.yview)
        main_canvas.configure(yscrollcommand=main_scrollbar.set)
        main_canvas.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        main_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        main_frame = tk.Frame(main_canvas)
        main_canvas.create_window((0, 0), window=main_frame, anchor="nw")

        def _on_main_frame_configure(event):
            main_canvas.configure(scrollregion=main_canvas.bbox("all"))
        main_frame.bind("<Configure>", _on_main_frame_configure)

        def _on_main_mousewheel(event):
            main_canvas.yview_scroll(int(-1*(event.delta/120)), "units")
        main_canvas.bind_all("<MouseWheel>", _on_main_mousewheel)

        # ====== 顶部标题 ======
        title = ttk.Label(main_frame, text="风电功率预测系统", font=("Arial", 20, "bold"))
        title.pack(pady=10)
        # ====== 按钮区 ======
        frame_btn = ttk.Frame(main_frame)
        frame_btn.pack(pady=5)
        btn_hist = ttk.Button(frame_btn, text="历史预测", command=self.hist_test)
        btn_hist.pack(side=tk.LEFT, padx=15)
        btn_now = ttk.Button(frame_btn, text="当前预测", command=self.now_test)
        btn_now.pack(side=tk.LEFT, padx=15)
        btn_stop = ttk.Button(frame_btn, text="强行结束", command=self.force_stop, style="Danger.TButton")
        btn_stop.pack(side=tk.LEFT, padx=15)
        # ====== 历史预测区 ======
        self.frame_calendar = ttk.LabelFrame(main_frame, text="历史预测时间选择")
        self.label_tip = ttk.Label(self.frame_calendar, text="请选择日期进行历史预测", foreground="blue")
        self.label_tip.pack(anchor=tk.W, padx=5, pady=2)
        # 新增Canvas和Scrollbar（只包裹日历控件本身）
        calendar_canvas = tk.Canvas(self.frame_calendar, height=250)
        calendar_scrollbar = ttk.Scrollbar(self.frame_calendar, orient="vertical", command=calendar_canvas.yview)
        calendar_canvas.configure(yscrollcommand=calendar_scrollbar.set)
        calendar_canvas.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        calendar_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        calendar_inner = tk.Frame(calendar_canvas)
        calendar_canvas.create_window((0, 0), window=calendar_inner, anchor="nw")
        def _on_frame_configure(event):
            calendar_canvas.configure(scrollregion=calendar_canvas.bbox("all"))
        calendar_inner.bind("<Configure>", _on_frame_configure)
        today = datetime.today()
        last_month = today.month - 1 if today.month > 1 else 12
        last_year = today.year if today.month > 1 else today.year - 1
        self.cal = Calendar(
            calendar_inner,
            selectmode='day',
            year=last_year,
            month=last_month,
            day=1,
            date_pattern='yyyy-mm-dd',
            font=("Arial", 12),
            background="white",
            foreground="black",
            selectbackground="blue",
            selectforeground="white"
        )
        self.cal.pack(padx=5, pady=5)
        self.btn_calendar_ok = ttk.Button(self.frame_calendar, text="确定", command=self.on_calendar_ok)
        self.btn_calendar_ok.pack(side=tk.LEFT, padx=5)
        self.frame_calendar.pack_forget()
        # 鼠标滚轮支持（只针对日历区）
        def _on_mousewheel(event):
            calendar_canvas.yview_scroll(int(-1*(event.delta/120)), "units")
        calendar_canvas.bind_all("<MouseWheel>", _on_mousewheel)
        # ====== 日志区 ======
        frame_log = ttk.LabelFrame(main_frame, text="训练过程日志")
        frame_log.pack(pady=8, fill=tk.BOTH, expand=False)
        self.text_log = tk.Text(frame_log, height=12, width=110, font=("Consolas", 11))
        self.text_log.pack()
        # ====== 训练过程图表区 ======
        self.frame_train_plot = ttk.LabelFrame(main_frame, text="训练过程损失曲线")
        self.frame_train_plot.pack(pady=8, fill=tk.BOTH, expand=False)
        # ====== 结果区 ======
        frame_result = ttk.LabelFrame(main_frame, text="预测结果")
        frame_result.pack(pady=8, fill=tk.BOTH, expand=False)
        self.text_result = tk.Text(frame_result, height=8, width=110, font=("Consolas", 12))
        self.text_result.pack()
        # ====== 图表区 ======
        self.frame_plot = ttk.LabelFrame(main_frame, text="结果点图")
        self.frame_plot.pack(pady=8, fill=tk.BOTH, expand=True)
        # 初始化预测结果图表
        self.fig_pred_plot, self.ax_pred_plot = plt.subplots(figsize=(5, 3))
        self.canvas_pred_plot = FigureCanvasTkAgg(self.fig_pred_plot, master=self.frame_plot)
        self.canvas_pred_plot_widget = self.canvas_pred_plot.get_tk_widget()
        self.canvas_pred_plot_widget.pack(fill=tk.BOTH, expand=True)
        # 初始化训练损失图表
        self.fig_train_plot, self.ax_train_plot = plt.subplots(figsize=(5, 3))
        self.canvas_train_plot = FigureCanvasTkAgg(self.fig_train_plot, master=self.frame_train_plot)
        self.canvas_train_plot_widget = self.canvas_train_plot.get_tk_widget()
        self.canvas_train_plot_widget.pack(fill=tk.BOTH, expand=True)
        # 绑定窗口关闭事件
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
        self.predictor = WindPowerPredictionSystem(seq_length=12, prediction_horizon=4)
        self.weather_df = None
        self.df = None
        self.X_seq = None
        self.y = None
        self.model_trained = False
        self.train_thread = None
        self.stop_event = threading.Event()
        self.scaler_X = None  # 初始化
        self.scaler_y = None  # 初始化

    def _safe_gui_update(self, func, *args, **kwargs):
        """安全地在主线程中执行GUI更新操作"""
        self.root.after(0, func, *args, **kwargs)

    def _update_log_text(self, text):
        self.text_log.insert(tk.END, text)
        self.text_log.see(tk.END)

    def _clear_log_text(self):
        self.text_log.delete(1.0, tk.END)

    def _update_result_text(self, text):
        self.text_result.insert(tk.END, text)

    def _clear_result_text(self):
        self.text_result.delete(1.0, tk.END)

    def _clear_plot_frames(self):
        """清除所有绘图区域的现有数据，而不是销毁控件"""
        self.ax_pred_plot.clear()
        self.ax_pred_plot.set_title('预测结果点图') # 重设标题
        self.ax_pred_plot.set_xlabel('预测天数')
        self.ax_pred_plot.set_ylabel('功率(MW)')
        self.ax_train_plot.clear()
        self.ax_train_plot.set_title('模型训练损失曲线') # 重设标题
        self.ax_train_plot.set_xlabel('轮次')
        self.ax_train_plot.set_ylabel('损失')
        self.canvas_pred_plot.draw()
        self.canvas_train_plot.draw()

    def hist_test(self):
        self.frame_calendar.pack(pady=5)
    def on_calendar_ok(self):
        date_str = self.cal.get_date()
        try:
            selected_time = pd.to_datetime(date_str)
        except Exception:
            messagebox.showerror("输入错误", "日期格式错误")
            return
        self.frame_calendar.pack_forget()
        year, month = selected_time.year, selected_time.month
        self.start_train_and_predict(selected_time, mode="hist", year=year, month=month)
    def now_test(self):
        now = datetime.now()
        if now.minute >= 30:
            now = now.replace(minute=0, second=0, microsecond=0) + timedelta(hours=1)
        else:
            now = now.replace(minute=0, second=0, microsecond=0)
        self.start_train_and_predict(now, mode="now")
    def force_stop(self):
        if self.train_thread and self.train_thread.is_alive():
            self.stop_event.set()
            self._safe_gui_update(self._update_log_text, "⚠️ 已请求强行结束训练！\n")
        else:
            self._safe_gui_update(self._update_log_text, "⚠️ 当前没有正在训练的任务。\n")
    def start_train_and_predict(self, selected_time, mode, year=None, month=None):
        self._safe_gui_update(self._clear_log_text)
        self._safe_gui_update(self._clear_result_text)
        self._safe_gui_update(self._clear_plot_frames)
        self.stop_event.clear()
        self._safe_gui_update(self._update_log_text, "📡 正在获取气象数据...\n")
        self.train_thread = threading.Thread(target=self.train_and_predict, args=(selected_time, mode, year, month), daemon=False)
        self.train_thread.start()

    def train_and_predict(self, selected_time, mode, year, month):
        self.stop_event.clear()
        try:
            fetch_end_time = selected_time
            if mode == "hist":
                fetch_end_time = selected_time + timedelta(days=7)
            self.weather_df = fetch_past_30days_weather_data(fetch_end_time)
            print(f"DEBUG: weather_df 行数: {len(self.weather_df)}")
            if not isinstance(self.weather_df, str) and not self.weather_df.empty:
                print(f"DEBUG: weather_df 日期范围: {self.weather_df['timestamp'].min().date()} 到 {self.weather_df['timestamp'].max().date()}")
            if isinstance(self.weather_df, str) and self.weather_df.startswith("API_ERROR"):
                self._safe_gui_update(self._update_log_text, f"❌ 气象数据获取失败: {self.weather_df}\n")
                return
            if hasattr(self.weather_df, 'empty') and self.weather_df.empty:
                self._safe_gui_update(self._update_log_text, "❌ 气象数据为空！\n")
                return
            self._safe_gui_update(self._update_log_text, f"✅ 已获取{len(self.weather_df)}条气象数据\n")
            self._safe_gui_update(self._update_log_text, "🔧 正在按天聚合...\n")
            self.df = daily_aggregate(self.weather_df)
            print(f"DEBUG: daily_df 行数 (聚合后天数): {len(self.df)}")
            if not self.df.empty:
                print(f"DEBUG: daily_df 日期范围: {self.df['timestamp'].min().date()} 到 {self.df['timestamp'].max().date()}")
            self._safe_gui_update(self._update_log_text, f"✅ 已聚合为{len(self.df)}天数据\n")
            self._safe_gui_update(self._update_log_text, "📈 正在准备序列...\n")
            self.X_seq, self.y, self.scaler_X, self.scaler_y = prepare_sequences_daily(self.df)
            if len(self.X_seq) == 0:
                self._safe_gui_update(self._update_log_text, "❌ 数据量不足，无法训练！\n")
                return
            self._safe_gui_update(self._update_log_text, "🧠 正在训练模型...\n")
            from sklearn.model_selection import train_test_split
            idx = np.arange(len(self.X_seq))
            train_idx, val_idx = train_test_split(idx, test_size=0.2, random_state=42)
            X_seq_val, y_val = self.X_seq[val_idx], self.y[val_idx]
            self.predictor.model = keras.Sequential([
                keras.layers.Input(shape=(self.X_seq.shape[1], self.X_seq.shape[2])),
                TCN(nb_filters=64, kernel_size=3, dilations=[1,2,4,8], return_sequences=False),
                keras.layers.Dense(32, activation='relu'),
                keras.layers.Dense(4, activation='linear')
            ])
            self.predictor.model.compile(optimizer='adam', loss='mse')
            training_callback = TkinterLoggerCallback(
                self,  # 传递 gui_instance (self)
                X_val=X_seq_val,
                y_val=y_val,
                model=self.predictor.model,
                stop_event=self.stop_event
            )
            history = self.predictor.model.fit(
                self.X_seq[train_idx], self.y[train_idx],
                validation_data=(X_seq_val, y_val),
                epochs=30,
                batch_size=8,
                verbose=0,
                callbacks=[training_callback]
            )
            self.model_trained = True
            self._safe_gui_update(self._update_log_text, "✅ 训练完成！\n")
            self.predict_at_time(selected_time, mode)
        except Exception as e:
            import traceback
            err = traceback.format_exc()
            self._safe_gui_update(self._update_log_text, f"❌ 训练或预测过程中发生异常: {e}\n{err}\n")

    def predict_at_time(self, user_time, mode):
        df = self.df
        input_days = 7
        pred_days = [1,2,3,7]
        # 找到user_time在df中的索引
        user_day = user_time.date()
        idx = df.index[df['timestamp'].dt.date == user_day]
        if len(idx) == 0:
            all_days = df['timestamp'].dt.date
            nearest_idx = (pd.Series([abs((d - user_day).days) for d in all_days])).idxmin()
            nearest_time = df['timestamp'].iloc[nearest_idx]
            self._safe_gui_update(self._update_result_text, f"未找到该日期的数据，已自动对齐到最近的日期：{nearest_time.date()}\n")
            idx = nearest_idx
        else:
            idx = idx[0]
        if idx < input_days - 1:
            self._safe_gui_update(self._update_result_text, f"该日期前数据不足{input_days}天，无法预测。\n")
            return
        X_input = df.iloc[idx-input_days+1:idx+1]
        seq_features = ['wind_speed', 'wind_direction', 'turbulence_intensity', 'temperature', 'pressure', 'humidity', 'altitude', 'hour', 'month', 'air_density', 'power_change_rate']
        X_input = self.scaler_X.transform(X_input[seq_features])
        X_input = X_input.reshape(1, input_days, -1)
        y_pred = self.predictor.model.predict(X_input)[0]
        y_pred = self.scaler_y.inverse_transform(y_pred.reshape(-1,1)).flatten()
        self._safe_gui_update(self._clear_result_text)
        
        y_true = [] # 默认初始化为空列表
        if mode == "hist":
            # 有真实值
            for d in pred_days:
                idx_true = idx + d
                if idx_true < len(df):
                    y_true.append(df['power'].iloc[idx_true])
                else:
                    y_true.append(None)
            self._safe_gui_update(self._update_result_text, "天数后 | 真实均值(MW) | 预测均值(MW) | 误差(MW)\n")
            self._safe_gui_update(self._update_result_text, "-"*45+"\n")
            for i, d in enumerate(pred_days):
                if y_true[i] is not None:
                    error = abs(y_true[i] - y_pred[i])
                    self._safe_gui_update(self._update_result_text, f"{d:2d}天后 | {y_true[i]:10.3f}   | {y_pred[i]:10.3f}   | {error:8.3f}\n")
                else:
                    self._safe_gui_update(self._update_result_text, f"{d:2d}天后 |    无真实值    | {y_pred[i]:10.3f}\n")
        else:
            self._safe_gui_update(self._update_result_text, "天数后 | 预测均值(MW)\n")
            self._safe_gui_update(self._update_result_text, "-"*25+"\n")
            for i, d in enumerate(pred_days):
                self._safe_gui_update(self._update_result_text, f"{d:2d}天后 | {y_pred[i]:10.3f}\n")
        self.root.after(0, self._update_plot, y_true, y_pred, pred_days)

    def _update_plot(self, y_true, y_pred, pred_days):
        """在主线程中更新绘图"""
        self.ax_pred_plot.clear()
        # 过滤掉 y_true 中的 None，保证 x 和 y_true_filtered 长度一致
        x_true = []
        y_true_filtered = []
        for i, v in enumerate(y_true):
            if v is not None:
                x_true.append(i+1)
                y_true_filtered.append(v)
        x_pred = list(range(1, len(pred_days)+1))
        self.ax_pred_plot.scatter(x_true, y_true_filtered, color='blue', label='真实均值')
        self.ax_pred_plot.scatter(x_pred, y_pred, color='orange', label='预测均值')
        if x_true:
            self.ax_pred_plot.plot(x_true, y_true_filtered, color='blue', alpha=0.3)
        self.ax_pred_plot.plot(x_pred, y_pred, color='orange', alpha=0.3)
        self.ax_pred_plot.set_xlabel('预测天数')
        self.ax_pred_plot.set_ylabel('功率(MW)')
        self.ax_pred_plot.set_title('预测结果点图')
        self.ax_pred_plot.legend()
        self.ax_pred_plot.grid(True)
        self.canvas_pred_plot.draw()

    def _update_training_plot(self, losses, val_losses):
        """更新训练过程中的损失曲线图"""
        self.ax_train_plot.clear()
        self.ax_train_plot.plot(losses, label='训练损失', color='blue')
        self.ax_train_plot.plot(val_losses, label='验证损失', color='red')
        self.ax_train_plot.set_title('模型训练损失曲线')
        self.ax_train_plot.set_xlabel('轮次')
        self.ax_train_plot.set_ylabel('损失')
        self.ax_train_plot.legend()
        self.ax_train_plot.grid(True)
        self.canvas_train_plot.draw()

    def on_closing(self):
        try:
            if self.train_thread and self.train_thread.is_alive():
                self.stop_event.set() # 发送停止信号
                time.sleep(0.1) # 短暂等待，允许线程响应停止信号
                self.train_thread.join(timeout=5.0) # 等待训练线程结束，设置超时5秒
        except Exception as e:
            print(f"关闭窗口时线程处理异常: {e}")
        try:
            plt.close('all')
            self.canvas_pred_plot = None
            self.canvas_pred_plot_widget = None
            self.fig_pred_plot = None
            self.ax_pred_plot = None
            self.canvas_train_plot = None
            self.canvas_train_plot_widget = None
            self.fig_train_plot = None
            self.ax_train_plot = None
        except Exception as e:
            print(f"关闭窗口时资源释放异常: {e}")
        self.root.destroy() # 销毁Tkinter窗口

if __name__ == "__main__":
    root = tk.Tk()
    app = WindPowerGUI(root)
    root.mainloop()

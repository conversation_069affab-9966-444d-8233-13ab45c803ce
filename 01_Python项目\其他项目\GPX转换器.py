import gpxpy
import json
import os
import tkinter as tk
from tkinter import filedialog, messagebox
from tkinter import ttk
import datetime
import subprocess

class GPXConverterGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("GPX转GeoJSON工具")
        self.root.geometry("600x300")
        
        # 创建主框架
        main_frame = ttk.Frame(root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # GPX文件选择
        ttk.Label(main_frame, text="GPX文件:").grid(row=0, column=0, sticky=tk.W, pady=5)
        self.gpx_path = tk.StringVar()
        entry_gpx = ttk.Entry(main_frame, textvariable=self.gpx_path, width=50)
        entry_gpx.grid(row=0, column=1, padx=5)
        entry_gpx.bind('<Control-v>', self.paste_and_clean)
        ttk.Button(main_frame, text="浏览", command=self.select_gpx).grid(row=0, column=2)
        
        # 输出目录选择
        ttk.Label(main_frame, text="输出目录:").grid(row=1, column=0, sticky=tk.W, pady=5)
        self.output_dir = tk.StringVar(value=r"D:\Users\lenovo\Downloads\跑步脚本")
        entry_output = ttk.Entry(main_frame, textvariable=self.output_dir, width=50)
        entry_output.grid(row=1, column=1, padx=5)
        entry_output.bind('<Control-v>', self.paste_and_clean)
        ttk.Button(main_frame, text="浏览", command=self.select_output_dir).grid(row=1, column=2)
        
        # 转换按钮
        ttk.Button(main_frame, text="开始转换", command=self.convert).grid(row=2, column=1, pady=20)
        
        # 状态标签
        self.status_var = tk.StringVar()
        ttk.Label(main_frame, textvariable=self.status_var).grid(row=3, column=0, columnspan=3)

    def paste_and_clean(self, event):
        """处理粘贴事件，去除路径中的引号"""
        try:
            # 获取剪贴板内容
            clipboard = self.root.clipboard_get()
            # 移除开头和结尾的引号
            cleaned = clipboard.strip('"\'')
            
            # 确定当前焦点所在的输入框
            widget = event.widget
            widget.delete(0, tk.END)
            widget.insert(0, cleaned)
            
            # 阻止默认粘贴行为
            return "break"
        except:
            # 如果出错，让默认粘贴行为继续
            pass

    def select_gpx(self):
        filename = filedialog.askopenfilename(
            title="选择GPX文件",
            filetypes=[("GPX files", "*.gpx"), ("All files", "*.*")]
        )
        if filename:
            self.gpx_path.set(filename)

    def select_output_dir(self):
        directory = filedialog.askdirectory(
            title="选择输出目录",
            initialdir=self.output_dir.get()
        )
        if directory:
            self.output_dir.set(directory)

    def open_output_folder(self, folder_path):
        """打开输出文件所在的文件夹"""
        try:
            # 使用系统默认的文件浏览器打开文件夹
            if os.path.exists(folder_path):
                if os.name == 'nt':  # Windows
                    os.startfile(folder_path)
                elif os.name == 'posix':  # macOS 和 Linux
                    if os.uname().sysname == 'Darwin':  # macOS
                        subprocess.call(['open', folder_path])
                    else:  # Linux
                        subprocess.call(['xdg-open', folder_path])
        except Exception as e:
            self.status_var.set(f"打开文件夹失败: {str(e)}")

    def convert(self):
        gpx_file = self.gpx_path.get().strip('"\'')  # 去除可能存在的引号
        output_dir = self.output_dir.get().strip('"\'')  # 去除可能存在的引号
        
        if not gpx_file:
            messagebox.showerror("错误", "请选择GPX文件")
            return
            
        if not output_dir:
            messagebox.showerror("错误", "请选择输出目录")
            return
            
        try:
            # 确保输出目录存在
            os.makedirs(output_dir, exist_ok=True)
            
            # 生成输出文件路径，使用当前日期命名文件
            current_date = datetime.datetime.now()
            day_name = current_date.strftime("%a")[:3]  # 获取星期几的前三个字母
            day_number = current_date.strftime("%d")  # 获取日期
            filename = f"{day_name}_{day_number}_trace.geojson"
            output_file = os.path.join(output_dir, filename)
            
            # 执行转换
            self.gpx_to_geojson(gpx_file, output_file)
            
            self.status_var.set(f"转换成功！文件保存在: {output_file}")
            messagebox.showinfo("成功", "转换完成！")
            
            # 打开输出文件所在的文件夹
            self.open_output_folder(output_dir)
            
        except Exception as e:
            self.status_var.set(f"转换失败: {str(e)}")
            messagebox.showerror("错误", f"转换过程中出现错误：{str(e)}")

    def gpx_to_geojson(self, gpx_file_path, output_file_path):
        """
        将GPX文件转换为GeoJSON文件
        :param gpx_file_path: GPX文件路径
        :param output_file_path: 输出的GeoJSON文件路径
        """
        # 解析GPX文件
        with open(gpx_file_path, 'r', encoding='utf-8') as gpx_file:
            gpx = gpxpy.parse(gpx_file)
        
        # 提取轨迹点坐标
        coordinates = []
        for track in gpx.tracks:
            for segment in track.segments:
                for point in segment.points:
                    coordinates.append([point.longitude, point.latitude])
        
        # 创建GeoJSON结构
        geojson = {
            "type": "FeatureCollection",
            "features": [
                {
                    "type": "Feature",
                    "properties": {
                        "name": gpx.name if gpx.name else "Track"
                    },
                    "geometry": {
                        "type": "LineString",
                        "coordinates": coordinates
                    }
                }
            ]
        }
        
        # 写入GeoJSON文件
        with open(output_file_path, 'w', encoding='utf-8') as output_file:
            json.dump(geojson, output_file, indent=4)

if __name__ == "__main__":
    root = tk.Tk()
    app = GPXConverterGUI(root)
    root.mainloop()
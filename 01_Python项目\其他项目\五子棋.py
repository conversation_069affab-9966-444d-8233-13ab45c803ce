import pygame
import sys

# 初始化 pygame
pygame.init()

# 设置屏幕大小
screen_size = 600
screen = pygame.display.set_mode((screen_size, screen_size))
pygame.display.set_caption('五子棋')

# 定义颜色
WHITE = (255, 255, 255)
BLACK = (0, 0, 0)
GRAY = (200, 200, 200)

# 棋盘参数
board_size = 15
cell_size = screen_size // board_size
margin = cell_size // 2

# 初始化棋盘状态
board = [[0] * board_size for _ in range(board_size)]
current_player = 1  # 1 for black, -1 for white


def draw_board():
    # 填充背景色
    screen.fill(GRAY)

    # 画棋盘
    for row in range(board_size):
        pygame.draw.line(screen, BLACK, (margin, margin + row * cell_size),
                         (screen_size - margin, margin + row * cell_size), 1)
        pygame.draw.line(screen, BLACK, (margin + row * cell_size, margin),
                         (margin + row * cell_size, screen_size - margin), 1)


def draw_pieces():
    for row in range(board_size):
        for col in range(board_size):
            if board[row][col] != 0:
                color = BLACK if board[row][col] == 1 else WHITE
                pos = (margin + col * cell_size, margin + row * cell_size)
                pygame.draw.circle(screen, color, pos, cell_size // 2 - 2)


def check_winner():
    # 检查是否有玩家获胜
    directions = [(1, 0), (0, 1), (1, 1), (1, -1)]
    for row in range(board_size):
        for col in range(board_size):
            if board[row][col] == 0:
                continue
            for dx, dy in directions:
                count = 1
                for step in range(1, 5):
                    x, y = row + step * dx, col + step * dy
                    if 0 <= x < board_size and 0 <= y < board_size and board[x][y] == board[row][col]:
                        count += 1
                    else:
                        break
                if count >= 5:
                    return board[row][col]
    return 0


def main():
    global current_player
    clock = pygame.time.Clock()
    winner = 0

    while True:
        for event in pygame.event.get():
            if event.type == pygame.QUIT:
                pygame.quit()
                sys.exit()
            elif event.type == pygame.MOUSEBUTTONDOWN and winner == 0:
                x, y = event.pos
                col = (x - margin + cell_size // 2) // cell_size
                row = (y - margin + cell_size // 2) // cell_size
                if 0 <= row < board_size and 0 <= col < board_size and board[row][col] == 0:
                    board[row][col] = current_player
                    winner = check_winner()
                    current_player *= -1

        draw_board()
        draw_pieces()

        if winner != 0:
            font = pygame.font.SysFont(None, 48)
            text = font.render('Black wins!' if winner == 1 else 'White wins!', True, BLACK)
            screen.blit(text, (screen_size // 3, screen_size // 3))

        pygame.display.flip()
        clock.tick(60)


if __name__ == '__main__':
    main()
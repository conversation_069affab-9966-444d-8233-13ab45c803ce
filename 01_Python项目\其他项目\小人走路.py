import pygame
import random
import sys

# 初始化pygame
pygame.init()

# 设置窗口尺寸
WIDTH, HEIGHT = 800, 600
screen = pygame.display.set_mode((WIDTH, HEIGHT))
pygame.display.set_caption("Q版初音未来像素小人")

# 颜色定义
BLACK = (0, 0, 0)
WHITE = (255, 255, 255)
BLUE = (0, 191, 255)
TEAL = (0, 188, 188)
LIGHT_TEAL = (139, 224, 204)
GRAY = (100, 100, 100)
PINK = (255, 182, 193)
SKIN = (255, 220, 200)  # 添加肤色

# 帧率控制
clock = pygame.time.Clock()
FPS = 60

# 原有的 ScrollingCity 类保持不变
class ScrollingCity:
    # ... (保持原有的 ScrollingCity 类代码不变)
    def __init__(self):
        self.CITY_WIDTH = WIDTH * 5
        self.scroll_x = 0
        self.bg_surface = pygame.Surface((self.CITY_WIDTH, HEIGHT))
        self.buildings_surface = pygame.Surface((self.CITY_WIDTH, HEIGHT), pygame.SRCALPHA)
        self.foreground_surface = pygame.Surface((self.CITY_WIDTH, HEIGHT), pygame.SRCALPHA)
        self.generate_city()
    
    def generate_city(self):
        for y in range(HEIGHT):
            color_value = int(20 + 30 * (1 - y / HEIGHT))
            pygame.draw.line(self.bg_surface, (color_value, color_value, 80), 
                           (0, y), (self.CITY_WIDTH, y))
        
        pygame.draw.circle(self.bg_surface, (255, 255, 220), (300, 100), 50)
        pygame.draw.circle(self.bg_surface, (20, 20, 80), (280, 90), 45)
        
        for _ in range(300):
            star_x = random.randint(0, self.CITY_WIDTH)
            star_y = random.randint(0, HEIGHT // 2)
            star_size = random.randint(1, 3)
            brightness = random.randint(150, 255)
            pygame.draw.circle(self.bg_surface, (brightness, brightness, brightness), 
                             (star_x, star_y), star_size)
        
        for i in range(50):
            building_width = random.randint(50, 150)
            building_height = random.randint(100, 200)
            building_x = random.randint(0, self.CITY_WIDTH - building_width)
            building_color = (
                random.randint(20, 40),
                random.randint(20, 40),
                random.randint(40, 70)
            )
            pygame.draw.rect(self.bg_surface, building_color, 
                           (building_x, HEIGHT - 100 - building_height, building_width, building_height))
            for w in range(building_width // 20):
                for h in range(building_height // 30):
                    if random.random() > 0.3:
                        window_brightness = random.randint(200, 255)
                        window_color = (window_brightness, window_brightness, 150)
                        pygame.draw.rect(self.bg_surface, window_color, 
                                       (building_x + 5 + w * 20, 
                                        HEIGHT - 95 - building_height + h * 30, 
                                        10, 15))
        
        for i in range(30):
            building_width = random.randint(80, 200)
            building_height = random.randint(150, 350)
            building_x = random.randint(0, self.CITY_WIDTH - building_width)
            building_color = (
                random.randint(30, 60),
                random.randint(30, 60),
                random.randint(60, 90)
            )
            pygame.draw.rect(self.buildings_surface, building_color, 
                           (building_x, HEIGHT - 100 - building_height, building_width, building_height))
            for w in range(building_width // 25):
                for h in range(building_height // 35):
                    if random.random() > 0.25:
                        window_brightness = random.randint(180, 255)
                        window_color = (window_brightness, window_brightness, 150)
                        pygame.draw.rect(self.buildings_surface, window_color, 
                                       (building_x + 8 + w * 25, 
                                        HEIGHT - 92 - building_height + h * 35, 
                                        15, 20))
            if random.random() > 0.7:
                pygame.draw.rect(self.buildings_surface, (255, 0, 0, 150), 
                               (building_x + building_width // 2 - 5, 
                                HEIGHT - 100 - building_height - 15, 10, 15))
        
        pygame.draw.rect(self.foreground_surface, (30, 30, 30), 
                       (0, HEIGHT - 100, self.CITY_WIDTH, 100))
        
        for i in range(0, self.CITY_WIDTH, 100):
            pygame.draw.rect(self.foreground_surface, (200, 200, 0), 
                           (i, HEIGHT - 50, 40, 5))
        
        for i in range(0, self.CITY_WIDTH, 200):
            pygame.draw.rect(self.foreground_surface, (50, 50, 50), 
                           (i, HEIGHT - 150, 5, 50))
            pygame.draw.rect(self.foreground_surface, (70, 70, 70), 
                           (i - 10, HEIGHT - 160, 25, 10))
            pygame.draw.circle(self.foreground_surface, (255, 255, 200, 200), 
                             (i + 2, HEIGHT - 155), 15)
            pygame.draw.circle(self.foreground_surface, (255, 255, 150, 100), 
                             (i + 2, HEIGHT - 155), 25)
        
        for _ in range(50):
            detail_x = random.randint(0, self.CITY_WIDTH)
            detail_type = random.randint(0, 3)
            if detail_type == 0:
                pygame.draw.rect(self.foreground_surface, (100, 100, 100), 
                               (detail_x, HEIGHT - 130, 15, 30))
            elif detail_type == 1:
                pygame.draw.rect(self.foreground_surface, (139, 69, 19), 
                               (detail_x, HEIGHT - 130, 40, 10))
                pygame.draw.rect(self.foreground_surface, (90, 40, 10), 
                               (detail_x + 5, HEIGHT - 120, 5, 20))
                pygame.draw.rect(self.foreground_surface, (90, 40, 10), 
                               (detail_x + 30, HEIGHT - 120, 5, 20))
            elif detail_type == 2:
                pygame.draw.rect(self.foreground_surface, (101, 67, 33), 
                               (detail_x, HEIGHT - 140, 10, 40))
                pygame.draw.circle(self.foreground_surface, (0, 100, 0), 
                                 (detail_x + 5, HEIGHT - 150), 20)
            else:
                pygame.draw.rect(self.foreground_surface, (200, 0, 0), 
                               (detail_x, HEIGHT - 130, 15, 25))
                pygame.draw.rect(self.foreground_surface, (150, 0, 0), 
                               (detail_x - 2, HEIGHT - 105, 19, 5))
    
    def update(self, direction, speed):
        self.scroll_x += direction * speed
        if self.scroll_x < 0:
            self.scroll_x = 0
        elif self.scroll_x > self.CITY_WIDTH - WIDTH:
            self.scroll_x = self.CITY_WIDTH - WIDTH
    
    def draw(self, surface):
        surface.blit(self.bg_surface, (-self.scroll_x * 0.3, 0))
        surface.blit(self.buildings_surface, (-self.scroll_x * 0.7, 0))
        surface.blit(self.foreground_surface, (-self.scroll_x, 0))

# 优化的 Miku 类
class Miku:
    def __init__(self):
        self.width = 40
        self.height = 60
        self.screen_x = WIDTH // 2
        self.world_x = WIDTH // 2
        self.y = HEIGHT - 150
        self.vel_x = 3
        self.frame = 0
        self.frame_count = 8
        self.frame_delay = 5
        self.frame_timer = 0
        self.direction = 1  # 固定向右
        self.is_moving = True
        self.hair_timer = 0
        self.hair_wave = 0
        self.blink_timer = 0
        self.is_blinking = False
        self.blink_duration = 5

    def update(self):
        if self.is_moving:
            self.world_x += self.vel_x * self.direction
            self.frame_timer += 1
            if self.frame_timer >= self.frame_delay:
                self.frame = (self.frame + 1) % self.frame_count
                self.frame_timer = 0
        
        self.hair_timer += 1
        if self.hair_timer >= 10:
            self.hair_wave = (self.hair_wave + 1) % 4
            self.hair_timer = 0
        
        self.blink_timer += 1
        if self.blink_timer >= 60 and random.random() > 0.7:
            self.is_blinking = True
            self.blink_timer = 0
        
        if self.is_blinking:
            self.blink_duration -= 1
            if self.blink_duration <= 0:
                self.is_blinking = False
                self.blink_duration = 5
                
        return self.direction, self.vel_x

    def draw(self, surface):
        # 阴影
        pygame.draw.ellipse(surface, (50, 50, 50, 150), (self.screen_x - 15, self.y + 55, 40, 10))
        
        # 裙子 - 更自然的形状
        skirt_offset = 2 if self.frame % 2 == 0 else -2
        pygame.draw.polygon(surface, TEAL, [
            (self.screen_x - 15, self.y + 25),
            (self.screen_x - 20 + skirt_offset, self.y + 50),
            (self.screen_x + 20 - skirt_offset, self.y + 50),
            (self.screen_x + 15, self.y + 25)
        ])
        
        # 上衣
        pygame.draw.rect(surface, TEAL, (self.screen_x - 10, self.y + 10, 20, 15))
        pygame.draw.rect(surface, WHITE, (self.screen_x - 8, self.y + 10, 16, 5))  # 衣领
        
        # 领带 - 更精致
        pygame.draw.polygon(surface, PINK, [
            (self.screen_x, self.y + 15),
            (self.screen_x + 5, self.y + 25),
            (self.screen_x - 5, self.y + 25)
        ])
        
        # 腿部动画
        leg_frames = [
            [(self.screen_x - 5, self.y + 45, 8, 15), (self.screen_x + 5, self.y + 45, 8, 15)],
            [(self.screen_x - 5, self.y + 45, 8, 15), (self.screen_x + 8, self.y + 45, 8, 15)],
            [(self.screen_x - 5, self.y + 45, 8, 15), (self.screen_x + 10, self.y + 45, 8, 15)],
            [(self.screen_x - 5, self.y + 45, 8, 15), (self.screen_x + 8, self.y + 45, 8, 15)],
            [(self.screen_x - 5, self.y + 45, 8, 15), (self.screen_x + 5, self.y + 45, 8, 15)],
            [(self.screen_x - 8, self.y + 45, 8, 15), (self.screen_x + 5, self.y + 45, 8, 15)],
            [(self.screen_x - 10, self.y + 45, 8, 15), (self.screen_x + 5, self.y + 45, 8, 15)],
            [(self.screen_x - 8, self.y + 45, 8, 15), (self.screen_x + 5, self.y + 45, 8, 15)]
        ]
        
        left_leg, right_leg = leg_frames[self.frame]
        pygame.draw.rect(surface, SKIN, left_leg)  # 使用肤色
        pygame.draw.rect(surface, SKIN, right_leg)
        pygame.draw.rect(surface, LIGHT_TEAL, (left_leg[0], left_leg[1], left_leg[2], 8))  # 袜子
        pygame.draw.rect(surface, LIGHT_TEAL, (right_leg[0], right_leg[1], right_leg[2], 8))
        
        # 手臂
        arm_frames = [2, 4, 6, 4, 2, 0, -2, -4]
        arm_offset = arm_frames[self.frame]
        pygame.draw.rect(surface, SKIN, (self.screen_x - 15, self.y + 15 + arm_offset, 6, 12))
        pygame.draw.rect(surface, SKIN, (self.screen_x + 10, self.y + 15 - arm_offset, 6, 12))
        pygame.draw.rect(surface, LIGHT_TEAL, (self.screen_x - 15, self.y + 23 + arm_offset, 6, 6))
        pygame.draw.rect(surface, LIGHT_TEAL, (self.screen_x + 10, self.y + 23 - arm_offset, 6, 6))
        
        # 头部 - 使用圆形更可爱
        pygame.draw.circle(surface, SKIN, (self.screen_x + 2, self.y), 15)
        
        # 头发
        hair_offset = [0, 1, 2, 1][self.hair_wave]
        pygame.draw.polygon(surface, TEAL, [  # 刘海
            (self.screen_x - 10, self.y - 5),
            (self.screen_x + 15, self.y - 5),
            (self.screen_x + 10, self.y + 5),
            (self.screen_x - 5, self.y + 5)
        ])
        
        # 双马尾
        pygame.draw.polygon(surface, TEAL, [  # 左马尾
            (self.screen_x - 15, self.y - 5),
            (self.screen_x - 25, self.y + 15 + hair_offset),
            (self.screen_x - 10, self.y + 10 - hair_offset)
        ])
        pygame.draw.polygon(surface, TEAL, [  # 右马尾
            (self.screen_x + 15, self.y - 5),
            (self.screen_x + 25, self.y + 15 - hair_offset),
            (self.screen_x + 10, self.y + 10 + hair_offset)
        ])
        
        # 眼睛
        if not self.is_blinking:
            pygame.draw.circle(surface, WHITE, (self.screen_x + 5, self.y - 2), 4)
            pygame.draw.circle(surface, BLUE, (self.screen_x + 5, self.y - 2), 2)
        else:
            pygame.draw.line(surface, BLACK, (self.screen_x + 2, self.y - 2), 
                           (self.screen_x + 8, self.y - 2), 1)
        
        # 嘴
        pygame.draw.arc(surface, PINK, (self.screen_x, self.y + 2, 6, 4), 0, 3.14, 1)
        
        # 腮红
        pygame.draw.circle(surface, PINK, (self.screen_x - 5, self.y + 2), 2)
        pygame.draw.circle(surface, PINK, (self.screen_x + 10, self.y + 2), 2)

# 原有的 Rain 类保持不变
class Rain:
    def __init__(self):
        self.drops = []
        self.generate_rain(200)
    
    def generate_rain(self, count):
        for _ in range(count):
            x = random.randint(0, WIDTH)
            y = random.randint(-500, 0)
            speed = random.randint(5, 15)
            length = random.randint(5, 20)
            thickness = random.choice([1, 2])
            alpha = random.randint(150, 255)
            self.drops.append([x, y, speed, length, thickness, alpha])
    
    def update(self):
        for drop in self.drops:
            drop[1] += drop[2]
            if drop[1] > HEIGHT:
                drop[0] = random.randint(0, WIDTH)
                drop[1] = random.randint(-50, 0)
                drop[2] = random.randint(5, 15)
                drop[3] = random.randint(5, 20)
                drop[5] = random.randint(150, 255)
    
    def draw(self, surface):
        for drop in self.drops:
            rain_color = (LIGHT_TEAL[0], LIGHT_TEAL[1], LIGHT_TEAL[2], drop[5])
            pygame.draw.line(surface, rain_color, (drop[0], drop[1]), 
                            (drop[0], drop[1] + drop[3]), drop[4])

# 原有的 Splash 类保持不变
class Splash:
    def __init__(self):
        self.splashes = []
    
    def update(self):
        self.splashes = [splash for splash in self.splashes if splash['lifetime'] > 0]
        for splash in self.splashes:
            splash['lifetime'] -= 1
            splash['radius'] += 0.2
            if random.random() > 0.9:
                self.add_splash(random.randint(0, WIDTH), HEIGHT - 100)
    
    def add_splash(self, x, y):
        self.splashes.append({
            'x': x,
            'y': y,
            'radius': random.randint(1, 3),
            'lifetime': random.randint(10, 20),
            'color': LIGHT_TEAL
        })
    
    def draw(self, surface):
        for splash in self.splashes:
            alpha = int(255 * (splash['lifetime'] / 20))
            color = (splash['color'][0], splash['color'][1], splash['color'][2], alpha)
            pygame.draw.circle(surface, color, (int(splash['x']), int(splash['y'])), 
                             int(splash['radius']), 1)

# 创建对象实例
city = ScrollingCity()
miku = Miku()
rain = Rain()
splash = Splash()

rain_surface = pygame.Surface((WIDTH, HEIGHT), pygame.SRCALPHA)

# 主游戏循环
running = True
while running:
    for event in pygame.event.get():
        if event.type == pygame.QUIT:
            running = False
    
    # 更新 - 移除方向切换逻辑
    direction, speed = miku.update()
    city.update(direction, speed)
    rain.update()
    splash.update()
    
    # 渲染
    city.draw(screen)
    rain_surface.fill((0, 0, 0, 0))
    miku.draw(screen)
    rain.draw(rain_surface)
    splash.draw(rain_surface)
    screen.blit(rain_surface, (0, 0))
    
    pygame.display.flip()
    clock.tick(FPS)

pygame.quit()
sys.exit()
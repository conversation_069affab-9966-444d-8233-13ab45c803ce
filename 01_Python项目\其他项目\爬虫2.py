import requests
from bs4 import BeautifulSoup
import re
import mysql.connector
import matplotlib.pyplot as plt
import matplotlib
from matplotlib.font_manager import FontProperties
import os
from datetime import datetime
import pandas as pd
import random
import time
try:
    from fake_useragent import UserAgent
    ua = UserAgent()
except:
    # 如果fake_useragent导入失败，使用备选的User-Agent列表
    class FallbackUserAgent:
        def __init__(self):
            self.user_agents = [
                'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/92.0.4515.107 Safari/537.36',
                'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:89.0) Gecko/20100101 Firefox/89.0',
                'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.1 Safari/605.1.15'
            ]
        
        @property
        def random(self):
            return random.choice(self.user_agents)
    
    ua = FallbackUserAgent()

import json

# 设置中文字体支持
matplotlib.rcParams['font.sans-serif'] = ['SimHei']  # 设置中文字体为黑体
matplotlib.rcParams['axes.unicode_minus'] = False  # 解决负号显示问题

class DoubanMovieScraper:
    def __init__(self, mysql_config):
        """初始化爬虫类，设置请求头和MySQL连接配置"""
        self.ua = ua  # 使用全局的ua对象
        self.base_url = 'https://movie.douban.com/j/search_subjects'
        self.mysql_config = mysql_config
        self.conn = None
        self.cursor = None
    
    def connect_to_mysql(self):
        """连接到MySQL数据库"""
        try:
            self.conn = mysql.connector.connect(**self.mysql_config)
            self.cursor = self.conn.cursor()
            print("MySQL数据库连接成功")
            
            # 创建表（如果不存在）
            self.cursor.execute('''
            CREATE TABLE IF NOT EXISTS douban_movies (
                id INT AUTO_INCREMENT PRIMARY KEY,
                title VARCHAR(255) NOT NULL,
                rating FLOAT NOT NULL,
                box_office FLOAT NOT NULL,
                director VARCHAR(255),
                actors VARCHAR(255),
                release_date VARCHAR(50),
                scrape_date DATETIME
            )
            ''')
            self.conn.commit()
            print("数据表创建或已存在")
        except mysql.connector.Error as err:
            print(f"MySQL连接错误: {err}")
            raise
    
    def close_connection(self):
        """关闭数据库连接"""
        if self.cursor:
            self.cursor.close()
        if self.conn:
            self.conn.close()
        print("MySQL连接已关闭")
    
    def get_random_headers(self):
        """生成随机请求头"""
        return {
            'User-Agent': self.ua.random,
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Connection': 'keep-alive',
            'Referer': 'https://movie.douban.com/',
            'Cookie': 'bid=' + "".join(random.choice('0123456789abcdef') for _ in range(11))
        }

    def get_random_delay(self):
        """生成随机延迟时间"""
        return random.uniform(1, 3)

    def scrape_movies(self):
        """爬取豆瓣热门电影数据"""
        try:
            movies_data = []
            params = {
                'type': 'movie',
                'tag': '热门',
                'sort': 'recommend',
                'page_limit': 10,
                'page_start': 0
            }
            
            # 发送请求获取数据
            for _ in range(3):  # 最多重试3次
                try:
                    time.sleep(self.get_random_delay())  # 随机延迟
                    response = requests.get(
                        self.base_url,
                        headers=self.get_random_headers(),
                        params=params,
                        timeout=10
                    )
                    response.raise_for_status()
                    data = response.json()
                    
                    if 'subjects' in data:
                        for movie in data['subjects'][:10]:
                            # 获取每部电影的详细信息
                            movie_url = movie.get('url', '')
                            if movie_url:
                                time.sleep(self.get_random_delay())
                                movie_detail = self.get_movie_detail(movie_url)
                                if movie_detail:
                                    movies_data.append(movie_detail)
                        break
                except requests.exceptions.RequestException as e:
                    print(f"请求失败，正在重试: {e}")
                    time.sleep(5)
                    continue
            
            print(f"成功爬取 {len(movies_data)} 部电影的数据")
            return movies_data
        
        except Exception as e:
            print(f"爬取过程中出现错误: {e}")
            return []

    def get_movie_detail(self, url):
        """获取电影详细信息"""
        try:
            response = requests.get(url, headers=self.get_random_headers(), timeout=10)
            response.raise_for_status()
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # 提取电影信息
            title = soup.select_one('h1 span[property="v:itemreviewed"]')
            title = title.text.strip() if title else '未知'
            
            rating = soup.select_one('.rating_num')
            rating = float(rating.text.strip()) if rating else 0.0
            
            info = soup.select_one('#info')
            info_text = info.text.strip() if info else ''
            
            director_match = re.search(r'导演:\s*(.*?)(?:\s*主演:|$)', info_text)
            director = director_match.group(1).strip() if director_match else '未知'
            
            actors_match = re.search(r'主演:\s*(.*?)(?:\s*类型:|$)', info_text)
            actors = actors_match.group(1).strip() if actors_match else '未知'
            
            date = soup.select_one('span[property="v:initialReleaseDate"]')
            release_date = date.text.strip() if date else '未知'
            
            # 模拟票房数据
            box_office = rating * random.uniform(1.0, 5.0)
            
            return {
                'title': title,
                'rating': rating,
                'box_office': box_office,
                'director': director,
                'actors': actors,
                'release_date': release_date
            }
            
        except Exception as e:
            print(f"获取电影详情失败: {e}")
            return None

    def save_to_mysql(self, movies_data):
        """将电影数据保存到MySQL数据库"""
        if not movies_data:
            print("没有数据可保存")
            return False
        
        try:
            # 先清空表中的旧数据（可选）
            self.cursor.execute("TRUNCATE TABLE douban_movies")
            
            # 准备插入语句
            insert_query = '''
            INSERT INTO douban_movies 
            (title, rating, box_office, director, actors, release_date, scrape_date)
            VALUES (%s, %s, %s, %s, %s, %s, %s)
            '''
            
            # 当前时间
            now = datetime.now()
            
            # 批量插入数据
            for movie in movies_data:
                self.cursor.execute(insert_query, (
                    movie['title'],
                    movie['rating'],
                    movie['box_office'],
                    movie['director'],
                    movie['actors'],
                    movie['release_date'],
                    now
                ))
            
            # 提交更改
            self.conn.commit()
            print(f"成功保存 {len(movies_data)} 条电影数据到MySQL")
            return True
        
        except mysql.connector.Error as err:
            print(f"MySQL错误: {err}")
            self.conn.rollback()  # 回滚事务
            return False
        except Exception as e:
            print(f"保存数据时出错: {e}")
            self.conn.rollback()  # 回滚事务
            return False
    
    def get_data_from_mysql(self):
        """从MySQL获取电影数据"""
        try:
            self.cursor.execute('''
            SELECT title, box_office FROM douban_movies
            ORDER BY box_office DESC
            ''')
            results = self.cursor.fetchall()
            
            titles = [row[0] for row in results]
            box_offices = [row[1] for row in results]
            
            return titles, box_offices
        
        except mysql.connector.Error as err:
            print(f"获取数据时出现MySQL错误: {err}")
            return [], []
        except Exception as e:
            print(f"获取数据时出错: {e}")
            return [], []
    
    def create_bar_chart(self, titles, box_offices):
        """创建水平柱状图"""
        if not titles or not box_offices:
            print("没有数据可用于创建图表")
            return False
        
        try:
            # 创建图形和轴
            plt.figure(figsize=(12, 8))
            
            # 反转数据顺序以便在图表中从上到下按降序排列
            titles.reverse()
            box_offices.reverse()
            
            # 创建水平条形图
            plt.barh(titles, box_offices, color='#3498db')
            
            # 设置图表标题和标签
            plt.title('豆瓣热门电影票房榜（亿元）', fontsize=16)
            plt.xlabel('票房（亿元）', fontsize=12)
            plt.tight_layout()
            
            # 添加网格线以便于阅读
            plt.grid(axis='x', linestyle='--', alpha=0.7)
            
            # 在每个条形上显示具体数值
            for i, v in enumerate(box_offices):
                plt.text(v + 0.1, i, f'{v:.2f}亿', va='center')
            
            # 保存图表到桌面
            desktop_path = os.path.join(os.path.expanduser('~'), 'Desktop')
            file_path = os.path.join(desktop_path, '豆瓣热门电影票房榜.png')
            plt.savefig(file_path, dpi=300, bbox_inches='tight')
            
            print(f"图表已保存到: {file_path}")
            
            # 展示图表
            plt.show()
            return True
        
        except Exception as e:
            print(f"创建图表时出错: {e}")
            return False
    
    def save_to_excel(self, movies_data):
        """将电影数据保存为Excel表格"""
        try:
            # 创建DataFrame
            df = pd.DataFrame(movies_data)
            
            # 保存到桌面
            desktop_path = os.path.join(os.path.expanduser('~'), 'Desktop')
            file_path = os.path.join(desktop_path, '豆瓣热门电影数据.xlsx')
            
            # 将DataFrame保存为Excel文件
            df.to_excel(file_path, index=False, sheet_name='热门电影')
            
            print(f"Excel表格已保存到: {file_path}")
            return True
        
        except Exception as e:
            print(f"保存Excel表格时出错: {e}")
            return False

    def run(self):
        """运行爬虫流程"""
        try:
            # 连接到MySQL
            self.connect_to_mysql()
            
            # 爬取电影数据
            movies_data = self.scrape_movies()
            
            if movies_data:
                # 保存数据到MySQL
                self.save_to_mysql(movies_data)
                
                # 获取数据用于绘图
                titles, box_offices = self.get_data_from_mysql()
                
                # 创建水平柱状图
                self.create_bar_chart(titles, box_offices)
                
                # 保存为Excel表格
                self.save_to_excel(movies_data)
            
        except Exception as e:
            print(f"运行过程中出错: {e}")
        finally:
            # 关闭数据库连接
            self.close_connection()


# 使用示例
if __name__ == "__main__":
    # MySQL连接配置
    mysql_config = {
        'host': 'localhost',
        'user': 'root',      # 请更改为你的MySQL用户名
        'password': 'Hu060729',  # 请更改为你的MySQL密码
        'database': 'movie_db'   # 请确保这个数据库已经创建
    }
    
    # 创建并运行爬虫
    scraper = DoubanMovieScraper(mysql_config)
    scraper.run()
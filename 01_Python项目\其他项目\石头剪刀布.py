import tkinter as tk
from tkinter import messagebox
import random
import time
from datetime import datetime

class RockPaperScissors:
    def __init__(self):
        self.window = tk.Tk()
        self.window.title("石头剪刀布游戏")
        self.window.geometry("400x400")  # 增加窗口高度以显示排行榜
        
        # 添加历史记录列表（最多保存10条）
        self.score_history = []
        
        # 添加分数记录
        self.player_score = 0
        self.computer_score = 0
        self.tie_score = 0
        
        # 创建分数显示标签
        self.score_label = tk.Label(
            self.window, 
            text="玩家得分：0  电脑得分：0  平局：0", 
            font=("Arial", 12)
        )
        self.score_label.pack(pady=10)
        
        # 创建标签用于显示倒计时和结果
        self.label = tk.Label(self.window, text="准备开始", font=("Arial", 20))
        self.label.pack(pady=20)
        
        # 创建游戏按钮
        self.buttons_frame = tk.Frame(self.window)
        self.buttons_frame.pack(pady=20)
        
        # 初始时按钮不可用
        self.rock_btn = tk.Button(self.buttons_frame, text="石头", command=lambda: self.play("石头"), state='disabled')
        self.paper_btn = tk.Button(self.buttons_frame, text="布", command=lambda: self.play("布"), state='disabled')
        self.scissors_btn = tk.Button(self.buttons_frame, text="剪刀", command=lambda: self.play("剪刀"), state='disabled')
        
        self.rock_btn.pack(side=tk.LEFT, padx=10)
        self.paper_btn.pack(side=tk.LEFT, padx=10)
        self.scissors_btn.pack(side=tk.LEFT, padx=10)
        
        # 修改按钮区域
        self.control_buttons_frame = tk.Frame(self.window)
        self.control_buttons_frame.pack(pady=10)
        
        # 将重新开始按钮改为下一局和结束游戏按钮
        self.next_round_btn = tk.Button(self.control_buttons_frame, text="下一局", command=self.next_round)
        self.next_round_btn.pack(side=tk.LEFT, padx=10)
        
        self.end_game_btn = tk.Button(self.control_buttons_frame, text="结束游戏", command=self.end_game)
        self.end_game_btn.pack(side=tk.LEFT, padx=10)
        
        # 添加排行榜显示
        self.ranking_label = tk.Label(
            self.window,
            text="历史得分排行榜",
            font=("Arial", 12)
        )
        self.ranking_label.pack(pady=5)
        
        self.ranking_text = tk.Text(
            self.window,
            height=6,
            width=40,
            font=("Arial", 10)
        )
        self.ranking_text.pack(pady=5)
        
        # 开始第一局
        self.start_game()
        
        self.window.mainloop()
    
    def start_game(self):
        # 保存旧的得分记录（如果不是第一次游戏）
        if hasattr(self, 'player_score') and self.player_score + self.computer_score > 0:
            self.add_score_record()
            
        # 初始化游戏，只在第一次运行时调用
        self.player_score = 0
        self.computer_score = 0
        self.tie_score = 0
        self.score_label.config(text="玩家得分：0  电脑得分：0  平局：0")
        
        # 游戏开始时才有倒计时
        self.rock_btn.config(state='disabled')
        self.paper_btn.config(state='disabled')
        self.scissors_btn.config(state='disabled')
        self.next_round_btn.config(state='disabled')
        self.countdown(5)
    
    def next_round(self):
        # 进入下一局，保持分数不变，直接开始
        self.label.config(text="请出招！")
        self.rock_btn.config(state='normal')
        self.paper_btn.config(state='normal')
        self.scissors_btn.config(state='normal')
        self.next_round_btn.config(state='disabled')
    
    def end_game(self):
        # 添加得分记录
        self.add_score_record()
        
        # 显示最终得分并询问是否真的要结束
        message = f"游戏结束！\n最终得分：\n玩家：{self.player_score}\n电脑：{self.computer_score}\n平局：{self.tie_score}"
        
        # 添加排名信息
        sorted_history = self.quick_sort(self.score_history)
        current_score = self.player_score - self.computer_score
        current_rank = 1
        for record in sorted_history:
            if record['score'] > current_score:
                current_rank += 1
        
        message += f"\n\n本局排名：第{current_rank}名"
        
        if messagebox.askyesno("游戏结束", message + "\n\n是否要开始新游戏？"):
            self.start_game()
        else:
            self.window.quit()
    
    def countdown(self, count):
        if count > 0:
            self.label.config(text=f"游戏将在 {count} 秒后开始")
            self.window.after(1000, self.countdown, count - 1)
        else:
            self.label.config(text="请出招！")
            # 启用游戏按钮
            self.rock_btn.config(state='normal')
            self.paper_btn.config(state='normal')
            self.scissors_btn.config(state='normal')
    
    def play(self, player_choice):
        choices = ["石头", "剪刀", "布"]
        computer_choice = random.choice(choices)
        
        # 判断胜负
        result = self.judge_winner(player_choice, computer_choice)
        
        # 更新分数
        if result == "你赢了！":
            self.player_score += 1
        elif result == "电脑赢了！":
            self.computer_score += 1
        else:
            self.tie_score += 1
            
        # 更新分数显示
        self.score_label.config(
            text=f"玩家得分：{self.player_score}  电脑得分：{self.computer_score}  平局：{self.tie_score}"
        )
        
        # 显示结果
        result_text = f"你出了：{player_choice}\n电脑出了：{computer_choice}\n{result}"
        self.label.config(text=result_text)
        
        # 启用下一局按钮
        self.next_round_btn.config(state='normal')
        
        # 禁用游戏按钮，等待下一局
        self.rock_btn.config(state='disabled')
        self.paper_btn.config(state='disabled')
        self.scissors_btn.config(state='disabled')
    
    def judge_winner(self, player, computer):
        if player == computer:
            return "平局！"
        elif (player == "石头" and computer == "剪刀") or \
             (player == "剪刀" and computer == "布") or \
             (player == "布" and computer == "石头"):
            return "你赢了！"
        else:
            return "电脑赢了！"
    
    def quick_sort(self, arr):
        """快速排序实现"""
        if len(arr) <= 1:
            return arr
        pivot = arr[len(arr) // 2]
        left = [x for x in arr if x['score'] > pivot['score']]
        middle = [x for x in arr if x['score'] == pivot['score']]
        right = [x for x in arr if x['score'] < pivot['score']]
        return self.quick_sort(left) + middle + self.quick_sort(right)
    
    def update_ranking(self):
        """更新排行榜显示"""
        sorted_history = self.quick_sort(self.score_history)
        ranking_text = "排名  得分  时间\n"
        ranking_text += "-" * 35 + "\n"
        
        for i, record in enumerate(sorted_history, 1):
            ranking_text += f"{i:2d}.   {record['score']:<4d}  {record['time']}\n"
        
        self.ranking_text.delete(1.0, tk.END)
        self.ranking_text.insert(tk.END, ranking_text)
    
    def add_score_record(self):
        """添加新的得分记录"""
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        total_score = self.player_score - self.computer_score  # 玩家得分减去电脑得分
        
        new_record = {
            'score': total_score,
            'time': current_time
        }
        
        self.score_history.append(new_record)
        if len(self.score_history) > 10:
            self.score_history.pop(0)  # 移除最旧的记录
        
        self.update_ranking()

if __name__ == "__main__":
    game = RockPaperScissors()
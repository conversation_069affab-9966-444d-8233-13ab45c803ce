import requests
from bs4 import BeautifulSoup
import mysql.connector
import matplotlib.pyplot as plt
import pandas as pd

# 连接到MySQL数据库
def connect_to_db():
    return mysql.connector.connect(
        host="localhost",
        user="root",  # 替换为你的MySQL用户名
        password="Hu060729",  # 替换为你的MySQL密码
        database="douban_movies"
    )

# 获取豆瓣热门电影的前十名
def get_top_movies():
    url = "https://movie.douban.com/chart"
    headers = {
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
    }
    response = requests.get(url, headers=headers)
    soup = BeautifulSoup(response.text, "html.parser")
    
    movies = []
    for item in soup.find_all('div', class_='item'):
        title = item.find('a').get_text(strip=True)
        rating = float(item.find('span', class_='rating_num').get_text(strip=True))
        link = item.find('a')['href']
        movies.append((title, rating, link))
    
    return movies

# 将数据保存到MySQL
def save_to_mysql(movies):
    conn = connect_to_db()
    cursor = conn.cursor()
    
    for movie in movies:
        cursor.execute("INSERT INTO movies (name, rating, link) VALUES (%s, %s, %s)", movie)
    
    conn.commit()
    cursor.close()
    conn.close()

# 从MySQL读取数据
def read_from_mysql():
    conn = connect_to_db()
    cursor = conn.cursor()
    cursor.execute("SELECT name, rating FROM movies")
    result = cursor.fetchall()
    cursor.close()
    conn.close()
    
    return result

# 绘制水平柱状图
def plot_bar_chart(movies):
    names = [movie[0] for movie in movies]
    ratings = [movie[1] for movie in movies]
    
    plt.barh(names, ratings, color='skyblue')
    plt.xlabel('Rating')
    plt.title('Top 10 Movies on Douban')
    plt.tight_layout()
    
    # 保存图表到桌面
    plt.savefig(r"D:\Users\lenovo\Downloads\Compressed")  # 修改为你的桌面路径
    plt.show()

# 主函数
def main():
    # 获取前十部热门电影
    movies = get_top_movies()
    
    # 保存数据到MySQL数据库
    save_to_mysql(movies)
    
    # 从MySQL读取数据
    movies_from_db = read_from_mysql()
    
    # 绘制并保存水平柱状图
    plot_bar_chart(movies_from_db)

if __name__ == "__main__":
    main()

import tkinter as tk
import math

class ScientificCalculator:
    def __init__(self, master):
        """
        初始化科学计算器界面
        :param master: Tkinter主窗口
        """
        self.master = master
        master.title("科学计算器")
        master.geometry("400x600")
        master.configure(bg='#f0f0f0')

        # 显示屏
        self.display = tk.Entry(master, width=30, justify='right', 
                                font=('Arial', 20), bd=10)
        self.display.grid(row=0, column=0, columnspan=4, 
                          padx=10, pady=10, sticky='nsew')

        # 警告标签
        self.warning_label = tk.Label(master, text="", 
                                      font=('Arial', 12), 
                                      fg='red', bg='#f0f0f0')
        self.warning_label.grid(row=1, column=0, columnspan=4, 
                                padx=10, pady=5)

        # 按钮布局
        buttons = [
            '7', '8', '9', '/',
            '4', '5', '6', '*',
            '1', '2', '3', '-',
            '0', '.', '=', '+',
            'sin', 'cos', 'x²', 'C'
        ]

        # 创建按钮
        row = 2
        col = 0
        for button in buttons:
            cmd = lambda x=button: self.click(x)
            tk.Button(master, text=button, command=cmd, 
                      width=10, height=2, 
                      bg='#e0e0e0', font=('Arial', 12)).grid(
                          row=row, column=col, padx=5, pady=5)
            col += 1
            if col > 3:
                col = 0
                row += 1

        # 配置网格权重，使界面可调整
        for i in range(6):
            master.grid_rowconfigure(i, weight=1)
        for i in range(4):
            master.grid_columnconfigure(i, weight=1)

    def format_number(self, num):
        """
        格式化数字，超过8位数使用科学计数法
        :param num: 待格式化的数字
        :return: 格式化后的字符串
        """
        # 处理整数和浮点数
        try:
            # 数字转换为字符串（用于判断总位数）
            str_num = str(abs(num))
            
            # 处理整数部分（去掉小数点）
            str_num = str_num.replace('.', '')
            
            # 超过12位数给出警告
            if len(str_num) > 12:
                self.warning_label.config(text="数字过大！")
                return "数字过大"
            
            # 超过8位数使用科学计数法
            if len(str_num) > 8:
                self.warning_label.config(text="")
                return f"{num:.4e}"
            
            # 8位数以内，保留小数点后4位
            self.warning_label.config(text="")
            return f"{num:.4f}".rstrip('0').rstrip('.')
        
        except Exception as e:
            self.warning_label.config(text="计算错误")
            return "错误"

    def click(self, key):
        """
        处理按钮点击事件
        :param key: 被点击的按钮
        """
        # 清除之前的警告
        self.warning_label.config(text="")

        if key == '=':
            # 计算结果
            try:
                result = eval(self.display.get())
                formatted_result = self.format_number(result)
                
                self.display.delete(0, tk.END)
                self.display.insert(tk.END, str(formatted_result))
            except:
                self.display.delete(0, tk.END)
                self.display.insert(tk.END, "错误")
        
        elif key == 'C':
            # 清除
            self.display.delete(0, tk.END)
            self.warning_label.config(text="")
        
        elif key == 'sin':
            try:
                value = float(self.display.get())
                result = math.sin(math.radians(value))
                formatted_result = self.format_number(result)
                
                self.display.delete(0, tk.END)
                self.display.insert(tk.END, str(formatted_result))
            except:
                self.display.delete(0, tk.END)
                self.display.insert(tk.END, "错误")
        
        elif key == 'cos':
            try:
                value = float(self.display.get())
                result = math.cos(math.radians(value))
                formatted_result = self.format_number(result)
                
                self.display.delete(0, tk.END)
                self.display.insert(tk.END, str(formatted_result))
            except:
                self.display.delete(0, tk.END)
                self.display.insert(tk.END, "错误")
        
        elif key == 'x²':
            try:
                value = float(self.display.get())
                result = value ** 2
                formatted_result = self.format_number(result)
                
                self.display.delete(0, tk.END)
                self.display.insert(tk.END, str(formatted_result))
            except:
                self.display.delete(0, tk.END)
                self.display.insert(tk.END, "错误")
        
        else:
            # 普通数字和运算符
            self.display.insert(tk.END, key)

def main():
    root = tk.Tk()
    calculator = ScientificCalculator(root)
    root.mainloop()

if __name__ == "__main__":
    main()
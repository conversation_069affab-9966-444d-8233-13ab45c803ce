import pygame
import sys
import random
import os

# 初始化 Pygame
pygame.init()

# 常量
WINDOW_WIDTH = 800
WINDOW_HEIGHT = 600
FPS = 60

# 颜色常量
WHITE = (255, 255, 255)
BLACK = (0, 0, 0)
RED = (255, 0, 0)
GREEN = (0, 255, 0)
BLUE = (0, 0, 255)
YELLOW = (255, 255, 0)
PURPLE = (255, 0, 255)
ORANGE = (255, 165, 0)

class GameObject(pygame.sprite.Sprite):
    """游戏对象基类，包含通用的绘制和碰撞检测方法"""
    def __init__(self, x, y, width, height, color):
        super().__init__()
        self.rect = pygame.Rect(x, y, width, height)
        self.color = color

    def draw(self, screen, camera_x):
        """绘制对象（子类需实现）"""
        pass

class Player(GameObject):
    """玩家类，继承自 GameObject"""
    def __init__(self):
        super().__init__(400, 300, 30, 30, BLUE)
        self.vel_x = 0
        self.vel_y = 0
        self.speed = 6
        self.jump_power = -16
        self.gravity = 0.8
        self.on_ground = False
        self.jumps_left = 2
        self.health = 3
        self.invincible = False
        self.invincible_timer = 0
        self.invincible_duration = 60
        self.health_bar_height = 5

    def move(self, direction):
        """控制玩家左右移动"""
        if direction == 'left':
            self.vel_x = -self.speed
        elif direction == 'right':
            self.vel_x = self.speed

    def jump(self):
        """玩家跳跃（支持二段跳）"""
        if self.jumps_left > 0:
            self.vel_y = self.jump_power
            self.jumps_left -= 1
            self.on_ground = False

    def take_damage(self):
        """受到伤害"""
        if not self.invincible:
            self.health -= 1
            self.invincible = True
            self.invincible_timer = 0
            return True
        return False

    def update(self, platforms):
        """更新玩家位置并处理碰撞"""
        self.vel_y += self.gravity
        self.rect.x += self.vel_x
        self.rect.y += self.vel_y

        self.on_ground = False
        for platform in platforms:
            if self.rect.colliderect(platform.rect):
                if self.vel_y > 0:
                    self.rect.bottom = platform.rect.top
                    self.vel_y = 0
                    self.on_ground = True
                    self.jumps_left = 2
                elif self.vel_y < 0:
                    self.rect.top = platform.rect.bottom
                    self.vel_y = 0

        if self.vel_y > 20:
            self.vel_y = 20
        if not self.on_ground:
            self.vel_x *= 0.95

        if self.invincible:
            self.invincible_timer += 1
            if self.invincible_timer >= self.invincible_duration:
                self.invincible = False
                self.invincible_timer = 0

    def draw(self, screen, camera_x):
        """绘制玩家（包括血量条）"""
        if not self.invincible or self.invincible_timer % 4 < 2:
            pygame.draw.rect(screen, self.color, 
                           (self.rect.x - camera_x, self.rect.y, self.rect.width, self.rect.height))
        
        pygame.draw.rect(screen, (100, 100, 100),
                        (self.rect.x - camera_x, 
                         self.rect.y - self.health_bar_height - 2,
                         self.rect.width, 
                         self.health_bar_height))
        
        health_width = (self.health / 3) * self.rect.width
        if health_width > 0:
            pygame.draw.rect(screen, RED,
                           (self.rect.x - camera_x, 
                            self.rect.y - self.health_bar_height - 2,
                            health_width, 
                            self.health_bar_height))

class Platform(GameObject):
    """平台类，继承自 GameObject"""
    def __init__(self, x, y, width, height):
        super().__init__(x, y, width, height, GREEN)

    def draw(self, screen, camera_x):
        """绘制平台"""
        pygame.draw.rect(screen, self.color, 
                        (self.rect.x - camera_x, self.rect.y, self.rect.width, self.rect.height))

class Spike(GameObject):
    """尖刺类，继承自 GameObject"""
    def __init__(self, x, y, size):
        super().__init__(x, y, size, size, RED)
        self.size = size

    def draw(self, screen, camera_x):
        """绘制尖刺"""
        points = [
            (self.rect.x - camera_x, self.rect.y + self.size),
            (self.rect.x + self.size / 2 - camera_x, self.rect.y),
            (self.rect.x + self.size - camera_x, self.rect.y + self.size)
        ]
        pygame.draw.polygon(screen, self.color, points)

class Goal(GameObject):
    """终点类，继承自 GameObject"""
    def __init__(self, x, y):
        super().__init__(x, y, 30, 50, YELLOW)

    def draw(self, screen, camera_x):
        """绘制终点"""
        pygame.draw.rect(screen, self.color, 
                        (self.rect.x - camera_x, self.rect.y, self.rect.width, self.rect.height))

class Laser(GameObject):
    """激光类，继承自 GameObject"""
    def __init__(self, x, y, width, is_vertical=True):
        height = 4 if is_vertical else width
        width = width if is_vertical else 4
        super().__init__(x, y, width, height, PURPLE)
        self.is_vertical = is_vertical
        self.active = True
        self.flash_timer = 0
        self.flash_interval = 60

    def update(self):
        """更新激光状态（闪烁）"""
        self.flash_timer += 1
        if self.flash_timer >= self.flash_interval:
            self.active = not self.active
            self.flash_timer = 0

    def draw(self, screen, camera_x):
        """绘制激光"""
        if self.active:
            pygame.draw.rect(screen, self.color,
                           (self.rect.x - camera_x, self.rect.y, self.rect.width, self.rect.height))

class Food(GameObject):
    """食物类，继承自 GameObject"""
    def __init__(self, x, y):
        super().__init__(x, y, 15, 15, ORANGE)
        self.collected = False

    def draw(self, screen, camera_x):
        """绘制食物"""
        if not self.collected:
            pygame.draw.rect(screen, self.color,
                           (self.rect.x - camera_x, self.rect.y, self.rect.width, self.rect.height))

class Game:
    """游戏主类，管理游戏状态和循环"""
    def __init__(self):
        self.screen = pygame.display.set_mode((WINDOW_WIDTH, WINDOW_HEIGHT))
        pygame.display.set_caption("跑酷游戏")
        self.clock = pygame.time.Clock()
        self.fps = FPS
        self.paused = False

        # 初始化字体
        self.score_font = self.load_font(36)
        self.button_font = self.load_font(32)

        self.player = Player()
        self.camera_x = 0
        self.game_state = "RUNNING"
        self.score = 0
        self.last_platform_index = 0

        # 游戏对象组
        self.platforms = pygame.sprite.Group()
        self.spikes = pygame.sprite.Group()
        self.lasers = pygame.sprite.Group()
        self.foods = pygame.sprite.Group()
        self.goal = None
        self.level_length = 8000
        self.min_platform_distance = 180
        self.max_platform_distance = 300
        self.platform_y_variance = 150
        self.food_ground_chance = 0.6
        self.food_air_chance = 0.4
        self.spike_chance = 0.5
        self.laser_chance = 0.4
        self.create_level()

        # 结束游戏按钮
        self.end_button = {
            'rect': pygame.Rect(WINDOW_WIDTH - 120, 10, 100, 30),
            'color': RED,
            'hover_color': (200, 0, 0),
            'text': "结束游戏",
            'is_hovered': False
        }

    def load_font(self, size):
        """加载字体，尝试使用系统字体"""
        try:
            font_path = os.path.join(os.environ['SYSTEMROOT'], 'Fonts', 'msyh.ttc')
            if os.path.exists(font_path):
                return pygame.font.Font(font_path, size)
            default_font = pygame.font.get_default_font()
            return pygame.font.Font(default_font, size)
        except:
            try:
                return pygame.font.SysFont('pingfang', size)
            except:
                try:
                    return pygame.font.SysFont('notosanscjk', size)
                except:
                    default_font = pygame.font.get_default_font()
                    return pygame.font.Font(default_font, size)

    def create_level(self):
        """生成关卡"""
        start_platform = Platform(300, 400, 200, 20)
        self.platforms.add(start_platform)
        last_x = start_platform.rect.right
        last_y = start_platform.rect.y

        while last_x < self.level_length:
            distance = random.randint(self.min_platform_distance, self.max_platform_distance)
            platform_width = random.randint(100, 250)
            min_y = max(100, last_y - self.platform_y_variance)
            max_y = min(WINDOW_HEIGHT - 100, last_y + self.platform_y_variance)
            platform_y = random.randint(min_y, max_y)
            platform = Platform(last_x + distance, platform_y, platform_width, 20)
            self.platforms.add(platform)

            # 生成尖刺
            spike_positions = []
            if random.random() < self.spike_chance and len(self.platforms) > 2:
                spike_count = random.randint(1, 2)
                for _ in range(spike_count):
                    spike_x = platform.rect.x + random.randint(10, platform_width - 30)
                    spike_y = platform.rect.y - 20
                    self.spikes.add(Spike(spike_x, spike_y, 20))
                    spike_positions.append((spike_x, spike_y))

            # 生成食物
            if random.random() < self.food_ground_chance:
                food_count = random.randint(1, 3)
                attempts = 0
                foods_placed = 0
                while foods_placed < food_count and attempts < 10:
                    food_x = platform.rect.x + random.randint(0, platform_width - 15)
                    food_y = platform.rect.y - random.randint(20, 40)
                    valid_position = True
                    for spike_x, spike_y in spike_positions:
                        if (abs(food_x - spike_x) < 25 and abs(food_y - spike_y) < 25):
                            valid_position = False
                            break
                    if valid_position:
                        self.foods.add(Food(food_x, food_y))
                        foods_placed += 1
                    attempts += 1

            if random.random() < self.food_air_chance:
                if platform.rect.right < self.level_length - 300:
                    max_attempts = 5
                    for _ in range(max_attempts):
                        food_x = last_x + random.randint(distance//2, distance)
                        max_platform_height = float('-inf')
                        for p in self.platforms:
                            if p.rect.x <= food_x <= p.rect.right:
                                max_platform_height = max(max_platform_height, p.rect.y)
                        if max_platform_height != float('-inf'):
                            min_food_y = max_platform_height - 150
                            max_food_y = max_platform_height - 50
                            food_y = random.randint(min_food_y, max_food_y)
                            valid_position = True
                            for spike_x, spike_y in spike_positions:
                                if (abs(food_x - spike_x) < 25 and abs(food_y - spike_y) < 25):
                                    valid_position = False
                                    break
                            if valid_position:
                                self.foods.add(Food(food_x, food_y))
                                break

            last_x = platform.rect.right
            last_y = platform.rect.y

        final_platform = Platform(last_x + 200, 400, 300, 20)
        self.platforms.add(final_platform)
        self.goal = Goal(final_platform.rect.right - 50, final_platform.rect.y - 50)

        for i, platform in enumerate(list(self.platforms)[:-1]):
            next_platform = list(self.platforms)[i + 1]
            if random.random() < self.laser_chance:
                if random.random() < 0.5:
                    laser_x = random.randint(platform.rect.right, next_platform.rect.x)
                    laser_height = random.randint(100, 200)
                    laser_y = random.randint(
                        min(platform.rect.y, next_platform.rect.y) - laser_height,
                        max(platform.rect.y, next_platform.rect.y)
                    )
                    self.lasers.add(Laser(laser_x, laser_y, laser_height, True))
                else:
                    laser_x = random.randint(platform.rect.right, next_platform.rect.x)
                    laser_width = random.randint(50, 150)
                    laser_y = random.randint(
                        min(platform.rect.y, next_platform.rect.y) - 100,
                        max(platform.rect.y, next_platform.rect.y) - 50
                    )
                    self.lasers.add(Laser(laser_x, laser_y, laser_width, False))

    def draw_end_screen(self):
        """绘制游戏结束画面"""
        self.screen.fill(BLACK)
        result_text = "游戏结束！" if self.game_state == "GAME_OVER" else "恭喜胜利！"
        result_surface = self.score_font.render(result_text, True, WHITE)
        result_rect = result_surface.get_rect(center=(WINDOW_WIDTH // 2, WINDOW_HEIGHT // 2 - 50))
        self.screen.blit(result_surface, result_rect)

        score_text = f"总得分: {self.score}"
        score_surface = self.score_font.render(score_text, True, WHITE)
        score_rect = score_surface.get_rect(center=(WINDOW_WIDTH // 2, WINDOW_HEIGHT // 2))
        self.screen.blit(score_surface, score_rect)

        restart_button = pygame.Rect(WINDOW_WIDTH // 2 - 100, WINDOW_HEIGHT // 2 + 50, 200, 50)
        self.draw_button(restart_button, GREEN, "再来一局", WHITE)

        quit_button = pygame.Rect(WINDOW_WIDTH // 2 - 100, WINDOW_HEIGHT // 2 + 110, 200, 50)
        self.draw_button(quit_button, RED, "退出游戏", WHITE)

        pygame.display.flip()

    def draw_button(self, rect, color, text, text_color):
        """绘制按钮"""
        pygame.draw.rect(self.screen, color, rect)
        text_surface = self.button_font.render(text, True, text_color)
        text_rect = text_surface.get_rect(center=rect.center)
        self.screen.blit(text_surface, text_rect)

    def handle_events(self):
        """处理事件（键盘、鼠标）"""
        for event in pygame.event.get():
            if event.type == pygame.QUIT:
                return False
            elif event.type == pygame.KEYDOWN:
                if event.key == pygame.K_SPACE and self.game_state == "RUNNING" and not self.paused:
                    self.player.jump()
                elif event.key == pygame.K_p and self.game_state == "RUNNING":
                    self.paused = not self.paused
            elif event.type == pygame.MOUSEBUTTONDOWN:
                if event.button == 1:
                    mouse_pos = event.pos
                    if self.game_state == "RUNNING":
                        if self.end_button['rect'].collidepoint(mouse_pos):
                            self.game_state = "END"
                    elif self.game_state in ["GAME_OVER", "WIN", "END"]:
                        restart_button = pygame.Rect(WINDOW_WIDTH // 2 - 100, WINDOW_HEIGHT // 2 + 50, 200, 50)
                        quit_button = pygame.Rect(WINDOW_WIDTH // 2 - 100, WINDOW_HEIGHT // 2 + 110, 200, 50)
                        if restart_button.collidepoint(mouse_pos):
                            self.__init__()
                        elif quit_button.collidepoint(mouse_pos):
                            pygame.quit()
                            sys.exit()
            elif event.type == pygame.MOUSEMOTION:
                mouse_pos = event.pos
                self.end_button['is_hovered'] = self.end_button['rect'].collidepoint(mouse_pos)

        if self.game_state == "RUNNING" and not self.paused:
            keys = pygame.key.get_pressed()
            if keys[pygame.K_LEFT]:
                self.player.move('left')
            elif keys[pygame.K_RIGHT]:
                self.player.move('right')
            else:
                self.player.vel_x = 0

        return True

    def update(self):
        """更新游戏状态"""
        if self.game_state == "RUNNING" and not self.paused:
            self.player.update(self.platforms)
            self.lasers.update()

            if self.player.rect.bottom > WINDOW_HEIGHT:
                self.game_state = "GAME_OVER"

            if self.player.rect.colliderect(self.goal.rect):
                self.game_state = "WIN"

            collided_lasers = pygame.sprite.spritecollide(self.player, self.lasers, False)
            for laser in collided_lasers:
                if laser.active and self.player.take_damage():
                    if self.player.health <= 0:
                        self.game_state = "GAME_OVER"

            collided_spikes = pygame.sprite.spritecollide(self.player, self.spikes, False)
            for spike in collided_spikes:
                if self.player.take_damage():
                    self.score -= 3
                    if self.player.health <= 0:
                        self.game_state = "GAME_OVER"

            collided_foods = pygame.sprite.spritecollide(self.player, self.foods, False)
            for food in collided_foods:
                if not food.collected:
                    food.collected = True
                    self.score += 5

            target_camera_x = self.player.rect.x - WINDOW_WIDTH // 2
            self.camera_x += (target_camera_x - self.camera_x) * 0.1

            current_platform_index = self.get_current_platform_index()
            if current_platform_index > self.last_platform_index:
                self.score += (current_platform_index - self.last_platform_index)
                self.last_platform_index = current_platform_index

    def get_current_platform_index(self):
        """获取当前玩家的平台索引"""
        for i, platform in enumerate(self.platforms):
            if self.player.rect.x > platform.rect.right:
                continue
            return i
        return len(self.platforms) - 1

    def draw(self):
        """绘制游戏画面"""
        self.screen.fill(BLACK)

        for platform in self.platforms:
            platform.draw(self.screen, self.camera_x)
        for spike in self.spikes:
            spike.draw(self.screen, self.camera_x)
        for laser in self.lasers:
            laser.draw(self.screen, self.camera_x)
        for food in self.foods:
            food.draw(self.screen, self.camera_x)
        self.goal.draw(self.screen, self.camera_x)
        self.player.draw(self.screen, self.camera_x)

        score_text = self.score_font.render(f"得分: {self.score}", True, WHITE)
        self.screen.blit(score_text, (10, 10))

        fps_text = self.score_font.render(f"FPS: {int(self.clock.get_fps())}", True, WHITE)
        self.screen.blit(fps_text, (10, 50))

        button_color = self.end_button['hover_color'] if self.end_button['is_hovered'] else self.end_button['color']
        self.draw_button(self.end_button['rect'], button_color, self.end_button['text'], WHITE)
        pygame.draw.rect(self.screen, WHITE, self.end_button['rect'], 2)

        if self.paused:
            pause_surface = self.score_font.render("游戏已暂停，按 P 继续", True, WHITE)
            pause_rect = pause_surface.get_rect(center=(WINDOW_WIDTH // 2, WINDOW_HEIGHT // 2))
            self.screen.blit(pause_surface, pause_rect)

        pygame.display.flip()

    def run(self):
        """游戏主循环"""
        running = True
        while running:
            try:
                running = self.handle_events()
                if self.game_state == "RUNNING":
                    self.update()
                    self.draw()
                elif self.game_state in ["GAME_OVER", "WIN", "END"]:
                    self.draw_end_screen()
                self.clock.tick(self.fps)
            except Exception as e:
                print(f"游戏运行时发生错误: {e}")
                running = False

        pygame.quit()
        sys.exit()

if __name__ == "__main__":
    game = Game()
    game.run()
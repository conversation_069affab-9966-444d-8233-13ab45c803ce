import pygame
import sys
import os
import random
import math

# 初始化 Pygame
pygame.init()

# 设置常量
WINDOW_WIDTH = 800
WINDOW_HEIGHT = 600
FPS = 60

# 颜色常量
WHITE = (255, 255, 255)
BLACK = (0, 0, 0)
RED = (255, 0, 0)
GREEN = (0, 255, 0)
BLUE = (0, 0, 255)
YELLOW = (255, 255, 0)

# 预加载字体以避免重复创建
FONT_SMALL = pygame.font.Font(None, 36)
FONT_LARGE = pygame.font.Font(None, 48)

# 对象池管理类
class ObjectPool:
    def __init__(self, create_func, max_size=100):
        self.pool = []
        self.create_func = create_func
        self.max_size = max_size
    
    def get(self):
        if self.pool:
            return self.pool.pop()
        return self.create_func()
    
    def recycle(self, obj):
        if len(self.pool) < self.max_size:
            self.pool.append(obj)

class Bullet:
    def __init__(self, x=0, y=0, direction=1, color=YELLOW):
        self.width = 5
        self.height = 10
        self.x = x
        self.y = y
        self.speed = 7
        self.direction = direction  # 1表示向上(玩家子弹)，-1表示向下(敌人子弹)
        self.color = color
        self.damage = 10
        self.active = False
    
    def reset(self, x, y, direction=1, color=YELLOW):
        self.x = x
        self.y = y
        self.direction = direction
        self.color = color
        self.active = True
        return self

    def move(self):
        self.y -= self.speed * self.direction
        if self.y < 0 or self.y > WINDOW_HEIGHT:
            self.active = False
            return True
        return False

    def draw(self, screen):
        pygame.draw.rect(screen, self.color, (self.x, self.y, self.width, self.height))

class Player:
    def __init__(self):
        self.width = 50
        self.height = 50
        self.x = WINDOW_WIDTH // 2 - self.width // 2
        self.y = WINDOW_HEIGHT - self.height - 10
        self.speed = 5
        self.color = RED
        self.score = 0
        self.health = 100
        self.bullets = []  # 活跃的子弹
        self.bullet_pool = ObjectPool(Bullet, 50)  # 子弹对象池
        self.shoot_cooldown = 0
        self.shoot_delay = 20  # 射击冷却时间（帧数）
        # 预先计算玩家绘制信息以减少每帧的计算
        self.player_rect = pygame.Rect(0, 0, self.width, self.height)

    def move(self, direction):
        if direction == 'left' and self.x > 0:
            self.x -= self.speed
        if direction == 'right' and self.x < WINDOW_WIDTH - self.width:
            self.x += self.speed
        # 更新碰撞矩形
        self.player_rect.x = self.x
        self.player_rect.y = self.y

    def shoot(self):
        if self.shoot_cooldown <= 0:
            bullet_x = self.x + self.width // 2 - 2.5  # 子弹从玩家中心发射
            bullet = self.bullet_pool.get().reset(bullet_x, self.y)
            self.bullets.append(bullet)
            self.shoot_cooldown = self.shoot_delay

    def update(self):
        if self.shoot_cooldown > 0:
            self.shoot_cooldown -= 1
        
        # 更新子弹
        i = 0
        while i < len(self.bullets):
            bullet = self.bullets[i]
            if bullet.move():
                self.bullet_pool.recycle(bullet)
                self.bullets.pop(i)
            else:
                i += 1

    def draw(self, screen):
        # 绘制玩家
        pygame.draw.rect(screen, self.color, (self.x, self.y, self.width, self.height))
        
        # 绘制子弹
        for bullet in self.bullets:
            bullet.draw(screen)
            
        # 绘制血条
        health_width = (self.width * self.health) // 100
        pygame.draw.rect(screen, RED, (self.x, self.y - 10, self.width, 5))
        pygame.draw.rect(screen, GREEN, (self.x, self.y - 10, health_width, 5))

class Enemy:
    def __init__(self):
        self.width = 30
        self.height = 30
        self.x = 0
        self.y = 0
        self.speed = 0
        self.color = BLUE
        self.health = 30
        self.max_health = 30
        self.damage = 20  # 碰撞造成的伤害
        self.active = False
        # 预先计算碰撞矩形以减少每帧的计算
        self.rect = pygame.Rect(0, 0, self.width, self.height)
    
    def reset(self, level=1):
        self.x = random.randint(0, WINDOW_WIDTH - self.width)
        self.y = -self.height
        self.speed = random.randint(1, 3 + (level - 1) // 2)
        self.health = self.max_health
        self.active = True
        # 更新碰撞矩形
        self.rect.x = self.x
        self.rect.y = self.y
        return self

    def move(self):
        self.y += self.speed
        # 更新碰撞矩形
        self.rect.y = self.y
        if self.y > WINDOW_HEIGHT:
            self.active = False
            return True
        return False

    def draw(self, screen):
        # 绘制敌人
        pygame.draw.rect(screen, self.color, self.rect)
        # 绘制血条
        health_width = (self.width * self.health) // self.max_health
        pygame.draw.rect(screen, RED, (self.x, self.y - 10, self.width, 5))
        pygame.draw.rect(screen, GREEN, (self.x, self.y - 10, health_width, 5))

class WhiteEnemy(Enemy):
    def __init__(self):
        super().__init__()
        self.color = WHITE
        self.health = 50
        self.max_health = 50
        self.damage = 30  # 碰撞造成的伤害
        self.bullets = []  # 活跃的子弹
        self.bullet_pool = ObjectPool(Bullet, 20)  # 子弹对象池
        self.shoot_cooldown = 0
        self.shoot_delay = 120  # 初始射击冷却时间
    
    def reset(self, level=1):
        super().reset(level)
        self.speed = random.randint(1, 2 + (level - 1) // 3)  # 速度稍慢于普通敌人
        self.shoot_delay = max(60, 120 - level * 5)  # 随级别增加射速
        self.shoot_cooldown = random.randint(30, self.shoot_delay)
        # 清空子弹列表
        for bullet in self.bullets:
            self.bullet_pool.recycle(bullet)
        self.bullets = []
        return self
    
    def shoot(self):
        if self.shoot_cooldown <= 0:
            bullet_x = self.x + self.width // 2 - 2.5
            bullet = self.bullet_pool.get().reset(bullet_x, self.y + self.height, -1, WHITE)
            self.bullets.append(bullet)
            self.shoot_cooldown = self.shoot_delay
    
    def update(self):
        if self.shoot_cooldown > 0:
            self.shoot_cooldown -= 1
        else:
            self.shoot()
        
        # 更新子弹
        i = 0
        while i < len(self.bullets):
            bullet = self.bullets[i]
            if bullet.move():
                self.bullet_pool.recycle(bullet)
                self.bullets.pop(i)
            else:
                i += 1
    
    def draw(self, screen):
        # 调用父类方法绘制敌人和血条
        super().draw(screen)
        # 绘制子弹
        for bullet in self.bullets:
            bullet.draw(screen)

class Game:
    def __init__(self):
        self.screen = pygame.display.set_mode((WINDOW_WIDTH, WINDOW_HEIGHT))
        pygame.display.set_caption("太空射击游戏")
        self.clock = pygame.time.Clock()
        
        self.player = Player()
        
        # 使用对象池管理敌人
        self.enemy_pool = ObjectPool(Enemy, 100)
        self.white_enemy_pool = ObjectPool(WhiteEnemy, 20)
        
        self.enemies = []  # 活跃的普通敌人
        self.white_enemies = []  # 活跃的白色敌人
        
        # 限制最大敌人数量，防止过多对象导致性能下降
        self.max_enemies = 50
        self.max_white_enemies = 10
        
        self.enemy_spawn_timer = 0
        self.enemy_spawn_delay = 60  # 初始敌人生成延迟（帧数）
        self.white_enemy_spawn_timer = 0
        self.white_enemy_spawn_delay = 180  # 白色敌机生成延迟（较长）
        
        # 预渲染的UI文本
        self.ui_texts = {}
        self.last_score = -1
        self.last_health = -1
        self.last_level = -1
        
        self.running = True
        self.game_state = "RUNNING"
        self.score = 0
        self.level = 1
        
        # 性能监控
        self.frame_times = []
        self.last_fps_update = pygame.time.get_ticks()

    def handle_events(self):
        for event in pygame.event.get():
            if event.type == pygame.QUIT:
                self.running = False
            elif event.type == pygame.KEYDOWN:
                if event.key == pygame.K_ESCAPE:
                    self.running = False
                elif event.key == pygame.K_p:
                    self.game_state = "PAUSED" if self.game_state == "RUNNING" else "RUNNING"
                elif event.key == pygame.K_f:  # 显示FPS
                    current_fps = self.clock.get_fps()
                    print(f"FPS: {current_fps:.1f}")

        if self.game_state == "RUNNING":
            keys = pygame.key.get_pressed()
            if keys[pygame.K_LEFT]:
                self.player.move('left')
            if keys[pygame.K_RIGHT]:
                self.player.move('right')
            if keys[pygame.K_SPACE]:
                self.player.shoot()

    def check_collisions(self):
        player_rect = self.player.player_rect
        
        # 1. 检查子弹和普通敌人的碰撞
        for enemy in self.enemies[:]:
            for bullet in self.player.bullets[:]:
                bullet_rect = pygame.Rect(bullet.x, bullet.y, bullet.width, bullet.height)
                if enemy.rect.colliderect(bullet_rect):
                    enemy.health -= bullet.damage
                    self.player.bullets.remove(bullet)
                    self.player.bullet_pool.recycle(bullet)
                    if enemy.health <= 0:
                        self.enemies.remove(enemy)
                        self.enemy_pool.recycle(enemy)
                        self.score += 10
                        break
        
        # 2. 检查子弹和白色敌人的碰撞
        for enemy in self.white_enemies[:]:
            for bullet in self.player.bullets[:]:
                bullet_rect = pygame.Rect(bullet.x, bullet.y, bullet.width, bullet.height)
                if enemy.rect.colliderect(bullet_rect):
                    enemy.health -= bullet.damage
                    self.player.bullets.remove(bullet)
                    self.player.bullet_pool.recycle(bullet)
                    if enemy.health <= 0:
                        self.white_enemies.remove(enemy)
                        self.white_enemy_pool.recycle(enemy)
                        self.score += 20  # 白色敌机分数更高
                        break

        # 3. 检查敌人子弹和玩家的碰撞
        for enemy in self.white_enemies:
            for bullet in enemy.bullets[:]:
                bullet_rect = pygame.Rect(bullet.x, bullet.y, bullet.width, bullet.height)
                if player_rect.colliderect(bullet_rect):
                    self.player.health -= bullet.damage
                    enemy.bullets.remove(bullet)
                    enemy.bullet_pool.recycle(bullet)
                    if self.player.health <= 0:
                        self.game_state = "GAME_OVER"
                        break

        # 4. 检查玩家和敌人的碰撞
        for enemy in self.enemies[:]:
            if player_rect.colliderect(enemy.rect):
                self.player.health -= enemy.damage
                self.enemies.remove(enemy)
                self.enemy_pool.recycle(enemy)
                if self.player.health <= 0:
                    self.game_state = "GAME_OVER"
        
        # 5. 检查玩家和白色敌人的碰撞
        for enemy in self.white_enemies[:]:
            if player_rect.colliderect(enemy.rect):
                self.player.health -= enemy.damage
                self.white_enemies.remove(enemy)
                self.white_enemy_pool.recycle(enemy)
                if self.player.health <= 0:
                    self.game_state = "GAME_OVER"

    def update_difficulty(self):
        # 根据分数更新难度级别
        new_level = self.score // 50 + 1
        if new_level != self.level:
            self.level = new_level
            # 随着级别提升，减少敌人生成延迟（增加敌人数量）
            self.enemy_spawn_delay = max(15, 60 - (self.level - 1) * 5)

    def update(self):
        if self.game_state == "RUNNING":
            # 更新玩家
            self.player.update()
            
            # 更新白色敌机
            for enemy in self.white_enemies[:]:
                enemy.update()
                if enemy.move():
                    self.white_enemies.remove(enemy)
                    self.white_enemy_pool.recycle(enemy)
            
            # 更新难度级别
            self.update_difficulty()
            
            # 生成普通敌人
            self.enemy_spawn_timer += 1
            if self.enemy_spawn_timer >= self.enemy_spawn_delay and len(self.enemies) < self.max_enemies:
                # 根据难度级别决定生成敌人数量
                enemies_to_spawn = min(5, 1 + (self.level - 1) // 2)
                for _ in range(enemies_to_spawn):
                    new_enemy = self.enemy_pool.get().reset(self.level)
                    self.enemies.append(new_enemy)
                self.enemy_spawn_timer = 0
            
            # 生成白色敌人
            self.white_enemy_spawn_timer += 1
            if self.white_enemy_spawn_timer >= self.white_enemy_spawn_delay and len(self.white_enemies) < self.max_white_enemies:
                # 白色敌人数量较少且缓慢增加
                white_enemies_to_spawn = min(3, (self.level + 1) // 3)
                if random.random() < 0.5:  # 只有50%的概率生成白色敌人
                    for _ in range(white_enemies_to_spawn):
                        new_enemy = self.white_enemy_pool.get().reset(self.level)
                        self.white_enemies.append(new_enemy)
                self.white_enemy_spawn_timer = 0

            # 更新敌人位置
            for enemy in self.enemies[:]:
                if enemy.move():
                    self.enemies.remove(enemy)
                    self.enemy_pool.recycle(enemy)

            # 检测碰撞 (每两帧检测一次以提高性能)
            if pygame.time.get_ticks() % 2 == 0:
                self.check_collisions()
            
            # 只在值变化时更新UI文本
            if self.score != self.last_score:
                self.ui_texts['score'] = FONT_SMALL.render(f"Score: {self.score}", True, WHITE)
                self.last_score = self.score
            
            if self.player.health != self.last_health:
                self.ui_texts['health'] = FONT_SMALL.render(f"Health: {self.player.health}", True, WHITE)
                self.last_health = self.player.health
            
            if self.level != self.last_level:
                self.ui_texts['level'] = FONT_SMALL.render(f"Level: {self.level}", True, WHITE)
                self.last_level = self.level

    def draw_game(self):
        self.screen.fill(BLACK)
        self.player.draw(self.screen)
        
        # 绘制所有敌人
        for enemy in self.enemies:
            enemy.draw(self.screen)
        for enemy in self.white_enemies:
            enemy.draw(self.screen)
        
        # 绘制预渲染的UI文本
        if 'score' in self.ui_texts:
            self.screen.blit(self.ui_texts['score'], (10, 10))
        if 'health' in self.ui_texts:
            self.screen.blit(self.ui_texts['health'], (10, 40))
        if 'level' in self.ui_texts:
            self.screen.blit(self.ui_texts['level'], (10, 70))

    def draw_pause_screen(self):
        self.draw_game()
        overlay = pygame.Surface((WINDOW_WIDTH, WINDOW_HEIGHT))
        overlay.fill(BLACK)
        overlay.set_alpha(128)
        self.screen.blit(overlay, (0, 0))
        
        if 'pause' not in self.ui_texts:
            self.ui_texts['pause'] = FONT_LARGE.render("PAUSED", True, WHITE)
        
        text_rect = self.ui_texts['pause'].get_rect(center=(WINDOW_WIDTH/2, WINDOW_HEIGHT/2))
        self.screen.blit(self.ui_texts['pause'], text_rect)

    def draw_game_over_screen(self):
        self.draw_game()
        overlay = pygame.Surface((WINDOW_WIDTH, WINDOW_HEIGHT))
        overlay.fill(BLACK)
        overlay.set_alpha(128)
        self.screen.blit(overlay, (0, 0))
        
        if 'game_over' not in self.ui_texts or self.score != self.last_score:
            self.ui_texts['game_over'] = FONT_LARGE.render(f"GAME OVER - Score: {self.score}", True, RED)
            self.last_score = self.score
        
        text_rect = self.ui_texts['game_over'].get_rect(center=(WINDOW_WIDTH/2, WINDOW_HEIGHT/2))
        self.screen.blit(self.ui_texts['game_over'], text_rect)

    def draw(self):
        if self.game_state == "RUNNING":
            self.draw_game()
        elif self.game_state == "PAUSED":
            self.draw_pause_screen()
        elif self.game_state == "GAME_OVER":
            self.draw_game_over_screen()
            
        # 显示FPS (可选)
        current_time = pygame.time.get_ticks()
        if current_time - self.last_fps_update > 1000:  # 每秒更新一次
            fps = self.clock.get_fps()
            if 'fps' not in self.ui_texts or abs(fps - getattr(self, 'last_fps', 0)) > 1:
                self.ui_texts['fps'] = FONT_SMALL.render(f"FPS: {fps:.1f}", True, WHITE)
                self.last_fps = fps
            self.last_fps_update = current_time
        
        if 'fps' in self.ui_texts:
            self.screen.blit(self.ui_texts['fps'], (WINDOW_WIDTH - 120, 10))
        
        pygame.display.flip()

    def run(self):
        while self.running:
            self.handle_events()
            self.update()
            self.draw()
            self.clock.tick(FPS)

        pygame.quit()
        sys.exit()


if __name__ == "__main__":
    game = Game()
    game.run()
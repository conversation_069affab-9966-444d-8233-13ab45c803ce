<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <title>热烈地拥抱这个时代——致全校青年的一封信</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@400;700&display=swap" rel="stylesheet">
  <style>
    body {
      margin: 0;
      font-family: 'Roboto', 'PingFang SC', 'Hiragino Sans', Arial, sans-serif;
      background: #f5f7fa;
      color: #222;
      line-height: 1.6;
    }

    .navbar {
      background: linear-gradient(90deg, #174ea6 60%, #2196f3 100%);
      color: #fff;
      padding: 0 32px;
      display: flex;
      align-items: center;
      height: 60px;
      box-shadow: 0 2px 12px rgba(0,0,0,0.06);
      position: sticky;
      top: 0;
      z-index: 10;
      transition: all 0.3s ease;
    }

    .navbar.scrolled {
      background: rgba(23, 78, 166, 0.95);
      box-shadow: 0 2px 12px rgba(0,0,0,0.1);
    }

    .navbar .logo {
      font-weight: bold;
      font-size: 22px;
      margin-right: 36px;
      letter-spacing: 2px;
      transition: transform 0.3s ease;
    }

    .navbar .logo:hover {
      transform: scale(1.05);
    }

    .navbar a {
      color: #fff;
      text-decoration: none;
      margin: 0 14px;
      font-size: 1em;
      transition: all 0.2s ease;
      position: relative;
    }

    .navbar a:after {
      content: '';
      display: block;
      width: 0;
      height: 2px;
      background: #ffd600;
      transition: all 0.3s ease;
      position: absolute;
      left: 0;
      bottom: -4px;
    }

    .navbar a:hover {
      transform: translateY(-2px);
    }

    .navbar a:hover:after {
      width: 100%;
    }

    .container {
      max-width: 900px;
      margin: 40px auto 0;
      background: #fff;
      border-radius: 16px;
      box-shadow: 0 6px 32px rgba(33,150,243,0.08);
      padding: 48px 36px;
      transition: transform 0.3s ease;
    }

    .container:hover {
      transform: translateY(-5px);
    }

    .title {
      font-size: 2.4em;
      font-weight: 700;
      color: #174ea6;
      margin-bottom: 16px;
      letter-spacing: 1px;
      text-align: center;
    }

    .meta {
      color: #888;
      font-size: 1em;
      margin-bottom: 28px;
      text-align: center;
    }

    .content {
      line-height: 2.1;
      font-size: 1.18em;
      margin-bottom: 36px;
      letter-spacing: 0.2px;
    }

    .content p {
      margin: 1.5em 0;
      text-indent: 2em;
    }

    .recommend {
      margin: 48px 0 0;
      padding-top: 24px;
      border-top: 2px solid #f0f0f0;
    }

    .recommend-title {
      font-size: 1.25em;
      font-weight: 600;
      color: #174ea6;
      margin-bottom: 18px;
      letter-spacing: 1px;
    }

    .recommend-list {
      display: flex;
      flex-wrap: wrap;
      gap: 18px;
      justify-content: space-between;
    }

    .recommend-card {
      background: linear-gradient(135deg, #e3f2fd 60%, #fff 100%);
      border-radius: 10px;
      padding: 20px 22px;
      min-width: 220px;
      flex: 1;
      box-shadow: 0 2px 10px rgba(33,150,243,0.07);
      transition: all 0.3s ease;
      position: relative;
      overflow: hidden;
    }

    .recommend-card::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: linear-gradient(45deg, transparent, rgba(33,150,243,0.1), transparent);
      transform: translateX(-100%);
      transition: transform 0.6s ease;
    }

    .recommend-card:hover::before {
      transform: translateX(100%);
    }

    .recommend-card:hover {
      box-shadow: 0 6px 24px rgba(33,150,243,0.13);
      transform: translateY(-2px) scale(1.03);
    }

    .recommend-card a {
      color: #174ea6;
      text-decoration: none;
      font-weight: 500;
      font-size: 1.05em;
      transition: color 0.3s ease;
    }

    .recommend-card a:hover {
      color: #0d47a1;
      text-decoration: underline;
    }

    .footer {
      margin-top: 56px;
      background: linear-gradient(90deg, #174ea6 60%, #2196f3 100%);
      color: #fff;
      text-align: center;
      padding: 28px 0 16px;
      border-radius: 0 0 16px 16px;
      font-size: 1em;
    }

    .footer a {
      color: #ffd600;
      text-decoration: none;
      margin: 0 10px;
      transition: color 0.3s ease;
    }

    .footer a:hover {
      color: #fff;
    }

    .footer input[type='text'] {
      border: none;
      border-radius: 4px;
      padding: 7px 12px;
      margin-left: 8px;
      font-size: 1em;
      background: rgba(255,255,255,0.1);
      color: #fff;
      transition: background 0.3s ease;
    }

    .footer input[type='text']:focus {
      outline: none;
      background: rgba(255,255,255,0.2);
    }

    @media (max-width: 600px) {
      .container {
        padding: 18px 4px;
      }

      .recommend-list {
        flex-direction: column;
        gap: 12px;
      }

      .navbar {
        flex-wrap: wrap;
        height: auto;
        padding: 10px 8px;
      }

      .navbar .logo {
        margin-bottom: 8px;
        font-size: 18px;
      }

      .navbar a {
        margin: 0 8px;
        font-size: 0.9em;
      }

      .content p {
        margin: 1.2em 0;
        text-indent: 1.5em;
      }
    }

    /* Add smooth scrolling */
    html {
      scroll-behavior: smooth;
    }
  </style>
</head>
<body>
  <nav class="navbar" id="navbar">
    <span class="logo">西南石油大学新闻资讯</span>
    <a href="#" onclick="scrollToSection('home')">学校首页</a>
    <a href="#" onclick="scrollToSection('news')">本网首页</a>
    <a href="#" onclick="scrollToSection('contact')">联系我们</a>
    <a href="#" onclick="scrollToSection('news')">要闻</a>
    <a href="#" onclick="scrollToSection('news')">综合</a>
    <a href="#" onclick="scrollToSection('news')">基层</a>
    <a href="#" onclick="scrollToSection('news')">故事</a>
    <a href="#" onclick="scrollToSection('news')">师想</a>
    <a href="#" onclick="scrollToSection('news')">关注</a>
    <a href="#" onclick="scrollToSection('news')">微博</a>
    <a href="#" onclick="scrollToSection('news')">视频</a>
    <a href="#" onclick="scrollToSection('news')">校报</a>
    <a href="#" onclick="scrollToSection('news')">外宣</a>
    <a href="#" onclick="scrollToSection('news')">人物</a>
    <a href="#" onclick="scrollToSection('news')">校友</a>
    <a href="#" onclick="scrollToSection('news')">校园</a>
    <a href="#" onclick="scrollToSection('news')">讲堂</a>
    <a href="#" onclick="scrollToSection('news')">评论</a>
    <a href="#" onclick="scrollToSection('news')">微信</a>
    <a href="#" onclick="scrollToSection('news')">图片</a>
    <a href="#" onclick="scrollToSection('news')">文化</a>
  </nav>
  <div class="container">
    <div class="title">热烈地拥抱这个时代——致全校青年的一封信</div>
    <div class="meta">来源：党政办 团委　作者：团委　编辑：向发全　审核：覃吉春 肖友平　终审：杜鹏　日期：2025-05-02</div>
    <div class="content">
      <p>青年朋友们：</p>
      <p>五月的风，总是带着青春的气息。在这万物并秀的日子里，“五四”青年节如约而至。中国共产党西南石油大学委员会向你们致以热烈的祝贺和亲切的问候！期待你们以青春之名，融时代之潮，在百年未有之大变局中谱写属于西南石大青年的乐章。</p>
      <p>青年是最富生气的力量。“五四”运动时期，青年掀起伟大的爱国运动，唤起了民族觉醒。新中国成立时期，青年以“敢教日月换新天”的豪情铸就共和国脊梁。改革开放以来，青年承担先锋角色，以创新和担当持续重塑中国的发展高度，用代际接力诠释了伟大的“五四”精神。</p>
      <p>一代人有一代人的长征，一代人有一代人的使命，我们正在经历和见证一个前所未有的时代。</p>
      <p>‌这是科技革命与产业变革交织的时代。数字化与智能化时代已经到来，人工智能、大数据、新材料、新能源等技术迅猛发展，在各个领域大放异彩，日益改变着人们的工作模式、思维方式和生活方式。</p>
      <p>‌这是挑战与机遇并存的时代。‌世界正经历百年未有之大变局，人类面临诸多共同挑战，但和平与发展仍是时代主题，新兴市场国家和发展中国家正快速崛起，中国发展呈现出“风景这边独好”的局面，我们面临着难得的发展机遇。</p>
      <p>这是推进中华民族伟大复兴的时代。实现全体人民共同富裕，物质文明和精神文明协调发展，人与自然和谐共生，建成教育强国、科技强国、人才强国、文化强国、体育强国、健康中国，美好的蓝图在我们面前徐徐展开。</p>
      <p>‌这是呼唤创新与实干的时代。科技创新是当今时代的重大命题，是国际战略博弈的主战场。我们迫切需要实干精神，从“跟跑”“并跑”到“领跑”，实现高水平科技自立自强。</p>
      <p>为了迎接这个时代，一批国之重器建设加速推进。高能同步辐射光源、稳态强磁场实验装置、高效低碳燃气轮机等77个国家大科学装置已经投用和布局，推动了高能物理、生命科学、高温超导材料等领域的一批重大突破。</p>
      <p>这一切无不告诉我们，一个新的时代到来了，科学技术飞速发展，新生事物层出不穷，知识的生命周期大大缩短，劳动效率大大提高。不经意间，时代就可能抛下我们。</p>
      <p>青年朋友们，自从选择了西南石大，便选择了“为祖国加油，为民族争气”的使命。在这个充满变革与挑战的时代，石大青年应当何为？</p>
      <p>一是坚定理想信念。青年的理想信念关乎国家未来。青年理想远大、信念坚定，是一个国家、一个民族无坚不摧的前进动力。希望广大青年将个人理想融入国家能源安全、科技强国的战略需求，做“端牢能源饭碗”的践行者。</p>
      <p>二是培养敏锐的洞察力。要通过对现实的观察和深入思考，敏锐地捕捉世界的变化，特别是人工智能带来的机遇与挑战，从而找到自己在时代洪流中的位置。</p>
      <p>三是保持探索精神。既深耕专业，成为能源领域的“深地钻探者”；又跨界融合，在“能源+数字”“能源+生态”中开辟新赛道。不论是科技领域的探索，还是人文精神的深耕，希望你们一往无前、不怕艰难。‌</p>
      <p>四是练就过硬本领。希望你们扎根实验室、田野，深入丰富多彩的社会生活，在深层油气开发、储能技术等各个领域勇闯“无人区”，将论文写在保障国家能源安全的实践中。</p>
      <p>建校以来，历代西南石大学子、青年校友以兴油报国为使命，创造了骄人的业绩。“高校毕业生基层就业卓越奖学金”获得者朱松柏校友长年扎根一线，为3000万吨大油气田的建成做出了突出贡献；中国青年科技奖获得者卢聪教授带领团队攻克深层页岩气开采技术，将论文写在祖国大地上；全国劳动模范代娟校友、张金友校友咬住难题不放松，最终啃下硬骨头。他们用矢志奋斗的实际行动书写了科技强国、能源报国的青春华章。</p>
      <p>“世界是你们的，也是我们的，但是归根结底是你们的。你们青年人朝气蓬勃，正在兴旺时期，好像早晨八九点钟的太阳。希望寄托在你们身上。”毛泽东同志这段演讲几十年来鼓舞了无数中国青年，希望你们时刻铭记，汲取奋进力量。</p>
      <p>青年朋友们，属于你们的时代到来了。愿你们以“能源报国”的初心为帆，以“为祖国加油，为民族争气”的石大精神为舵，在时代的浪潮中奋力划桨！</p>
      <p>请张开双臂热烈地拥抱这个时代吧！</p>
      <p style="text-align:right;">中国共产党西南石油大学委员会<br>2025年5月2日</p>
    </div>
    <div class="recommend">
      <div class="recommend-title">推荐</div>
      <div class="recommend-list">
        <div class="recommend-card" onclick="scrollToSection('news')">
          <a href="#">【要闻】西南石油大学2025年新年贺词</a>
        </div>
        <div class="recommend-card" onclick="scrollToSection('news')">
          <a href="#">【校园】快来打乒乓球——记五星级社团教职工乒乓球协会</a>
        </div>
        <div class="recommend-card" onclick="scrollToSection('news')">
          <a href="#">【要闻】满眼都是你——图书馆服务读者纪实</a>
        </div>
        <div class="recommend-card" onclick="scrollToSection('news')">
          <a href="#">【视频】【校庆·老镜头】2002出品《翻开历史新一页——搬迁纪实》</a>
        </div>
        <div class="recommend-card" onclick="scrollToSection('news')">
          <a href="#">【视频】【校庆·老镜头】1993出品《龙井湖抒怀》</a>
        </div>
        <div class="recommend-card" onclick="scrollToSection('news')">
          <a href="#">【视频】【校庆·老镜头】1988出品《三十而立 力争一流》</a>
        </div>
        <div class="recommend-card" onclick="scrollToSection('news')">
          <a href="#">【要闻】学校出台进一步加强和改进新闻报道工作实施办法</a>
        </div>
        <div class="recommend-card" onclick="scrollToSection('news')">
          <a href="#">【评论】【校园评论】以高质量抓落实促高质量发展</a>
        </div>
        <div class="recommend-card" onclick="scrollToSection('news')">
          <a href="#">【讲堂】学有佳境无止境 教有良法无定法</a>
        </div>
      </div>
    </div>
  </div>
  <footer class="footer">
    <div>
      <a href="#" onclick="scrollToSection('about')">关于本站</a> | 
      <a href="#" onclick="scrollToSection('contact')">联系我们</a>
    </div>
    <div style="margin:12px 0;">
      版权所有© 西南石油大学新闻中心 2019-2021
    </div>
    <div>
      请输入搜索信息：<input type="text" placeholder="搜索..." oninput="searchContent(this.value)">
    </div>
  </footer>
  <script>
    // Navbar scroll effect
    window.addEventListener('scroll', function() {
      const navbar = document.getElementById('navbar');
      if (window.scrollY > 50) {
        navbar.classList.add('scrolled');
      } else {
        navbar.classList.remove('scrolled');
      }
    });

    // Smooth scrolling to sections
    function scrollToSection(section) {
      const element = document.querySelector(`#${section}`);
      if (element) {
        element.scrollIntoView({ behavior: 'smooth' });
      }
    }

    // Search functionality (basic implementation)
    function searchContent(query) {
      const content = document.querySelector('.content');
      const paragraphs = content.querySelectorAll('p');
      
      paragraphs.forEach(p => {
        if (p.textContent.toLowerCase().includes(query.toLowerCase())) {
          p.style.backgroundColor = '#e3f2fd';
        } else {
          p.style.backgroundColor = 'transparent';
        }
      });
    }
  </script>
</body>
</html>
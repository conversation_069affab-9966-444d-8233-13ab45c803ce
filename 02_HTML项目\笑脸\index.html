<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>真正的3D笑脸球</title>
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      font-family: 'Arial', sans-serif;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      min-height: 100vh;
      overflow-x: hidden;
      color: white;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
    }

    .title {
      font-size: 2.5rem;
      margin-bottom: 10px;
      text-align: center;
      text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
      background: linear-gradient(45deg, #ffd700, #ffeb3b, #ffc107);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
    }

    .instruction {
      font-size: 1.2rem;
      margin-bottom: 30px;
      text-align: center;
      opacity: 0.9;
    }

    #container {
      width: 500px;
      height: 500px;
      position: relative;
    }

    #emoji-fireworks {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      pointer-events: none;
      z-index: 1000;
    }

    .instructions {
      margin-top: 30px;
      text-align: center;
      opacity: 0.8;
    }

    .instructions p {
      margin: 8px 0;
      font-size: 1rem;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 10px;
    }

    .instructions strong {
      color: #ffd700;
      text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
    }

    .firework {
      position: absolute;
      pointer-events: none;
    }

    .firework-particle {
      position: absolute;
      width: 4px;
      height: 4px;
      border-radius: 50%;
      animation: firework-explosion 1s ease-out forwards;
    }

    @keyframes firework-explosion {
      0% {
        transform: scale(1);
        opacity: 1;
      }
      100% {
        transform: scale(0);
        opacity: 0;
      }
    }
  </style>
</head>
<body>
  <h1 class="title">🌟 真正的3D笑脸球 🌟</h1>
  <p class="instruction">拖动旋转3D球体，按住蓄力发射！</p>

  <div id="container"></div>
  <div id="emoji-fireworks"></div>

  <div class="instructions">
    <p>🖱️ <strong>拖拽球体</strong> - 真正的3D旋转</p>
    <p>🎯 <strong>按住蓄力</strong> - 控制发射方向和力度</p>
    <p>⚡ <strong>力度指示</strong> - 左上角显示蓄力程度</p>
    <p>🎱 <strong>双击球体</strong> - 随机方向弹跳</p>
    <p>✨ <strong>点击空白</strong> - 随机表情烟花</p>
    <p>🟢 <strong>绿色边框</strong> - 台球桌边界</p>
  </div>

  <script src="https://cdn.jsdelivr.net/npm/three@0.152.2/build/three.min.js"></script>
  <script src="simple-sphere.js"></script>
</body>
</html>

// 等待页面加载完成
window.addEventListener('load', function() {
  initSmileyFace();
});

// 笑脸表情状态
const expressions = [
  {
    name: 'happy',
    mouth: { borderRadius: '0 0 160px 160px', transform: 'translateX(-50%) translateZ(10px) scaleY(1)' },
    eyes: 'normal',
    cheeks: 'normal'
  },
  {
    name: 'sad',
    mouth: { borderRadius: '160px 160px 0 0', transform: 'translateX(-50%) translateZ(10px) scaleY(0.7) rotateX(180deg)' },
    eyes: 'sad',
    cheeks: 'pale'
  },
  {
    name: 'wink',
    mouth: { borderRadius: '0 0 80px 80px', transform: 'translateX(-50%) translateZ(10px) scaleY(0.6)' },
    eyes: 'wink',
    cheeks: 'blush'
  },
  {
    name: 'surprised',
    mouth: { borderRadius: '50%', transform: 'translateX(-50%) translateZ(10px) scaleY(1.2) scaleX(0.6)' },
    eyes: 'surprised',
    cheeks: 'normal'
  },
  {
    name: 'sleepy',
    mouth: { borderRadius: '0 0 80px 80px', transform: 'translateX(-50%) translateZ(10px) scaleY(0.4)' },
    eyes: 'sleepy',
    cheeks: 'pale'
  },
  {
    name: 'laughing',
    mouth: { borderRadius: '0 0 200px 200px', transform: 'translateX(-50%) translateZ(10px) scaleY(1.3)' },
    eyes: 'laughing',
    cheeks: 'blush'
  }
];

let currentExpression = 0;

// 3D旋转相关变量
let isDragging = false;
let lastMouseX = 0;
let lastMouseY = 0;
let rotationX = 0;
let rotationY = 0;
let velocityX = 0;
let velocityY = 0;
let animationId = null;

function initSmileyFace() {
  const smileyFace = document.getElementById('smiley-face');

  // 笑脸点击事件 - 改变表情
  smileyFace.addEventListener('click', function(e) {
    if (!isDragging) {
      e.stopPropagation();
      changeExpression();
      createClickEffect(e.clientX, e.clientY);
    }
  });

  // 3D拖拽旋转事件
  smileyFace.addEventListener('mousedown', startDrag);
  document.addEventListener('mousemove', drag);
  document.addEventListener('mouseup', stopDrag);

  // 触摸事件支持
  smileyFace.addEventListener('touchstart', startDragTouch);
  document.addEventListener('touchmove', dragTouch);
  document.addEventListener('touchend', stopDrag);

  // 开始惯性旋转
  startInertiaRotation();
}

function changeExpression() {
  currentExpression = (currentExpression + 1) % expressions.length;
  const expr = expressions[currentExpression];

  const mouth = document.querySelector('.mouth');
  const leftEye = document.querySelector('.left-eye');
  const rightEye = document.querySelector('.right-eye');
  const leftCheek = document.querySelector('.left-cheek');
  const rightCheek = document.querySelector('.right-cheek');

  // 改变嘴巴形状和变换
  mouth.style.borderRadius = expr.mouth.borderRadius;
  mouth.style.transform = expr.mouth.transform;

  // 改变眼睛状态
  switch(expr.eyes) {
    case 'wink':
      leftEye.style.height = '8px';
      leftEye.style.transform = 'translateZ(30px) scaleY(0.1)';
      rightEye.style.height = '70px';
      rightEye.style.transform = 'translateZ(30px) scaleY(1)';
      break;
    case 'surprised':
      leftEye.style.width = '90px';
      leftEye.style.height = '90px';
      leftEye.style.transform = 'translateZ(30px) scaleY(1.3)';
      rightEye.style.width = '90px';
      rightEye.style.height = '90px';
      rightEye.style.transform = 'translateZ(30px) scaleY(1.3)';
      break;
    case 'sleepy':
      leftEye.style.height = '15px';
      leftEye.style.transform = 'translateZ(30px) scaleY(0.2)';
      rightEye.style.height = '15px';
      rightEye.style.transform = 'translateZ(30px) scaleY(0.2)';
      break;
    case 'sad':
      leftEye.style.width = '70px';
      leftEye.style.height = '70px';
      leftEye.style.transform = 'translateZ(30px) scaleY(1) rotateZ(10deg)';
      rightEye.style.width = '70px';
      rightEye.style.height = '70px';
      rightEye.style.transform = 'translateZ(30px) scaleY(1) rotateZ(-10deg)';
      break;
    case 'laughing':
      leftEye.style.height = '20px';
      leftEye.style.transform = 'translateZ(30px) scaleY(0.25) rotateZ(-5deg)';
      rightEye.style.height = '20px';
      rightEye.style.transform = 'translateZ(30px) scaleY(0.25) rotateZ(5deg)';
      break;
    default:
      leftEye.style.width = '70px';
      leftEye.style.height = '70px';
      leftEye.style.transform = 'translateZ(30px) scaleY(1)';
      rightEye.style.width = '70px';
      rightEye.style.height = '70px';
      rightEye.style.transform = 'translateZ(30px) scaleY(1)';
  }

  // 改变脸颊状态
  switch(expr.cheeks) {
    case 'blush':
      leftCheek.style.opacity = '1';
      rightCheek.style.opacity = '1';
      leftCheek.style.transform = 'translateZ(25px) scale(1.4)';
      rightCheek.style.transform = 'translateZ(25px) scale(1.4)';
      break;
    case 'pale':
      leftCheek.style.opacity = '0.3';
      rightCheek.style.opacity = '0.3';
      leftCheek.style.transform = 'translateZ(25px) scale(0.6)';
      rightCheek.style.transform = 'translateZ(25px) scale(0.6)';
      break;
    default:
      leftCheek.style.opacity = '0.7';
      rightCheek.style.opacity = '0.7';
      leftCheek.style.transform = 'translateZ(25px) scale(1)';
      rightCheek.style.transform = 'translateZ(25px) scale(1)';
  }

  // 添加表情变化动画和特效
  const smileyFace = document.getElementById('smiley-face');
  smileyFace.style.animation = 'none';
  smileyFace.classList.add('expression-change');

  setTimeout(() => {
    smileyFace.classList.remove('expression-change');
    if (!isDragging) {
      smileyFace.style.animation = 'float 3s ease-in-out infinite';
    }
  }, 600);
}

// 3D拖拽功能
function startDrag(e) {
  isDragging = true;
  lastMouseX = e.clientX;
  lastMouseY = e.clientY;

  const smileyFace = document.getElementById('smiley-face');
  smileyFace.style.animation = 'none';

  if (animationId) {
    cancelAnimationFrame(animationId);
  }

  e.preventDefault();
}

function drag(e) {
  if (!isDragging) return;

  const deltaX = e.clientX - lastMouseX;
  const deltaY = e.clientY - lastMouseY;

  velocityX = deltaX * 0.5;
  velocityY = deltaY * 0.5;

  rotationY += deltaX * 0.5;
  rotationX -= deltaY * 0.5;

  // 限制旋转角度
  rotationX = Math.max(-90, Math.min(90, rotationX));

  updateSmileyTransform();

  lastMouseX = e.clientX;
  lastMouseY = e.clientY;
}

function stopDrag() {
  if (!isDragging) return;
  isDragging = false;

  // 开始惯性旋转
  startInertiaRotation();
}

// 触摸事件处理
function startDragTouch(e) {
  const touch = e.touches[0];
  startDrag({
    clientX: touch.clientX,
    clientY: touch.clientY,
    preventDefault: () => e.preventDefault()
  });
}

function dragTouch(e) {
  if (!isDragging) return;
  const touch = e.touches[0];
  drag({
    clientX: touch.clientX,
    clientY: touch.clientY
  });
  e.preventDefault();
}

// 更新笑脸3D变换
function updateSmileyTransform() {
  const smileyFace = document.getElementById('smiley-face');
  smileyFace.style.transform = `rotateX(${rotationX}deg) rotateY(${rotationY}deg)`;
}

// 惯性旋转
function startInertiaRotation() {
  function animate() {
    if (isDragging) return;

    // 应用惯性
    rotationY += velocityX;
    rotationX += velocityY;

    // 摩擦力
    velocityX *= 0.95;
    velocityY *= 0.95;

    // 限制旋转角度
    rotationX = Math.max(-90, Math.min(90, rotationX));

    updateSmileyTransform();

    // 如果速度足够小，停止动画
    if (Math.abs(velocityX) > 0.1 || Math.abs(velocityY) > 0.1) {
      animationId = requestAnimationFrame(animate);
    } else {
      // 恢复浮动动画
      const smileyFace = document.getElementById('smiley-face');
      smileyFace.style.animation = 'float 3s ease-in-out infinite';
    }
  }

  animationId = requestAnimationFrame(animate);
}

function createClickEffect(x, y) {
  const effect = document.createElement('div');
  effect.style.cssText = `
    position: fixed;
    left: ${x}px;
    top: ${y}px;
    width: 20px;
    height: 20px;
    background: radial-gradient(circle, #ffeb3b, transparent);
    border-radius: 50%;
    pointer-events: none;
    z-index: 1000;
    animation: clickEffect 0.6s ease-out forwards;
  `;

  document.body.appendChild(effect);

  setTimeout(() => {
    effect.remove();
  }, 600);
}

// 添加点击效果动画
const style = document.createElement('style');
style.textContent = `
  @keyframes clickEffect {
    0% {
      transform: translate(-50%, -50%) scale(0);
      opacity: 1;
    }
    100% {
      transform: translate(-50%, -50%) scale(3);
      opacity: 0;
    }
  }
`;
document.head.appendChild(style);

// --- 表情烟花系统 ---
const EMOJIS = [
  '😂', '😍', '😎', '🥳', '😱', '😜', '🤩', '🥰', '😏', '😭',
  '😇', '😡', '😳', '🤔', '😋', '😝', '🤗', '😶', '🙃', '😘',
  '🌟', '✨', '💫', '⭐', '🎉', '🎊', '💖', '💝', '🎈', '🌈'
];

const fireworks = document.getElementById('emoji-fireworks');

function emojiFirework(x, y) {
  // 创建更多烟花效果
  const count = 4 + Math.floor(Math.random() * 3);
  for (let i = 0; i < count; i++) {
    const angle = (Math.PI * 2 * i) / count + Math.random() * 0.5;
    const dist = 80 + Math.random() * 60;
    const ex = x + Math.cos(angle) * dist;
    const ey = y + Math.sin(angle) * dist;
    const emoji = EMOJIS[Math.floor(Math.random() * EMOJIS.length)];

    setTimeout(() => {
      createEmojiBurst(emoji, x, y, ex, ey);
    }, i * 50);
  }
}

function createEmojiBurst(emoji, x0, y0, x1, y1) {
  const el = document.createElement('div');
  el.className = 'emoji-burst';
  el.textContent = emoji;
  el.style.position = 'fixed'; // 使用fixed定位以适应页面坐标
  el.style.left = x0 + 'px';
  el.style.top = y0 + 'px';
  el.style.transform = 'translate(-50%, -50%) scale(0.5)';
  el.style.opacity = '1';
  el.style.pointerEvents = 'none';
  el.style.zIndex = '1000';
  el.style.fontSize = '2.2rem';
  document.body.appendChild(el); // 直接添加到body而不是fireworks容器

  // 添加随机旋转
  const rotation = Math.random() * 720 - 360;

  setTimeout(() => {
    el.style.transform = `translate(${x1-x0-25}px, ${y1-y0-25}px) scale(1.8) rotate(${rotation}deg)`;
    el.style.opacity = '0';
  }, 50);

  setTimeout(() => {
    el.remove();
  }, 1500);
}

// 点击事件监听器 - 扩大到整个页面
document.addEventListener('click', function(e) {
  // 如果点击的不是笑脸本身，则触发烟花效果
  if (!e.target.closest('.smiley-face') && !isDragging) {
    // 使用页面坐标直接触发烟花
    emojiFirework(e.clientX, e.clientY);
  }
});

// 键盘事件
document.addEventListener('keydown', function(e) {
  switch(e.code) {
    case 'Space':
      e.preventDefault();
      // 随机位置烟花 - 在整个页面范围内
      const x = Math.random() * window.innerWidth;
      const y = Math.random() * window.innerHeight;
      emojiFirework(x, y);
      break;

    case 'Enter':
      e.preventDefault();
      // 改变笑脸表情
      changeExpression();
      break;

    case 'KeyF':
      e.preventDefault();
      // 在笑脸周围创建烟花圈
      createFireworkCircle();
      break;
  }
});

// 创建烟花圈效果
function createFireworkCircle() {
  const container = document.getElementById('container');
  const rect = container.getBoundingClientRect();
  const centerX = rect.left + rect.width / 2;
  const centerY = rect.top + rect.height / 2;
  const radius = 200;

  for (let i = 0; i < 8; i++) {
    const angle = (Math.PI * 2 * i) / 8;
    const x = centerX + Math.cos(angle) * radius;
    const y = centerY + Math.sin(angle) * radius;

    setTimeout(() => {
      emojiFirework(x, y);
    }, i * 100);
  }
}

// 添加双击事件 - 创建超级烟花
document.addEventListener('dblclick', function(e) {
  // 如果双击的不是笑脸，创建超级烟花效果
  if (!e.target.closest('.smiley-face')) {
    const x = e.clientX;
    const y = e.clientY;

    // 创建超级烟花效果
    for (let i = 0; i < 3; i++) {
      setTimeout(() => {
        emojiFirework(x + (Math.random() - 0.5) * 50, y + (Math.random() - 0.5) * 50);
      }, i * 200);
    }
  }
});
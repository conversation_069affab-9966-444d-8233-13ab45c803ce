// 3D球体笑脸交互脚本

class Sphere3D {
  constructor() {
    this.sphereContainer = document.getElementById('sphereContainer');
    this.sphere = document.getElementById('sphere');
    this.isDragging = false;
    this.previousMousePosition = { x: 0, y: 0 };
    this.rotation = { x: 0, y: 0 };
    this.velocity = { x: 0, y: 0 };
    this.damping = 0.95;

    this.init();
  }

  init() {
    this.setupEventListeners();
    this.startAnimation();
  }

  setupEventListeners() {
    // 鼠标事件
    this.sphereContainer.addEventListener('mousedown', this.onMouseDown.bind(this));
    document.addEventListener('mousemove', this.onMouseMove.bind(this));
    document.addEventListener('mouseup', this.onMouseUp.bind(this));

    // 触摸事件
    this.sphereContainer.addEventListener('touchstart', this.onTouchStart.bind(this));
    document.addEventListener('touchmove', this.onTouchMove.bind(this));
    document.addEventListener('touchend', this.onTouchEnd.bind(this));

    // 点击切换表情
    this.sphereContainer.addEventListener('click', this.changeExpression.bind(this));

    // 键盘事件
    document.addEventListener('keydown', this.onKeyDown.bind(this));

    // 页面点击烟花
    document.addEventListener('click', this.createFireworks.bind(this));
  }

  onMouseDown(event) {
    this.isDragging = true;
    this.previousMousePosition = {
      x: event.clientX,
      y: event.clientY
    };
    this.sphereContainer.style.cursor = 'grabbing';
    event.preventDefault();
  }

  onMouseMove(event) {
    if (!this.isDragging) return;

    const deltaMove = {
      x: event.clientX - this.previousMousePosition.x,
      y: event.clientY - this.previousMousePosition.y
    };

    this.velocity.x = deltaMove.y * 0.5;
    this.velocity.y = deltaMove.x * 0.5;

    this.previousMousePosition = {
      x: event.clientX,
      y: event.clientY
    };
  }

  onMouseUp() {
    this.isDragging = false;
    this.sphereContainer.style.cursor = 'grab';
  }

  onTouchStart(event) {
    if (event.touches.length === 1) {
      this.isDragging = true;
      this.previousMousePosition = {
        x: event.touches[0].clientX,
        y: event.touches[0].clientY
      };
      event.preventDefault();
    }
  }

  onTouchMove(event) {
    if (!this.isDragging || event.touches.length !== 1) return;

    const deltaMove = {
      x: event.touches[0].clientX - this.previousMousePosition.x,
      y: event.touches[0].clientY - this.previousMousePosition.y
    };

    this.velocity.x = deltaMove.y * 0.5;
    this.velocity.y = deltaMove.x * 0.5;

    this.previousMousePosition = {
      x: event.touches[0].clientX,
      y: event.touches[0].clientY
    };

    event.preventDefault();
  }

  onTouchEnd() {
    this.isDragging = false;
  }

  startAnimation() {
    const animate = () => {
      // 应用惯性
      if (!this.isDragging) {
        this.velocity.x *= this.damping;
        this.velocity.y *= this.damping;
      }

      // 更新旋转
      this.rotation.x += this.velocity.x;
      this.rotation.y += this.velocity.y;

      // 应用变换
      this.sphereContainer.style.transform = 
        `rotateX(${this.rotation.x}deg) rotateY(${this.rotation.y}deg)`;

      requestAnimationFrame(animate);
    };

    animate();
  }

  changeExpression() {
    const expressions = [
      { name: 'happy', eyes: 'normal', mouth: 'smile', cheeks: 'normal' },
      { name: 'wink', eyes: 'wink', mouth: 'smile', cheeks: 'blush' },
      { name: 'surprised', eyes: 'surprised', mouth: 'open', cheeks: 'pale' },
      { name: 'sleepy', eyes: 'sleepy', mouth: 'small', cheeks: 'normal' },
      { name: 'sad', eyes: 'sad', mouth: 'frown', cheeks: 'pale' },
      { name: 'laughing', eyes: 'laughing', mouth: 'big-smile', cheeks: 'blush' }
    ];

    const randomExpression = expressions[Math.floor(Math.random() * expressions.length)];
    this.applyExpression(randomExpression);
  }

  applyExpression(expr) {
    const leftEye = document.querySelector('.left-eye');
    const rightEye = document.querySelector('.right-eye');
    const mouth = document.querySelector('.mouth');
    const leftCheek = document.querySelector('.left-cheek');
    const rightCheek = document.querySelector('.right-cheek');

    // 重置所有样式
    this.resetFeatures();

    // 应用眼睛表情
    switch(expr.eyes) {
      case 'wink':
        leftEye.style.height = '8px';
        leftEye.style.transform = 'translateZ(40px) scaleY(0.1)';
        break;
      case 'surprised':
        leftEye.style.width = '90px';
        leftEye.style.height = '90px';
        rightEye.style.width = '90px';
        rightEye.style.height = '90px';
        break;
      case 'sleepy':
        leftEye.style.height = '15px';
        rightEye.style.height = '15px';
        leftEye.style.transform = 'translateZ(40px) scaleY(0.2)';
        rightEye.style.transform = 'translateZ(40px) scaleY(0.2)';
        break;
      case 'sad':
        leftEye.style.transform = 'translateZ(40px) rotateZ(10deg)';
        rightEye.style.transform = 'translateZ(40px) rotateZ(-10deg)';
        break;
      case 'laughing':
        leftEye.style.height = '20px';
        rightEye.style.height = '20px';
        leftEye.style.transform = 'translateZ(40px) scaleY(0.25) rotateZ(-5deg)';
        rightEye.style.transform = 'translateZ(40px) scaleY(0.25) rotateZ(5deg)';
        break;
    }

    // 应用嘴巴表情
    switch(expr.mouth) {
      case 'smile':
        mouth.style.width = '120px';
        mouth.style.height = '60px';
        break;
      case 'big-smile':
        mouth.style.width = '140px';
        mouth.style.height = '70px';
        break;
      case 'open':
        mouth.style.width = '80px';
        mouth.style.height = '80px';
        mouth.style.borderRadius = '50%';
        break;
      case 'small':
        mouth.style.width = '60px';
        mouth.style.height = '30px';
        break;
      case 'frown':
        mouth.style.transform = 'translateX(-50%) translateZ(35px) rotateZ(180deg)';
        break;
    }

    // 应用脸颊表情
    switch(expr.cheeks) {
      case 'blush':
        leftCheek.style.opacity = '1';
        rightCheek.style.opacity = '1';
        leftCheek.style.transform = 'translateZ(30px) scale(1.4)';
        rightCheek.style.transform = 'translateZ(30px) scale(1.4)';
        break;
      case 'pale':
        leftCheek.style.opacity = '0.3';
        rightCheek.style.opacity = '0.3';
        break;
    }
  }

  resetFeatures() {
    const features = document.querySelectorAll('.eye, .mouth, .cheek');
    features.forEach(feature => {
      feature.style.width = '';
      feature.style.height = '';
      feature.style.transform = '';
      feature.style.opacity = '';
      feature.style.borderRadius = '';
    });
  }

  onKeyDown(event) {
    switch(event.code) {
      case 'Space':
        this.createRandomFireworks();
        event.preventDefault();
        break;
      case 'KeyF':
        this.createFireworkCircle();
        event.preventDefault();
        break;
    }
  }

  createFireworks(event) {
    if (event.target === this.sphereContainer || event.target.closest('.sphere-container')) {
      return; // 不在球体上创建烟花
    }

    const x = event.clientX;
    const y = event.clientY;
    this.createFireworkAt(x, y);
  }

  createFireworkAt(x, y) {
    const firework = document.createElement('div');
    firework.className = 'firework';
    firework.style.left = x + 'px';
    firework.style.top = y + 'px';
    
    const emojis = ['😊', '😄', '😆', '😂', '🤣', '😍', '🥰', '😘', '🤗', '🎉', '🎊', '✨', '💫', '⭐', '🌟'];
    const emoji = emojis[Math.floor(Math.random() * emojis.length)];
    
    for (let i = 0; i < 12; i++) {
      const particle = document.createElement('span');
      particle.textContent = emoji;
      particle.className = 'firework-particle';
      particle.style.setProperty('--angle', `${i * 30}deg`);
      particle.style.setProperty('--distance', `${50 + Math.random() * 100}px`);
      firework.appendChild(particle);
    }
    
    document.body.appendChild(firework);
    
    setTimeout(() => {
      firework.remove();
    }, 2000);
  }

  createRandomFireworks() {
    for (let i = 0; i < 5; i++) {
      setTimeout(() => {
        const x = Math.random() * window.innerWidth;
        const y = Math.random() * window.innerHeight;
        this.createFireworkAt(x, y);
      }, i * 200);
    }
  }

  createFireworkCircle() {
    const centerX = window.innerWidth / 2;
    const centerY = window.innerHeight / 2;
    const radius = 200;
    
    for (let i = 0; i < 8; i++) {
      setTimeout(() => {
        const angle = (i / 8) * Math.PI * 2;
        const x = centerX + Math.cos(angle) * radius;
        const y = centerY + Math.sin(angle) * radius;
        this.createFireworkAt(x, y);
      }, i * 100);
    }
  }
}

// 初始化
document.addEventListener('DOMContentLoaded', () => {
  new Sphere3D();
});

// 添加烟花样式
const style = document.createElement('style');
style.textContent = `
  .firework {
    position: fixed;
    pointer-events: none;
    z-index: 1000;
  }
  
  .firework-particle {
    position: absolute;
    font-size: 20px;
    animation: firework-explode 2s ease-out forwards;
    transform-origin: center;
  }
  
  @keyframes firework-explode {
    0% {
      transform: translate(0, 0) scale(0) rotate(0deg);
      opacity: 1;
    }
    50% {
      transform: translate(
        calc(cos(var(--angle)) * var(--distance)),
        calc(sin(var(--angle)) * var(--distance))
      ) scale(1.5) rotate(180deg);
      opacity: 1;
    }
    100% {
      transform: translate(
        calc(cos(var(--angle)) * var(--distance)),
        calc(sin(var(--angle)) * var(--distance))
      ) scale(0) rotate(360deg);
      opacity: 0;
    }
  }
`;
document.head.appendChild(style);

// 简化的3D球体笑脸 - 确保基本功能工作
class SimpleSphere3D {
  constructor() {
    this.scene = null;
    this.camera = null;
    this.renderer = null;
    this.sphere = null;
    this.isDragging = false;
    this.previousMousePosition = { x: 0, y: 0 };

    // 台球物理系统
    this.physics = {
      isActive: false,
      velocity: { x: 0, y: 0 },
      position: { x: 0, y: 0 },
      friction: 0.98,
      gravity: 0.0005,
      bounceStrength: 0.8,
      sphereRadius: 1,
      containerWidth: 500,
      containerHeight: 500
    };

    // 力度控制系统
    this.forceControl = {
      isCharging: false,
      startTime: 0,
      maxForce: 0.15,
      direction: { x: 0, y: 0 },
      chargeDuration: 2000 // 2秒充满力度
    };

    this.init();
    this.setupEvents();
    this.animate();
  }

  init() {
    const container = document.getElementById('container');
    
    // 创建场景
    this.scene = new THREE.Scene();
    
    // 创建相机
    this.camera = new THREE.PerspectiveCamera(75, 500/500, 0.1, 1000);
    this.camera.position.z = 3;
    
    // 创建渲染器
    this.renderer = new THREE.WebGLRenderer({ antialias: true, alpha: true });
    this.renderer.setSize(500, 500);
    this.renderer.setClearColor(0x000000, 0);
    this.renderer.shadowMap.enabled = true;
    this.renderer.shadowMap.type = THREE.PCFSoftShadowMap;
    container.appendChild(this.renderer.domElement);
    
    // 创建球体
    const geometry = new THREE.SphereGeometry(1, 32, 32);
    
    // 创建正确的球体笑脸贴图 - 使用正方形纹理
    const canvas = document.createElement('canvas');
    canvas.width = 512;
    canvas.height = 512; // 改为正方形，避免拉伸
    const ctx = canvas.getContext('2d');

    // 绘制黄色背景
    ctx.fillStyle = '#fdd835';
    ctx.fillRect(0, 0, 512, 512);

    // 只在中心区域绘制笑脸（球体正面）
    const centerX = 256;
    const centerY = 256;

    // 绘制眼睛
    ctx.fillStyle = '#2c2c2c';

    // 左眼
    ctx.beginPath();
    ctx.arc(centerX - 60, centerY - 40, 25, 0, Math.PI * 2);
    ctx.fill();

    // 右眼
    ctx.beginPath();
    ctx.arc(centerX + 60, centerY - 40, 25, 0, Math.PI * 2);
    ctx.fill();

    // 眼睛高光
    ctx.fillStyle = 'rgba(255, 255, 255, 0.9)';
    ctx.beginPath();
    ctx.arc(centerX - 70, centerY - 50, 8, 0, Math.PI * 2);
    ctx.fill();
    ctx.beginPath();
    ctx.arc(centerX + 50, centerY - 50, 8, 0, Math.PI * 2);
    ctx.fill();

    // 绘制笑容
    ctx.strokeStyle = '#2c2c2c';
    ctx.lineWidth = 12;
    ctx.lineCap = 'round';
    ctx.beginPath();
    ctx.arc(centerX, centerY + 30, 80, 0.2, Math.PI - 0.2);
    ctx.stroke();
    
    const texture = new THREE.CanvasTexture(canvas);
    texture.wrapS = THREE.RepeatWrapping;
    texture.wrapT = THREE.ClampToEdgeWrapping;

    // 使用更好的材质以获得3D效果
    const material = new THREE.MeshPhongMaterial({
      map: texture,
      shininess: 80,
      specular: 0x444444
    });

    this.sphere = new THREE.Mesh(geometry, material);
    this.sphere.castShadow = true;
    this.sphere.receiveShadow = true;
    this.scene.add(this.sphere);

    // 改进的光照系统
    // 环境光
    const ambientLight = new THREE.AmbientLight(0x404040, 0.6);
    this.scene.add(ambientLight);

    // 主光源
    const directionalLight = new THREE.DirectionalLight(0xffffff, 1);
    directionalLight.position.set(5, 5, 5);
    directionalLight.castShadow = true;
    this.scene.add(directionalLight);

    // 补光
    const fillLight = new THREE.DirectionalLight(0x8888ff, 0.3);
    fillLight.position.set(-3, -3, -3);
    this.scene.add(fillLight);

    // 添加台球桌边界
    this.createTableBounds();

    console.log('SimpleSphere3D initialized');
  }

  createTableBounds() {
    // 创建台球桌边界线框
    const boundaryGeometry = new THREE.EdgesGeometry(new THREE.BoxGeometry(5, 5, 0.1));
    const boundaryMaterial = new THREE.LineBasicMaterial({
      color: 0x00ff00,
      opacity: 0.3,
      transparent: true
    });
    const boundaryLines = new THREE.LineSegments(boundaryGeometry, boundaryMaterial);
    boundaryLines.position.z = -0.5;
    this.scene.add(boundaryLines);

    // 添加台球桌面
    const tableGeometry = new THREE.PlaneGeometry(5, 5);
    const tableMaterial = new THREE.MeshPhongMaterial({
      color: 0x0d5016,
      opacity: 0.1,
      transparent: true
    });
    const table = new THREE.Mesh(tableGeometry, tableMaterial);
    table.position.z = -0.6;
    this.scene.add(table);
  }

  setupEvents() {
    const canvas = this.renderer.domElement;
    canvas.style.cursor = 'grab';

    canvas.addEventListener('mousedown', (e) => {
      console.log('Mouse down');

      // 如果物理系统未激活，开始蓄力
      if (!this.physics.isActive) {
        this.forceControl.isCharging = true;
        this.forceControl.startTime = Date.now();

        // 计算方向（从球心指向鼠标）
        const rect = canvas.getBoundingClientRect();
        const mouseX = ((e.clientX - rect.left) / rect.width) * 2 - 1;
        const mouseY = -((e.clientY - rect.top) / rect.height) * 2 + 1;

        this.forceControl.direction = { x: mouseX, y: mouseY };
        console.log('开始蓄力，方向:', this.forceControl.direction);
      } else {
        // 如果物理系统激活，进行拖拽旋转
        this.isDragging = true;
        this.previousMousePosition = { x: e.clientX, y: e.clientY };
        canvas.style.cursor = 'grabbing';
      }
    });

    document.addEventListener('mousemove', (e) => {
      if (!this.isDragging) return;

      const deltaX = e.clientX - this.previousMousePosition.x;
      const deltaY = e.clientY - this.previousMousePosition.y;

      this.sphere.rotation.y += deltaX * 0.01;
      this.sphere.rotation.x += deltaY * 0.01;

      this.previousMousePosition = { x: e.clientX, y: e.clientY };
      console.log('Rotating');
    });

    document.addEventListener('mouseup', () => {
      // 处理蓄力发射
      if (this.forceControl.isCharging) {
        const chargeDuration = Date.now() - this.forceControl.startTime;
        const forceRatio = Math.min(chargeDuration / this.forceControl.chargeDuration, 1);
        const force = forceRatio * this.forceControl.maxForce;

        // 启动物理系统
        this.physics.isActive = true;
        this.physics.position = { x: this.sphere.position.x, y: this.sphere.position.y };

        this.physics.velocity = {
          x: this.forceControl.direction.x * force,
          y: this.forceControl.direction.y * force
        };

        this.forceControl.isCharging = false;
        console.log('发射！力度:', forceRatio.toFixed(2), '速度:', this.physics.velocity);
      }

      // 处理拖拽结束
      if (this.isDragging) {
        this.isDragging = false;
        canvas.style.cursor = 'grab';
        console.log('Mouse up');
      }
    });

    // 双击球体启动台球物理
    canvas.addEventListener('dblclick', (e) => {
      e.preventDefault();
      this.startBallPhysics();
      console.log('Double click - 台球启动!');
    });

    // 点击空白处显示随机表情烟花
    document.addEventListener('click', (e) => {
      if (e.target !== canvas && !this.isDragging) {
        this.createEmojiFirework(e);
      }
    });
  }

  startBallPhysics() {
    // 启动台球物理系统，给球一个随机初始速度
    this.physics.isActive = true;
    this.physics.velocity.x = (Math.random() - 0.5) * 0.08;
    this.physics.velocity.y = (Math.random() - 0.5) * 0.08;
    this.physics.position.x = this.sphere.position.x;
    this.physics.position.y = this.sphere.position.y;
    console.log('台球物理启动！初始速度:', this.physics.velocity);
  }

  updatePhysics() {
    if (!this.physics.isActive) return;

    // 更新位置
    this.physics.position.x += this.physics.velocity.x;
    this.physics.position.y += this.physics.velocity.y;

    // 应用重力（轻微向下）
    this.physics.velocity.y -= this.physics.gravity;

    // 计算边界（考虑球体半径）
    const worldRadius = this.physics.sphereRadius;
    const leftBound = -2.5 + worldRadius;
    const rightBound = 2.5 - worldRadius;
    const topBound = 2.5 - worldRadius;
    const bottomBound = -2.5 + worldRadius;

    // 边界碰撞检测和反弹
    if (this.physics.position.x <= leftBound || this.physics.position.x >= rightBound) {
      this.physics.velocity.x *= -this.physics.bounceStrength;
      this.physics.position.x = Math.max(leftBound, Math.min(rightBound, this.physics.position.x));
      console.log('水平碰撞！');
    }

    if (this.physics.position.y <= bottomBound || this.physics.position.y >= topBound) {
      this.physics.velocity.y *= -this.physics.bounceStrength;
      this.physics.position.y = Math.max(bottomBound, Math.min(topBound, this.physics.position.y));
      console.log('垂直碰撞！');
    }

    // 应用摩擦力
    this.physics.velocity.x *= this.physics.friction;
    this.physics.velocity.y *= this.physics.friction;

    // 更新球体位置
    this.sphere.position.x = this.physics.position.x;
    this.sphere.position.y = this.physics.position.y;

    // 检查是否停止（速度太小时）
    const speed = Math.sqrt(this.physics.velocity.x ** 2 + this.physics.velocity.y ** 2);
    if (speed < 0.001) {
      this.physics.isActive = false;
      console.log('台球停止运动');
    }
  }

  createEmojiFirework(event) {
    // 创建烟花容器（如果不存在）
    let fireworksContainer = document.getElementById('emoji-fireworks');
    if (!fireworksContainer) {
      fireworksContainer = document.createElement('div');
      fireworksContainer.id = 'emoji-fireworks';
      fireworksContainer.style.position = 'fixed';
      fireworksContainer.style.top = '0';
      fireworksContainer.style.left = '0';
      fireworksContainer.style.width = '100%';
      fireworksContainer.style.height = '100%';
      fireworksContainer.style.pointerEvents = 'none';
      fireworksContainer.style.zIndex = '1000';
      document.body.appendChild(fireworksContainer);
    }

    // 随机表情数组
    const emojis = ['😊', '😄', '🥰', '😍', '🤩', '😎', '🤗', '😋', '😘', '🥳', '🎉', '✨', '🌟', '💫', '🎆', '🎇', '💖', '💕', '🌈'];

    // 创建多个粒子，从中心向外迸发
    const particleCount = 12;
    for (let i = 0; i < particleCount; i++) {
      const particle = document.createElement('div');
      particle.textContent = emojis[Math.floor(Math.random() * emojis.length)];
      particle.style.position = 'absolute';
      particle.style.fontSize = '24px';
      particle.style.userSelect = 'none';
      particle.style.pointerEvents = 'none';
      particle.style.left = event.clientX + 'px';
      particle.style.top = event.clientY + 'px';
      particle.style.transform = 'translate(-50%, -50%)';
      particle.style.opacity = '1';
      particle.style.zIndex = '1001';

      // 计算随机方向和距离
      const angle = (i / particleCount) * Math.PI * 2 + (Math.random() - 0.5) * 0.5;
      const distance = 80 + Math.random() * 60;
      const finalX = Math.cos(angle) * distance;
      const finalY = Math.sin(angle) * distance;

      // 添加到容器
      fireworksContainer.appendChild(particle);

      // 使用CSS动画从中心向外迸发
      particle.style.transition = 'all 1.2s cubic-bezier(0.25, 0.46, 0.45, 0.94)';

      // 延迟一帧以确保初始位置设置完成
      requestAnimationFrame(() => {
        particle.style.transform = `translate(calc(-50% + ${finalX}px), calc(-50% + ${finalY}px)) scale(0.5)`;
        particle.style.opacity = '0';
      });

      // 清理单个粒子
      setTimeout(() => {
        if (fireworksContainer.contains(particle)) {
          fireworksContainer.removeChild(particle);
        }
      }, 1300);
    }
  }

  animate() {
    requestAnimationFrame(() => this.animate());

    // 更新台球物理
    this.updatePhysics();

    // 更新力度指示器
    this.updateForceIndicator();

    // 自动旋转（只在非拖拽且非物理运动时）
    if (!this.isDragging && !this.physics.isActive && !this.forceControl.isCharging) {
      this.sphere.rotation.y += 0.005;
    }

    // 如果在物理运动中，添加旋转效果
    if (this.physics.isActive) {
      this.sphere.rotation.x += this.physics.velocity.y * 2;
      this.sphere.rotation.z += this.physics.velocity.x * 2;
    }

    this.renderer.render(this.scene, this.camera);
  }

  updateForceIndicator() {
    if (!this.forceControl.isCharging) {
      this.hideForceIndicator();
      return;
    }

    const chargeDuration = Date.now() - this.forceControl.startTime;
    const forceRatio = Math.min(chargeDuration / this.forceControl.chargeDuration, 1);

    this.showForceIndicator(forceRatio);
  }

  showForceIndicator(forceRatio) {
    let indicator = document.getElementById('force-indicator');
    if (!indicator) {
      indicator = document.createElement('div');
      indicator.id = 'force-indicator';
      indicator.style.position = 'fixed';
      indicator.style.top = '20px';
      indicator.style.left = '20px';
      indicator.style.width = '200px';
      indicator.style.height = '20px';
      indicator.style.border = '2px solid #333';
      indicator.style.borderRadius = '10px';
      indicator.style.backgroundColor = 'rgba(255, 255, 255, 0.9)';
      indicator.style.zIndex = '1000';
      indicator.style.overflow = 'hidden';

      const fill = document.createElement('div');
      fill.id = 'force-fill';
      fill.style.height = '100%';
      fill.style.borderRadius = '8px';
      fill.style.transition = 'all 0.1s ease';
      indicator.appendChild(fill);

      const label = document.createElement('div');
      label.textContent = '力度';
      label.style.position = 'absolute';
      label.style.top = '-25px';
      label.style.left = '0';
      label.style.fontSize = '14px';
      label.style.fontWeight = 'bold';
      label.style.color = '#333';
      indicator.appendChild(label);

      document.body.appendChild(indicator);
    }

    const fill = document.getElementById('force-fill');
    const percentage = forceRatio * 100;
    fill.style.width = percentage + '%';

    // 颜色渐变：绿色 -> 黄色 -> 红色
    if (forceRatio < 0.5) {
      const green = 255;
      const red = Math.floor(forceRatio * 2 * 255);
      fill.style.backgroundColor = `rgb(${red}, ${green}, 0)`;
    } else {
      const red = 255;
      const green = Math.floor((1 - forceRatio) * 2 * 255);
      fill.style.backgroundColor = `rgb(${red}, ${green}, 0)`;
    }
  }

  hideForceIndicator() {
    const indicator = document.getElementById('force-indicator');
    if (indicator) {
      indicator.remove();
    }
  }
}

// 初始化
document.addEventListener('DOMContentLoaded', () => {
  console.log('DOM loaded, creating SimpleSphere3D');
  new SimpleSphere3D();
});

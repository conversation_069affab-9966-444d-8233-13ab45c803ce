// Three.js 3D 笑脸球体
const container = document.getElementById('threejs-container');
const width = container.clientWidth;
const height = container.clientHeight;

// 创建场景、相机、渲染器
const scene = new THREE.Scene();
const camera = new THREE.PerspectiveCamera(45, width / height, 0.1, 1000);
camera.position.z = 4;

const renderer = new THREE.WebGLRenderer({ antialias: true, alpha: true });
renderer.setSize(width, height);
container.appendChild(renderer.domElement);

// 加载高清笑脸贴图
const textureLoader = new THREE.TextureLoader();
textureLoader.load('smile-texture.png', function(smileTexture) {
  // 创建球体，增强高光
  const geometry = new THREE.SphereGeometry(1.5, 64, 64);
  const material = new THREE.MeshPhongMaterial({ 
    map: smileTexture, 
    shininess: 60, // 高光强度
    specular: 0xffffff // 高光颜色
  });
  const sphere = new THREE.Mesh(geometry, material);
  scene.add(sphere);

  // 光源（增强立体感）
  const light1 = new THREE.DirectionalLight(0xffffff, 1.1);
  light1.position.set(2, 2, 4);
  scene.add(light1);
  const light2 = new THREE.AmbientLight(0xffffff, 0.4);
  scene.add(light2);
  const light3 = new THREE.PointLight(0xfff9c4, 0.5, 10);
  light3.position.set(-2, -2, 2);
  scene.add(light3);

  // 旋转控制
  let isDragging = false;
  let lastX, lastY;
  let rotation = { x: 0, y: 0 };

  renderer.domElement.addEventListener('mousedown', (e) => {
    isDragging = true;
    lastX = e.clientX;
    lastY = e.clientY;
  });
  window.addEventListener('mousemove', (e) => {
    if (!isDragging) return;
    const dx = (e.clientX - lastX) / width * Math.PI;
    const dy = (e.clientY - lastY) / height * Math.PI;
    rotation.y += dx;
    rotation.x += dy;
    lastX = e.clientX;
    lastY = e.clientY;
  });
  window.addEventListener('mouseup', () => { isDragging = false; });

  function animate() {
    sphere.rotation.y = rotation.y;
    sphere.rotation.x = rotation.x;
    renderer.render(scene, camera);
    requestAnimationFrame(animate);
  }
  animate();
}); 
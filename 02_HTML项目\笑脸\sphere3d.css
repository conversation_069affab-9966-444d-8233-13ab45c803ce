/* 全局样式重置 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Arial', sans-serif;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
  overflow-x: hidden;
  color: white;
}

.main-wrapper {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  padding: 20px;
}

.title {
  font-size: 2.5rem;
  margin-bottom: 10px;
  text-align: center;
  text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
  background: linear-gradient(45deg, #ffd700, #ffeb3b, #ffc107);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.instruction {
  font-size: 1.2rem;
  margin-bottom: 30px;
  text-align: center;
  opacity: 0.9;
}

/* 容器样式 */
#container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 500px;
  position: relative;
  perspective: 1500px;
  perspective-origin: center center;
}

/* 球体容器 */
.sphere-container {
  width: 400px;
  height: 400px;
  position: relative;
  transform-style: preserve-3d;
  cursor: grab;
  transition: transform 0.1s ease-out;
  /* 确保容器可见 */
  z-index: 1;
}

.sphere-container:active {
  cursor: grabbing;
}

/* 球体主体 */
.sphere {
  width: 100%;
  height: 100%;
  position: relative;
  transform-style: preserve-3d;
  animation: float 4s ease-in-out infinite;
  /* 确保球体可见 */
  z-index: 2;
}

/* 球体表面 - 创建真正平滑的3D球体 */
.sphere-surface {
  position: absolute;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  background:
    /* 真实球体渐变 - 模拟光照 */
    radial-gradient(ellipse 120% 100% at 30% 30%,
      #ffeb3b 0%,
      #ffc107 30%,
      #ff8f00 65%,
      #e65100 90%,
      #bf360c 100%);
  box-shadow:
    /* 球体立体阴影 - 更强的对比 */
    inset -60px -60px 120px rgba(0,0,0,0.8),
    inset 60px 60px 120px rgba(255,255,255,0.5),
    /* 外部投影 - 更真实的阴影 */
    0 60px 120px rgba(0,0,0,0.7),
    0 30px 60px rgba(0,0,0,0.5),
    0 15px 30px rgba(0,0,0,0.3);
  transform-style: preserve-3d;
  position: relative;
  /* 确保球体表面可见 */
  z-index: 3;
}

/* 球体主高光 */
.sphere-surface::before {
  content: '';
  position: absolute;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  background:
    /* 主要高光 - 左上角强光 */
    radial-gradient(ellipse 80% 60% at 22% 22%,
      rgba(255,255,255,1) 0%,
      rgba(255,255,255,0.8) 15%,
      rgba(255,255,255,0.4) 35%,
      rgba(255,255,255,0.1) 55%,
      transparent 70%);
  top: 0;
  left: 0;
  z-index: 2;
}

/* 球体次要高光和反射 */
.sphere-surface::after {
  content: '';
  position: absolute;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  background:
    /* 底部反射光 */
    radial-gradient(ellipse 60% 40% at 50% 88%,
      rgba(255,255,255,0.4) 0%,
      rgba(255,255,255,0.2) 30%,
      rgba(255,255,255,0.05) 60%,
      transparent 80%),
    /* 右侧环境光 */
    radial-gradient(ellipse 40% 80% at 85% 50%,
      rgba(255,235,59,0.3) 0%,
      rgba(255,193,7,0.15) 40%,
      transparent 70%),
    /* 左侧柔和反射 */
    radial-gradient(ellipse 30% 60% at 15% 60%,
      rgba(255,255,255,0.2) 0%,
      rgba(255,255,255,0.05) 50%,
      transparent 80%);
  top: 0;
  left: 0;
  z-index: 1;
}

/* 面部特征容器 */
.face-features {
  position: absolute;
  width: 100%;
  height: 100%;
  transform-style: preserve-3d;
  z-index: 10;
}

/* 眼睛样式 */
.eye {
  width: 70px;
  height: 70px;
  background: 
    radial-gradient(circle at 40% 40%, #ffffff, #f0f0f0 60%, #e0e0e0 100%);
  border-radius: 50%;
  position: absolute;
  top: 130px;
  animation: blink 4s infinite;
  box-shadow:
    inset -15px -15px 30px rgba(0,0,0,0.3),
    inset 15px 15px 30px rgba(255,255,255,0.6),
    0 5px 15px rgba(0,0,0,0.4);
  transform: translateZ(40px);
  border: 1px solid rgba(0,0,0,0.2);
}

.left-eye {
  left: 90px;
}

.right-eye {
  right: 90px;
}

/* 瞳孔 */
.eye::before {
  content: '';
  position: absolute;
  width: 30px;
  height: 30px;
  background: 
    radial-gradient(circle at 35% 35%, #333, #000);
  border-radius: 50%;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%) translateZ(3px);
  box-shadow: 
    inset -5px -5px 10px rgba(0,0,0,0.8),
    0 3px 6px rgba(0,0,0,0.5);
}

/* 瞳孔高光 */
.eye::after {
  content: '';
  position: absolute;
  width: 8px;
  height: 8px;
  background: 
    radial-gradient(circle, rgba(255,255,255,1), rgba(255,255,255,0.8) 60%, transparent);
  border-radius: 50%;
  top: 40%;
  left: 45%;
  transform: translateZ(5px);
  box-shadow: 0 0 3px rgba(255,255,255,0.8);
}

/* 鼻子样式 */
.nose {
  width: 25px;
  height: 35px;
  background: 
    radial-gradient(ellipse at 30% 20%, rgba(255,255,255,0.3), transparent 50%),
    radial-gradient(ellipse at 50% 50%, rgba(255,235,59,0.8), rgba(255,193,7,0.6));
  border-radius: 50% 50% 60% 60% / 60% 60% 50% 50%;
  position: absolute;
  top: 200px;
  left: 50%;
  transform: translateX(-50%) translateZ(45px);
  box-shadow:
    inset -8px -8px 16px rgba(0,0,0,0.3),
    inset 8px 8px 16px rgba(255,255,255,0.3),
    0 8px 16px rgba(0,0,0,0.4);
  border: 1px solid rgba(0,0,0,0.15);
}

/* 鼻孔 */
.nose::before {
  content: '';
  position: absolute;
  width: 4px;
  height: 8px;
  background: 
    radial-gradient(ellipse, rgba(0,0,0,0.8), rgba(0,0,0,0.4));
  border-radius: 50%;
  bottom: 12px;
  left: 6px;
  box-shadow: 
    9px 0 0 rgba(0,0,0,0.8);
  transform: translateZ(2px);
}

/* 嘴巴样式 */
.mouth {
  width: 120px;
  height: 60px;
  background: 
    radial-gradient(ellipse at center, #000, #1a1a1a 80%);
  border-radius: 0 0 120px 120px;
  position: absolute;
  bottom: 110px;
  left: 50%;
  transform: translateX(-50%) translateZ(35px);
  transition: all 0.3s ease;
  box-shadow:
    inset 0 -20px 40px rgba(0,0,0,0.8),
    inset 0 10px 20px rgba(255,255,255,0.1),
    0 10px 20px rgba(0,0,0,0.5);
  border: 1px solid rgba(0,0,0,0.3);
  overflow: hidden;
}

/* 脸颊样式 */
.cheek {
  width: 50px;
  height: 50px;
  background: 
    radial-gradient(circle at 25% 25%, rgba(255, 182, 193, 0.8), rgba(255, 140, 160, 0.5) 50%, transparent 70%),
    radial-gradient(circle at 50% 50%, rgba(255, 160, 180, 0.6), rgba(255, 120, 150, 0.3));
  border-radius: 50%;
  position: absolute;
  top: 170px;
  animation: pulse 2s ease-in-out infinite;
  box-shadow:
    inset -10px -10px 20px rgba(255, 100, 130, 0.5),
    inset 10px 10px 20px rgba(255, 220, 230, 0.6),
    0 8px 16px rgba(255, 140, 160, 0.4);
  transform: translateZ(30px);
  border: 1px solid rgba(255, 180, 200, 0.4);
}

.left-cheek {
  left: 30px;
}

.right-cheek {
  right: 30px;
}

/* 动画效果 */
@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotateX(0deg);
  }
  50% {
    transform: translateY(-20px) rotateX(5deg);
  }
}

@keyframes blink {
  0%, 90%, 100% {
    transform: translateZ(40px) scaleY(1);
  }
  95% {
    transform: translateZ(40px) scaleY(0.1);
  }
}

@keyframes pulse {
  0%, 100% {
    transform: translateZ(30px) scale(1);
    opacity: 0.7;
  }
  50% {
    transform: translateZ(30px) scale(1.1);
    opacity: 0.9;
  }
}

/* 指令样式 */
.instructions {
  margin-top: 30px;
  text-align: center;
  opacity: 0.8;
}

.instructions p {
  margin: 8px 0;
  font-size: 1rem;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
}

.instructions strong {
  color: #ffd700;
  text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
}

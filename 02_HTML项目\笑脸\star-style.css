/* 全局样式重置 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Arial', sans-serif;
  background: linear-gradient(135deg, #7b68ee 0%, #9370db 50%, #8a2be2 100%);
  min-height: 100vh;
  overflow-x: hidden;
  color: white;
}

.main-wrapper {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  padding: 20px;
}

.title {
  font-size: 2.5rem;
  margin-bottom: 10px;
  text-align: center;
  text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
  background: linear-gradient(45deg, #ffd700, #ffeb3b, #ffc107);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.instruction {
  font-size: 1.2rem;
  margin-bottom: 30px;
  text-align: center;
  opacity: 0.9;
}

/* 容器样式 */
#container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 500px;
  position: relative;
  perspective: 1500px;
  perspective-origin: center center;
}

/* 星形容器 */
.star-container {
  width: 400px;
  height: 400px;
  position: relative;
  transform-style: preserve-3d;
  cursor: grab;
  transition: transform 0.1s ease-out;
}

.star-container:active {
  cursor: grabbing;
}

/* 星形主体 */
.star-shape {
  width: 100%;
  height: 100%;
  position: relative;
  transform-style: preserve-3d;
  animation: float 4s ease-in-out infinite;
}

/* 花瓣样式 */
.petal {
  position: absolute;
  width: 100%;
  height: 100%;
  transform-style: preserve-3d;
  transform: rotateZ(var(--angle)) translateY(-50px);
  transform-origin: center center;
}

.petal-surface {
  width: 120px;
  height: 200px;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translateX(-50%) translateY(-100%) rotateX(15deg);
  background: 
    /* 花瓣渐变 */
    linear-gradient(180deg, 
      #ffeb3b 0%, 
      #ffc107 30%, 
      #ff8f00 70%, 
      #e65100 100%);
  border-radius: 50% 50% 50% 50% / 60% 60% 40% 40%;
  box-shadow:
    /* 内部阴影创造深度 */
    inset -15px -15px 30px rgba(0,0,0,0.3),
    inset 15px 15px 30px rgba(255,255,255,0.2),
    /* 外部阴影 */
    0 20px 40px rgba(0,0,0,0.4);
  transform-style: preserve-3d;
}

/* 花瓣高光效果 */
.petal-surface::before {
  content: '';
  position: absolute;
  width: 100%;
  height: 100%;
  border-radius: inherit;
  background: 
    /* 主高光 */
    radial-gradient(ellipse at 30% 20%, rgba(255,255,255,0.6), rgba(255,255,255,0.2) 50%, transparent 80%),
    /* 边缘高光 */
    linear-gradient(180deg, rgba(255,255,255,0.3) 0%, transparent 30%);
  top: 0;
  left: 0;
}

/* 中心圆形 */
.center-circle {
  position: absolute;
  width: 180px;
  height: 180px;
  background: 
    radial-gradient(circle at 30% 30%, #ffeb3b, #ffc107 40%, #ff8f00 70%, #e65100 100%);
  border-radius: 50%;
  top: 50%;
  left: 50%;
  transform: translateX(-50%) translateY(-50%) translateZ(30px);
  box-shadow:
    inset -20px -20px 40px rgba(0,0,0,0.3),
    inset 20px 20px 40px rgba(255,255,255,0.2),
    0 30px 60px rgba(0,0,0,0.5);
  z-index: 10;
}

/* 中心圆形高光 */
.center-circle::before {
  content: '';
  position: absolute;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  background: 
    radial-gradient(circle at 25% 25%, rgba(255,255,255,0.7), rgba(255,255,255,0.3) 40%, transparent 70%);
  top: 0;
  left: 0;
}

/* 面部特征容器 */
.face-features {
  position: absolute;
  width: 180px;
  height: 180px;
  top: 50%;
  left: 50%;
  transform: translateX(-50%) translateY(-50%) translateZ(35px);
  transform-style: preserve-3d;
}

/* 眼睛样式 */
.eye {
  width: 35px;
  height: 35px;
  background: 
    radial-gradient(circle at 40% 40%, #ffffff, #f0f0f0 60%, #e0e0e0 100%);
  border-radius: 50%;
  position: absolute;
  top: 50px;
  animation: blink 4s infinite;
  box-shadow:
    inset -8px -8px 15px rgba(0,0,0,0.3),
    inset 8px 8px 15px rgba(255,255,255,0.6),
    0 3px 8px rgba(0,0,0,0.4);
  transform: translateZ(5px);
  border: 1px solid rgba(0,0,0,0.2);
}

.left-eye {
  left: 35px;
}

.right-eye {
  right: 35px;
}

/* 瞳孔 */
.eye::before {
  content: '';
  position: absolute;
  width: 15px;
  height: 15px;
  background: 
    radial-gradient(circle at 35% 35%, #333, #000);
  border-radius: 50%;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%) translateZ(2px);
  box-shadow: 
    inset -3px -3px 6px rgba(0,0,0,0.8),
    0 2px 4px rgba(0,0,0,0.5);
}

/* 瞳孔高光 */
.eye::after {
  content: '';
  position: absolute;
  width: 4px;
  height: 4px;
  background: 
    radial-gradient(circle, rgba(255,255,255,1), rgba(255,255,255,0.8) 60%, transparent);
  border-radius: 50%;
  top: 40%;
  left: 45%;
  transform: translateZ(3px);
  box-shadow: 0 0 2px rgba(255,255,255,0.8);
}

/* 鼻子样式 */
.nose {
  width: 12px;
  height: 18px;
  background: 
    radial-gradient(ellipse at 30% 20%, rgba(255,255,255,0.3), transparent 50%),
    radial-gradient(ellipse at 50% 50%, rgba(255,235,59,0.8), rgba(255,193,7,0.6));
  border-radius: 50% 50% 60% 60% / 60% 60% 50% 50%;
  position: absolute;
  top: 80px;
  left: 50%;
  transform: translateX(-50%) translateZ(8px);
  box-shadow:
    inset -4px -4px 8px rgba(0,0,0,0.3),
    inset 4px 4px 8px rgba(255,255,255,0.3),
    0 4px 8px rgba(0,0,0,0.4);
  border: 1px solid rgba(0,0,0,0.15);
}

/* 嘴巴样式 */
.mouth {
  width: 60px;
  height: 30px;
  background: 
    radial-gradient(ellipse at center, #000, #1a1a1a 80%);
  border-radius: 0 0 60px 60px;
  position: absolute;
  bottom: 40px;
  left: 50%;
  transform: translateX(-50%) translateZ(5px);
  transition: all 0.3s ease;
  box-shadow:
    inset 0 -10px 20px rgba(0,0,0,0.8),
    inset 0 5px 10px rgba(255,255,255,0.1),
    0 5px 10px rgba(0,0,0,0.5);
  border: 1px solid rgba(0,0,0,0.3);
  overflow: hidden;
}

/* 脸颊样式 */
.cheek {
  width: 25px;
  height: 25px;
  background: 
    radial-gradient(circle at 25% 25%, rgba(255, 182, 193, 0.8), rgba(255, 140, 160, 0.5) 50%, transparent 70%);
  border-radius: 50%;
  position: absolute;
  top: 70px;
  animation: pulse 2s ease-in-out infinite;
  box-shadow:
    inset -5px -5px 10px rgba(255, 100, 130, 0.5),
    inset 5px 5px 10px rgba(255, 220, 230, 0.6),
    0 4px 8px rgba(255, 140, 160, 0.4);
  transform: translateZ(3px);
  border: 1px solid rgba(255, 180, 200, 0.4);
}

.left-cheek {
  left: 15px;
}

.right-cheek {
  right: 15px;
}

/* 动画效果 */
@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotateX(0deg);
  }
  50% {
    transform: translateY(-15px) rotateX(3deg);
  }
}

@keyframes blink {
  0%, 90%, 100% {
    transform: translateZ(5px) scaleY(1);
  }
  95% {
    transform: translateZ(5px) scaleY(0.1);
  }
}

@keyframes pulse {
  0%, 100% {
    transform: translateZ(3px) scale(1);
    opacity: 0.7;
  }
  50% {
    transform: translateZ(3px) scale(1.1);
    opacity: 0.9;
  }
}

/* 指令样式 */
.instructions {
  margin-top: 30px;
  text-align: center;
  opacity: 0.8;
}

.instructions p {
  margin: 8px 0;
  font-size: 1rem;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
}

.instructions strong {
  color: #ffd700;
  text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
}

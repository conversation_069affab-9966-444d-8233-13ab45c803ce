body {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  margin: 0;
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  font-family: 'Arial', sans-serif;
  padding: 20px;
  box-sizing: border-box;
}

.main-wrapper {
  text-align: center;
  color: white;
}

.title {
  font-size: 2.5rem;
  margin: 0 0 10px 0;
  text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
  animation: glow 2s ease-in-out infinite alternate;
}

.instruction {
  font-size: 1.1rem;
  margin: 0 0 30px 0;
  opacity: 0.9;
  text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
}

@keyframes glow {
  from { text-shadow: 2px 2px 4px rgba(0,0,0,0.3), 0 0 10px rgba(255,255,255,0.2); }
  to { text-shadow: 2px 2px 4px rgba(0,0,0,0.3), 0 0 20px rgba(255,255,255,0.4); }
}
#container {
  position: relative;
  width: 500px;
  height: 500px;
  border-radius: 20px;
  box-shadow: 0 20px 40px rgba(0,0,0,0.3);
  background:
    radial-gradient(circle at 30% 30%, rgba(255,255,255,0.2), transparent 50%),
    rgba(255,255,255,0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255,255,255,0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  perspective: 1200px; /* 增强3D透视 */
  overflow: hidden;
}

/* 添加环境光照效果 */
#container::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background:
    radial-gradient(circle at 25% 25%, rgba(255,255,255,0.1), transparent 40%),
    radial-gradient(circle at 75% 75%, rgba(0,0,0,0.05), transparent 40%);
  pointer-events: none;
  z-index: 1;
}

.smiley-face {
  width: 400px;
  height: 400px;
  background:
    /* 真实球体渐变 - 从左上角光源 */
    radial-gradient(circle at 30% 30%, #ffeb3b, #ffc107 50%, #ff8f00 80%, #e65100 100%);
  border-radius: 50%;
  position: relative;
  cursor: grab;
  transition: all 0.3s ease;
  box-shadow:
    /* 外部投影 - 真实阴影 */
    0 50px 100px rgba(0,0,0,0.6),
    0 25px 50px rgba(0,0,0,0.4),
    0 15px 30px rgba(0,0,0,0.3);
  animation: float 3s ease-in-out infinite;
  transform-style: preserve-3d;
  user-select: none;
  z-index: 2;
}

/* 球体主要高光 */
.smiley-face::before {
  content: '';
  position: absolute;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  background:
    /* 主要高光 - 左上角强光 */
    radial-gradient(circle at 25% 25%, rgba(255,255,255,0.8), rgba(255,255,255,0.4) 30%, transparent 60%);
  top: 0;
  left: 0;
  z-index: 1;
}

/* 球体次要高光和阴影 */
.smiley-face::after {
  content: '';
  position: absolute;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  background:
    /* 底部反射光 */
    radial-gradient(circle at 50% 80%, rgba(255,255,255,0.2), transparent 40%),
    /* 右下角阴影 */
    radial-gradient(circle at 80% 80%, rgba(0,0,0,0.3), transparent 50%);
  top: 0;
  left: 0;
  z-index: 1;
}

.smiley-face:hover {
  box-shadow:
    inset -35px -35px 70px rgba(0,0,0,0.2),
    inset 35px 35px 70px rgba(255,255,255,0.5),
    inset -15px -15px 30px rgba(0,0,0,0.15),
    0 25px 50px rgba(0,0,0,0.4),
    0 0 0 12px rgba(255,235,59,0.2);
}

.smiley-face:active {
  cursor: grabbing;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotateY(0deg) rotateX(0deg);
    box-shadow:
      inset -30px -30px 60px rgba(0,0,0,0.15),
      inset 30px 30px 60px rgba(255,255,255,0.4),
      inset -10px -10px 20px rgba(0,0,0,0.1),
      0 20px 40px rgba(0,0,0,0.3),
      0 0 0 8px rgba(255,235,59,0.1);
  }
  25% {
    transform: translateY(-5px) rotateY(5deg) rotateX(2deg);
    box-shadow:
      inset -35px -25px 60px rgba(0,0,0,0.18),
      inset 25px 35px 60px rgba(255,255,255,0.45),
      inset -15px -5px 20px rgba(0,0,0,0.12),
      0 25px 45px rgba(0,0,0,0.35),
      0 0 0 10px rgba(255,235,59,0.15);
  }
  50% {
    transform: translateY(-10px) rotateY(0deg) rotateX(-2deg);
    box-shadow:
      inset -30px -35px 60px rgba(0,0,0,0.2),
      inset 30px 25px 60px rgba(255,255,255,0.5),
      inset -10px -15px 20px rgba(0,0,0,0.15),
      0 30px 50px rgba(0,0,0,0.4),
      0 0 0 12px rgba(255,235,59,0.2);
  }
  75% {
    transform: translateY(-5px) rotateY(-5deg) rotateX(2deg);
    box-shadow:
      inset -25px -30px 60px rgba(0,0,0,0.18),
      inset 35px 30px 60px rgba(255,255,255,0.45),
      inset -5px -10px 20px rgba(0,0,0,0.12),
      0 25px 45px rgba(0,0,0,0.35),
      0 0 0 10px rgba(255,235,59,0.15);
  }
}

.eye {
  width: 70px;
  height: 70px;
  background:
    /* 眼球凹陷效果 */
    radial-gradient(circle at 40% 40%, #ffffff, #f0f0f0 60%, #e0e0e0 100%);
  border-radius: 50%;
  position: absolute;
  top: 130px;
  animation: blink 4s infinite;
  box-shadow:
    /* 眼窝深度 */
    inset -15px -15px 30px rgba(0,0,0,0.3),
    inset 15px 15px 30px rgba(255,255,255,0.6),
    /* 外部阴影 */
    0 5px 15px rgba(0,0,0,0.4);
  transform: translateZ(30px);
  border: 1px solid rgba(0,0,0,0.2);
}

/* 瞳孔 */
.eye::before {
  content: '';
  position: absolute;
  width: 30px;
  height: 30px;
  background:
    radial-gradient(circle at 35% 35%, #333, #000);
  border-radius: 50%;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%) translateZ(3px);
  box-shadow:
    inset -5px -5px 10px rgba(0,0,0,0.8),
    0 3px 6px rgba(0,0,0,0.5);
}

/* 瞳孔高光 */
.eye::after {
  content: '';
  position: absolute;
  width: 8px;
  height: 8px;
  background:
    radial-gradient(circle, rgba(255,255,255,1), rgba(255,255,255,0.8) 60%, transparent);
  border-radius: 50%;
  top: 40%;
  left: 45%;
  transform: translateZ(5px);
  box-shadow: 0 0 3px rgba(255,255,255,0.8);
}

.left-eye {
  left: 90px;
}

.right-eye {
  right: 90px;
}

@keyframes blink {
  0%, 90%, 100% { height: 90px; }
  95% { height: 8px; }
}

/* 鼻子样式 */
.nose {
  width: 25px;
  height: 35px;
  background:
    /* 鼻子高光 */
    radial-gradient(ellipse at 30% 20%, rgba(255,255,255,0.3), transparent 50%),
    /* 鼻子主体 - 与球体颜色一致 */
    radial-gradient(ellipse at 50% 50%, rgba(255,235,59,0.8), rgba(255,193,7,0.6));
  border-radius: 50% 50% 60% 60% / 60% 60% 50% 50%;
  position: absolute;
  top: 200px;
  left: 50%;
  transform: translateX(-50%) translateZ(35px);
  box-shadow:
    /* 鼻子凸起 */
    inset -8px -8px 16px rgba(0,0,0,0.3),
    inset 8px 8px 16px rgba(255,255,255,0.3),
    /* 外部阴影 */
    0 8px 16px rgba(0,0,0,0.4);
  border: 1px solid rgba(0,0,0,0.15);
}

/* 鼻孔 */
.nose::before {
  content: '';
  position: absolute;
  width: 4px;
  height: 8px;
  background:
    radial-gradient(ellipse, rgba(0,0,0,0.8), rgba(0,0,0,0.4));
  border-radius: 50%;
  bottom: 12px;
  left: 6px;
  box-shadow:
    9px 0 0 rgba(0,0,0,0.8);
  transform: translateZ(2px);
}

.mouth {
  width: 120px;
  height: 60px;
  background:
    /* 嘴巴深度 */
    radial-gradient(ellipse at center, #000, #1a1a1a 80%);
  border-radius: 0 0 120px 120px;
  position: absolute;
  bottom: 110px;
  left: 50%;
  transform: translateX(-50%) translateZ(20px);
  transition: all 0.3s ease;
  box-shadow:
    /* 嘴巴凹陷 */
    inset 0 -20px 40px rgba(0,0,0,0.8),
    inset 0 10px 20px rgba(255,255,255,0.1),
    /* 外部阴影 */
    0 10px 20px rgba(0,0,0,0.5);
  border: 1px solid rgba(0,0,0,0.3);
  overflow: hidden;
}

/* 舌头 */
.mouth::before {
  content: '';
  position: absolute;
  width: 60px;
  height: 25px;
  background:
    radial-gradient(ellipse at center, #ff4757, #c44569);
  border-radius: 0 0 60px 60px;
  bottom: 8px;
  left: 50%;
  transform: translateX(-50%);
  box-shadow:
    inset 0 -8px 16px rgba(0,0,0,0.4),
    0 3px 6px rgba(0,0,0,0.3);
}

/* 牙齿 */
.mouth::after {
  content: '';
  position: absolute;
  width: 80px;
  height: 10px;
  background:
    linear-gradient(to right,
      transparent 8%, white 12%, white 20%, transparent 24%,
      transparent 32%, white 36%, white 44%, transparent 48%,
      transparent 56%, white 60%, white 68%, transparent 72%,
      transparent 80%, white 84%, white 92%, transparent 96%);
  top: 10px;
  left: 50%;
  transform: translateX(-50%);
  border-radius: 5px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.3);
}

.cheek {
  width: 50px;
  height: 50px;
  background:
    /* 脸颊高光 */
    radial-gradient(circle at 25% 25%, rgba(255, 182, 193, 0.8), rgba(255, 140, 160, 0.5) 50%, transparent 70%),
    /* 脸颊主色 */
    radial-gradient(circle at 50% 50%, rgba(255, 160, 180, 0.6), rgba(255, 120, 150, 0.3));
  border-radius: 50%;
  position: absolute;
  top: 170px;
  animation: pulse 2s ease-in-out infinite;
  box-shadow:
    /* 脸颊凸起 */
    inset -10px -10px 20px rgba(255, 100, 130, 0.5),
    inset 10px 10px 20px rgba(255, 220, 230, 0.6),
    /* 外部阴影 */
    0 8px 16px rgba(255, 140, 160, 0.4);
  transform: translateZ(25px);
  border: 1px solid rgba(255, 180, 200, 0.4);
}

.left-cheek {
  left: 30px;
}

.right-cheek {
  right: 30px;
}

@keyframes pulse {
  0%, 100% {
    opacity: 0.6;
    transform: translateZ(15px) scale(1);
    box-shadow:
      inset -5px -5px 10px rgba(255, 100, 130, 0.3),
      inset 5px 5px 10px rgba(255, 220, 230, 0.6),
      0 3px 8px rgba(255, 140, 160, 0.2);
  }
  50% {
    opacity: 0.8;
    transform: translateZ(18px) scale(1.1);
    box-shadow:
      inset -7px -7px 14px rgba(255, 100, 130, 0.4),
      inset 7px 7px 14px rgba(255, 220, 230, 0.7),
      0 5px 12px rgba(255, 140, 160, 0.3);
  }
}

.instructions {
  position: absolute;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  text-align: center;
  color: white;
  font-size: 14px;
  background: rgba(0,0,0,0.3);
  padding: 15px 20px;
  border-radius: 10px;
  backdrop-filter: blur(10px);
}

.instructions p {
  margin: 5px 0;
}

#emoji-fireworks {
  pointer-events: none;
  position: absolute;
  left: 0; top: 0;
  width: 100%;
  height: 100%;
  z-index: 10;
}
.emoji-burst {
  position: absolute;
  font-size: 2.2rem;
  opacity: 1;
  transition: transform 1.2s cubic-bezier(.4,2,.6,1), opacity 1.2s;
  will-change: transform, opacity;
}

/* 表情变化时的特殊效果 */
.expression-change {
  animation: expressionChange 0.6s ease-out;
}

@keyframes expressionChange {
  0% {
    transform: scale(1) rotateZ(0deg);
    filter: brightness(1);
  }
  25% {
    transform: scale(1.05) rotateZ(2deg);
    filter: brightness(1.2);
  }
  50% {
    transform: scale(0.98) rotateZ(-1deg);
    filter: brightness(1.1);
  }
  75% {
    transform: scale(1.02) rotateZ(1deg);
    filter: brightness(1.05);
  }
  100% {
    transform: scale(1) rotateZ(0deg);
    filter: brightness(1);
  }
}

<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>测试3D笑脸球</title>
  <style>
    body {
      margin: 0;
      padding: 20px;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      min-height: 100vh;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      font-family: Arial, sans-serif;
      color: white;
    }

    h1 {
      margin-bottom: 20px;
      text-align: center;
    }

    #container {
      width: 500px;
      height: 500px;
      border: 2px solid rgba(255,255,255,0.3);
      border-radius: 10px;
      background: rgba(0,0,0,0.1);
    }

    .info {
      margin-top: 20px;
      text-align: center;
      opacity: 0.8;
    }
  </style>
</head>
<body>
  <h1>🌟 测试3D笑脸球 🌟</h1>
  <p>拖动球体进行旋转测试</p>
  
  <div id="container"></div>
  
  <div class="info">
    <p>🖱️ 拖拽球体进行旋转</p>
    <p>如果能拖动，说明交互正常</p>
  </div>

  <script src="https://cdn.jsdelivr.net/npm/three@0.152.2/build/three.min.js"></script>
  <script src="simple-sphere.js"></script>
</body>
</html>

// 真正的3D球体笑脸
class TrueSphere3D {
  constructor() {
    this.scene = null;
    this.camera = null;
    this.renderer = null;
    this.sphere = null;
    this.isDragging = false;
    this.previousMousePosition = { x: 0, y: 0 };
    this.rotationVelocity = { x: 0, y: 0 };
    this.damping = 0.95;
    
    this.init();
    this.setupEventListeners();
    this.animate();
  }

  init() {
    const container = document.getElementById('container');
    
    // 创建场景
    this.scene = new THREE.Scene();
    
    // 创建相机
    this.camera = new THREE.PerspectiveCamera(
      75, 
      container.clientWidth / container.clientHeight, 
      0.1, 
      1000
    );
    this.camera.position.z = 3;
    
    // 创建渲染器
    this.renderer = new THREE.WebGLRenderer({ 
      antialias: true, 
      alpha: true 
    });
    this.renderer.setSize(container.clientWidth, container.clientHeight);
    this.renderer.setClearColor(0x000000, 0); // 透明背景
    this.renderer.shadowMap.enabled = true;
    this.renderer.shadowMap.type = THREE.PCFSoftShadowMap;
    container.appendChild(this.renderer.domElement);
    
    // 创建球体几何体
    const geometry = new THREE.SphereGeometry(1, 64, 64);

    // 创建球体网格 - 先用基础材质
    const basicMaterial = new THREE.MeshPhongMaterial({
      color: 0xffeb3b,
      shininess: 80,
      specular: 0x444444
    });

    this.sphere = new THREE.Mesh(geometry, basicMaterial);
    this.sphere.castShadow = true;
    this.sphere.receiveShadow = true;
    this.scene.add(this.sphere);

    // 创建并应用备用材质
    this.createFallbackMaterial();

    // 尝试加载用户贴图
    const textureLoader = new THREE.TextureLoader();
    textureLoader.load('smile-texture.png',
      // 加载成功回调
      (texture) => {
        console.log('用户贴图加载成功');
        // 优化贴图设置
        texture.wrapS = THREE.RepeatWrapping;
        texture.wrapT = THREE.ClampToEdgeWrapping;
        texture.minFilter = THREE.LinearFilter;
        texture.magFilter = THREE.LinearFilter;

        const material = new THREE.MeshPhongMaterial({
          map: texture,
          shininess: 80,
          specular: 0x444444
        });

        if (this.sphere) {
          this.sphere.material = material;
        }
      },
      // 加载进度回调
      (progress) => {
        console.log('贴图加载进度:', progress);
      },
      // 加载失败回调
      (error) => {
        console.log('使用备用贴图 - 用户贴图未找到');
        // 备用材质已经应用了
      }
    );
    
    // 改进的照明系统以增强3D效果

    // 环境光 - 提供基础照明
    const ambientLight = new THREE.AmbientLight(0x404040, 0.4);
    this.scene.add(ambientLight);

    // 主光源 - 从右上方照射
    const mainLight = new THREE.DirectionalLight(0xffffff, 1.2);
    mainLight.position.set(8, 8, 5);
    mainLight.castShadow = true;
    mainLight.shadow.mapSize.width = 4096;
    mainLight.shadow.mapSize.height = 4096;
    mainLight.shadow.camera.near = 0.1;
    mainLight.shadow.camera.far = 50;
    mainLight.shadow.camera.left = -10;
    mainLight.shadow.camera.right = 10;
    mainLight.shadow.camera.top = 10;
    mainLight.shadow.camera.bottom = -10;
    this.scene.add(mainLight);

    // 补光 - 从左下方提供柔和照明
    const fillLight = new THREE.DirectionalLight(0x87ceeb, 0.5);
    fillLight.position.set(-6, -4, -3);
    this.scene.add(fillLight);

    // 边缘光 - 增强球体轮廓
    const rimLight = new THREE.DirectionalLight(0xffd700, 0.6);
    rimLight.position.set(0, 0, -8);
    this.scene.add(rimLight);

    // 顶部光源 - 增加立体感
    const topLight = new THREE.PointLight(0xffffff, 0.8, 20);
    topLight.position.set(0, 10, 0);
    this.scene.add(topLight);
  }

  createFallbackMaterial() {
    // 创建简化但高质量的球体贴图
    const canvas = document.createElement('canvas');
    canvas.width = 1024;
    canvas.height = 512; // 2:1 比例用于球体UV贴图
    const ctx = canvas.getContext('2d');

    // 绘制经典黄色背景
    ctx.fillStyle = '#fdd835';
    ctx.fillRect(0, 0, 1024, 512);

    // 绘制主笑脸 (正面中心位置)
    const centerX = 512;
    const centerY = 256;

    // 绘制眼睛
    ctx.fillStyle = '#000000';

    // 左眼
    ctx.beginPath();
    ctx.arc(centerX - 80, centerY - 50, 30, 0, Math.PI * 2);
    ctx.fill();

    // 右眼
    ctx.beginPath();
    ctx.arc(centerX + 80, centerY - 50, 30, 0, Math.PI * 2);
    ctx.fill();

    // 绘制笑容
    ctx.strokeStyle = '#000000';
    ctx.lineWidth = 15;
    ctx.lineCap = 'round';
    ctx.beginPath();
    ctx.arc(centerX, centerY + 30, 100, 0.2, Math.PI - 0.2);
    ctx.stroke();

    // 在侧面添加简化表情
    // 左侧
    const leftX = 200;
    ctx.fillStyle = 'rgba(0, 0, 0, 0.7)';
    ctx.beginPath();
    ctx.arc(leftX - 30, centerY - 20, 15, 0, Math.PI * 2);
    ctx.fill();
    ctx.beginPath();
    ctx.arc(leftX + 30, centerY - 20, 15, 0, Math.PI * 2);
    ctx.fill();
    ctx.strokeStyle = 'rgba(0, 0, 0, 0.7)';
    ctx.lineWidth = 8;
    ctx.beginPath();
    ctx.arc(leftX, centerY + 20, 40, 0.3, Math.PI - 0.3);
    ctx.stroke();

    // 右侧
    const rightX = 824;
    ctx.fillStyle = 'rgba(0, 0, 0, 0.7)';
    ctx.beginPath();
    ctx.arc(rightX - 30, centerY - 20, 15, 0, Math.PI * 2);
    ctx.fill();
    ctx.beginPath();
    ctx.arc(rightX + 30, centerY - 20, 15, 0, Math.PI * 2);
    ctx.fill();
    ctx.strokeStyle = 'rgba(0, 0, 0, 0.7)';
    ctx.lineWidth = 8;
    ctx.beginPath();
    ctx.arc(rightX, centerY + 20, 40, 0.3, Math.PI - 0.3);
    ctx.stroke();

    // 添加眼睛高光
    ctx.fillStyle = 'rgba(255, 255, 255, 0.8)';
    // 主脸左眼高光
    ctx.beginPath();
    ctx.arc(centerX - 90, centerY - 60, 8, 0, Math.PI * 2);
    ctx.fill();
    // 主脸右眼高光
    ctx.beginPath();
    ctx.arc(centerX + 70, centerY - 60, 8, 0, Math.PI * 2);
    ctx.fill();

    // 创建贴图
    const texture = new THREE.CanvasTexture(canvas);
    texture.wrapS = THREE.RepeatWrapping;
    texture.wrapT = THREE.ClampToEdgeWrapping;

    // 创建材质
    const material = new THREE.MeshPhongMaterial({
      map: texture,
      shininess: 100,
      specular: 0x666666
    });

    // 应用材质
    if (this.sphere) {
      this.sphere.material = material;
      console.log('笑脸贴图应用成功');
    } else {
      console.error('球体未创建，无法应用材质');
    }
  }

  setupEventListeners() {
    const canvas = this.renderer.domElement;

    // 确保canvas可以接收事件
    canvas.style.cursor = 'grab';
    canvas.style.userSelect = 'none';
    canvas.style.touchAction = 'none'; // 防止触摸滚动
    canvas.tabIndex = 0; // 使canvas可以获得焦点

    // 鼠标事件 - 使用更可靠的事件绑定
    canvas.addEventListener('mousedown', (e) => {
      e.preventDefault();
      e.stopPropagation();
      console.log('Mouse down detected');
      this.onMouseDown(e);
    }, { passive: false });

    // 将mousemove和mouseup绑定到document以确保在拖拽时不会丢失事件
    document.addEventListener('mousemove', (e) => {
      if (this.isDragging) {
        e.preventDefault();
        this.onMouseMove(e);
      }
    }, { passive: false });

    document.addEventListener('mouseup', (e) => {
      if (this.isDragging) {
        e.preventDefault();
        this.onMouseUp();
      }
    });

    // 触摸事件
    canvas.addEventListener('touchstart', (e) => {
      e.preventDefault();
      e.stopPropagation();
      console.log('Touch start detected');
      this.onTouchStart(e);
    }, { passive: false });

    document.addEventListener('touchmove', (e) => {
      if (this.isDragging) {
        e.preventDefault();
        this.onTouchMove(e);
      }
    }, { passive: false });

    document.addEventListener('touchend', (e) => {
      if (this.isDragging) {
        e.preventDefault();
        this.onTouchEnd();
      }
    });

    // 防止右键菜单
    canvas.addEventListener('contextmenu', (e) => e.preventDefault());

    // 窗口大小调整
    window.addEventListener('resize', () => this.onWindowResize());

    // 烟花事件 - 只在非canvas区域触发
    document.addEventListener('click', (e) => {
      if (e.target !== canvas && !this.isDragging) {
        this.createFirework(e);
      }
    });
    document.addEventListener('keydown', (e) => this.onKeyDown(e));
    document.addEventListener('dblclick', (e) => {
      if (e.target !== canvas) {
        this.createSuperFirework(e);
      }
    });
  }

  onMouseDown(event) {
    this.isDragging = true;
    this.previousMousePosition = {
      x: event.clientX,
      y: event.clientY
    };
    this.renderer.domElement.style.cursor = 'grabbing';
    // 停止自动旋转
    this.rotationVelocity.x = 0;
    this.rotationVelocity.y = 0;
    console.log('Dragging started at:', this.previousMousePosition);
  }

  onMouseMove(event) {
    if (!this.isDragging) return;

    const deltaMove = {
      x: event.clientX - this.previousMousePosition.x,
      y: event.clientY - this.previousMousePosition.y
    };

    // 提高旋转敏感度并添加平滑处理
    const sensitivity = 0.01;
    this.rotationVelocity.x = deltaMove.y * sensitivity;
    this.rotationVelocity.y = deltaMove.x * sensitivity;

    // 直接应用旋转以获得即时反馈
    if (this.sphere) {
      this.sphere.rotation.x += this.rotationVelocity.x;
      this.sphere.rotation.y += this.rotationVelocity.y;
    }

    console.log('Delta:', deltaMove, 'Velocity:', this.rotationVelocity);

    this.previousMousePosition = {
      x: event.clientX,
      y: event.clientY
    };
  }

  onMouseUp() {
    if (this.isDragging) {
      this.isDragging = false;
      this.renderer.domElement.style.cursor = 'grab';
      // 保持一些惯性
      this.rotationVelocity.x *= 0.3;
      this.rotationVelocity.y *= 0.3;
      console.log('Dragging stopped, inertia:', this.rotationVelocity);
    }
  }

  onTouchStart(event) {
    if (event.touches.length === 1) {
      this.isDragging = true;
      this.previousMousePosition = {
        x: event.touches[0].clientX,
        y: event.touches[0].clientY
      };
      // 停止自动旋转
      this.rotationVelocity.x = 0;
      this.rotationVelocity.y = 0;
      console.log('Touch dragging started');
    }
  }

  onTouchMove(event) {
    if (!this.isDragging || event.touches.length !== 1) return;

    const deltaMove = {
      x: event.touches[0].clientX - this.previousMousePosition.x,
      y: event.touches[0].clientY - this.previousMousePosition.y
    };

    // 使用与鼠标相同的敏感度
    const sensitivity = 0.01;
    this.rotationVelocity.x = deltaMove.y * sensitivity;
    this.rotationVelocity.y = deltaMove.x * sensitivity;

    // 直接应用旋转
    if (this.sphere) {
      this.sphere.rotation.x += this.rotationVelocity.x;
      this.sphere.rotation.y += this.rotationVelocity.y;
    }

    this.previousMousePosition = {
      x: event.touches[0].clientX,
      y: event.touches[0].clientY
    };
  }

  onTouchEnd() {
    if (this.isDragging) {
      this.isDragging = false;
      // 保持一些惯性
      this.rotationVelocity.x *= 0.3;
      this.rotationVelocity.y *= 0.3;
      console.log('Touch dragging stopped');
    }
  }

  onWindowResize() {
    const container = document.getElementById('container');
    this.camera.aspect = container.clientWidth / container.clientHeight;
    this.camera.updateProjectionMatrix();
    this.renderer.setSize(container.clientWidth, container.clientHeight);
  }

  animate() {
    requestAnimationFrame(() => this.animate());

    // 应用旋转
    if (this.sphere) {
      // 只在非拖拽状态下应用惯性旋转
      if (!this.isDragging) {
        this.sphere.rotation.x += this.rotationVelocity.x;
        this.sphere.rotation.y += this.rotationVelocity.y;

        // 应用阻尼
        this.rotationVelocity.x *= this.damping;
        this.rotationVelocity.y *= this.damping;

        // 添加轻微的自动旋转（当没有用户交互时）
        if (Math.abs(this.rotationVelocity.x) < 0.001 && Math.abs(this.rotationVelocity.y) < 0.001) {
          this.sphere.rotation.y += 0.003;
        }
      }
    }

    this.renderer.render(this.scene, this.camera);
  }

  // 烟花效果
  createFirework(event) {
    // 避免在球体上点击时触发烟花
    if (event.target === this.renderer.domElement) return;
    
    const fireworksContainer = document.getElementById('emoji-fireworks');
    const firework = document.createElement('div');
    firework.className = 'firework';
    firework.style.left = event.clientX + 'px';
    firework.style.top = event.clientY + 'px';
    
    const emojis = ['😊', '😄', '🎉', '✨', '🌟', '💫', '🎆', '🎇'];
    const colors = ['#ff6b6b', '#4ecdc4', '#45b7d1', '#96ceb4', '#ffeaa7', '#dda0dd', '#98d8c8'];
    
    for (let i = 0; i < 12; i++) {
      const particle = document.createElement('div');
      particle.className = 'firework-particle';
      particle.textContent = emojis[Math.floor(Math.random() * emojis.length)];
      particle.style.backgroundColor = colors[Math.floor(Math.random() * colors.length)];
      
      const angle = (i / 12) * Math.PI * 2;
      const velocity = 50 + Math.random() * 50;
      const x = Math.cos(angle) * velocity;
      const y = Math.sin(angle) * velocity;
      
      particle.style.transform = `translate(${x}px, ${y}px)`;
      firework.appendChild(particle);
    }
    
    fireworksContainer.appendChild(firework);
    
    setTimeout(() => {
      fireworksContainer.removeChild(firework);
    }, 1000);
  }

  createSuperFirework(event) {
    for (let i = 0; i < 5; i++) {
      setTimeout(() => {
        this.createFirework({
          clientX: event.clientX + (Math.random() - 0.5) * 100,
          clientY: event.clientY + (Math.random() - 0.5) * 100,
          target: document.body
        });
      }, i * 100);
    }
  }

  onKeyDown(event) {
    switch(event.code) {
      case 'Space':
        event.preventDefault();
        this.createFirework({
          clientX: Math.random() * window.innerWidth,
          clientY: Math.random() * window.innerHeight,
          target: document.body
        });
        break;
      case 'KeyF':
        event.preventDefault();
        this.createFireworkCircle();
        break;
    }
  }

  createFireworkCircle() {
    const centerX = window.innerWidth / 2;
    const centerY = window.innerHeight / 2;
    const radius = 200;
    
    for (let i = 0; i < 8; i++) {
      const angle = (i / 8) * Math.PI * 2;
      const x = centerX + Math.cos(angle) * radius;
      const y = centerY + Math.sin(angle) * radius;
      
      setTimeout(() => {
        this.createFirework({
          clientX: x,
          clientY: y,
          target: document.body
        });
      }, i * 100);
    }
  }
}

// 初始化
document.addEventListener('DOMContentLoaded', () => {
  const sphere3D = new TrueSphere3D();

  // 添加测试按钮
  const testButton = document.createElement('button');
  testButton.textContent = '测试旋转';
  testButton.style.position = 'fixed';
  testButton.style.top = '10px';
  testButton.style.right = '10px';
  testButton.style.zIndex = '1000';
  testButton.style.padding = '10px';
  testButton.style.backgroundColor = '#4CAF50';
  testButton.style.color = 'white';
  testButton.style.border = 'none';
  testButton.style.borderRadius = '5px';
  testButton.style.cursor = 'pointer';

  testButton.addEventListener('click', () => {
    if (sphere3D.sphere) {
      sphere3D.rotationVelocity.x = 0.1;
      sphere3D.rotationVelocity.y = 0.1;
      console.log('Test rotation applied');
    }
  });

  document.body.appendChild(testButton);

  console.log('TrueSphere3D initialized');
});

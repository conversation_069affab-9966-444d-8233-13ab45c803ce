# Simple Conda Environment Activation Script
# PowerShell version to avoid encoding issues

Write-Host "Conda Environment Activation Script" -ForegroundColor Green
Write-Host "====================================" -ForegroundColor Green

# Set Anaconda path
$ANACONDA_PATH = "D:\anaconda"

# Check if Anaconda exists
if (-not (Test-Path $ANACONDA_PATH)) {
    Write-Host "ERROR: Cannot find <PERSON><PERSON><PERSON> at $ANACONDA_PATH" -ForegroundColor Red
    Write-Host "Please check your Anaconda installation path" -ForegroundColor Yellow
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host "OK: Found Anaconda at $ANACONDA_PATH" -ForegroundColor Green

try {
    # Add conda to PATH
    $env:PATH = "$ANACONDA_PATH\Scripts;$ANACONDA_PATH\condabin;$env:PATH"
    
    Write-Host "INFO: Testing conda command..." -ForegroundColor Cyan
    
    # Test conda
    $condaVersion = & conda --version 2>$null
    if ($LASTEXITCODE -eq 0) {
        Write-Host "OK: Conda command works - $condaVersion" -ForegroundColor Green
    } else {
        Write-Host "WARNING: Conda command failed, trying alternative..." -ForegroundColor Yellow
        & "$ANACONDA_PATH\Scripts\activate.bat" "$ANACONDA_PATH"
    }
    
    Write-Host "INFO: Available environments:" -ForegroundColor Cyan
    & conda env list
    
    Write-Host "INFO: Activating tf-env environment..." -ForegroundColor Cyan
    & conda activate tf-env
    
    if ($env:CONDA_DEFAULT_ENV -eq "tf-env") {
        Write-Host "SUCCESS: tf-env environment activated!" -ForegroundColor Green
        Write-Host "Current environment: $env:CONDA_DEFAULT_ENV" -ForegroundColor Green
        Write-Host "Python path:" -ForegroundColor Cyan
        & where.exe python
    } else {
        Write-Host "ERROR: Failed to activate tf-env environment" -ForegroundColor Red
        Write-Host "Available environments:" -ForegroundColor Yellow
        & conda env list
    }
    
} catch {
    Write-Host "ERROR: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`nScript completed. You can now use conda commands." -ForegroundColor Green
Read-Host "Press Enter to exit"

@echo off
chcp 65001 >nul
REM ========================================
REM Auto-activate Anaconda tf-env environment
REM Solve the problem of manually starting Anaconda after boot
REM ========================================

echo [INFO] Auto-configuring Anaconda environment...

REM Set Anaconda installation path
set ANACONDA_PATH=D:\anaconda

REM Check if Anaconda exists
if not exist "%ANACONDA_PATH%" (
    echo [ERROR] Cannot find Anaconda directory: %ANACONDA_PATH%
    echo Please check if Anaconda installation path is correct
    pause
    exit /b 1
)

echo [OK] Found Anaconda directory: %ANACONDA_PATH%

REM Initialize conda environment
echo [INFO] Initializing conda environment...
call "%ANACONDA_PATH%\Scripts\activate.bat" "%ANACONDA_PATH%"

REM Activate tf-env environment
echo [INFO] Activating tf-env environment...
call conda activate tf-env

REM Check if environment is activated successfully
if "%CONDA_DEFAULT_ENV%"=="tf-env" (
    echo [OK] tf-env environment activated successfully!
    echo [SUCCESS] You can now run Python code normally
) else (
    echo [ERROR] tf-env environment activation failed
    echo Please check if environment exists: conda env list
)

REM Display current environment information
echo.
echo [INFO] Current environment information:
echo Environment name: %CONDA_DEFAULT_ENV%
echo Python path:
where python
echo.

REM Keep window open
echo [TIP] This window will remain open for you to run Python code
echo [TIP] Or you can open a new command window, tf-env is configured
echo.
cmd /k

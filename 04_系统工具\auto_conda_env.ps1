# ========================================
# 自动激活Anaconda tf-env环境PowerShell脚本
# 解决每次开机需要手动启动Anaconda的问题
# ========================================

Write-Host "🚀 正在自动配置Anaconda环境..." -ForegroundColor Green

# 设置Anaconda安装路径
$ANACONDA_PATH = "D:\anaconda"

# 检查Anaconda是否存在
if (-not (Test-Path $ANACONDA_PATH)) {
    Write-Host "❌ 错误：找不到Anaconda安装目录 $ANACONDA_PATH" -ForegroundColor Red
    Write-Host "请检查Anaconda安装路径是否正确" -ForegroundColor Yellow
    Read-Host "按任意键退出"
    exit 1
}

Write-Host "✅ 找到Anaconda安装目录: $ANACONDA_PATH" -ForegroundColor Green

try {
    # 初始化conda
    Write-Host "🔧 正在初始化conda环境..." -ForegroundColor Cyan
    
    # 添加conda到PATH
    $env:PATH = "$ANACONDA_PATH\Scripts;$ANACONDA_PATH\condabin;$env:PATH"
    
    # 初始化conda for PowerShell
    & "$ANACONDA_PATH\Scripts\conda.exe" init powershell --no-user
    
    # 激活tf-env环境
    Write-Host "🎯 正在激活tf-env环境..." -ForegroundColor Cyan
    & "$ANACONDA_PATH\Scripts\conda.exe" activate tf-env
    
    Write-Host "✅ tf-env环境配置完成！" -ForegroundColor Green
    Write-Host "🎉 现在可以正常运行Python代码了" -ForegroundColor Green
    
    # 显示当前环境信息
    Write-Host "`n📋 当前环境信息：" -ForegroundColor Yellow
    Write-Host "Python路径: " -NoNewline
    & where.exe python
    
    Write-Host "`n💡 提示：环境已配置完成，您可以开始使用了" -ForegroundColor Cyan
    
} catch {
    Write-Host "❌ 配置过程中出现错误：$($_.Exception.Message)" -ForegroundColor Red
    Write-Host "请手动启动Anaconda Navigator或检查安装" -ForegroundColor Yellow
}

Write-Host "`n按任意键继续..." -ForegroundColor Gray
Read-Host

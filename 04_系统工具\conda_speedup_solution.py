#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Conda启动慢问题的完整解决方案
"""

import os
import subprocess
import sys
from pathlib import Path

def create_optimized_condarc():
    """创建优化的.condarc配置"""
    print("📝 创建优化的conda配置...")
    
    condarc_content = """# 优化的conda配置文件
# 使用更快的solver
solver: libmamba

# 禁用不必要的功能
auto_update_conda: false
anaconda_anon_usage: false
notify_outdated_conda: false
auto_activate_base: false

# 通道配置 - 使用国内镜像
channel_priority: strict
channels:
  - https://mirrors.tuna.tsinghua.edu.cn/anaconda/pkgs/main/
  - https://mirrors.tuna.tsinghua.edu.cn/anaconda/pkgs/free/
  - https://mirrors.tuna.tsinghua.edu.cn/anaconda/pkgs/r/
  - https://mirrors.tuna.tsinghua.edu.cn/anaconda/cloud/conda-forge/
  - https://mirrors.tuna.tsinghua.edu.cn/anaconda/cloud/pytorch/
  - defaults

# 性能优化
use_pip: true
pip_interop_enabled: true
always_yes: false
changeps1: true

# 缓存设置
local_repodata_ttl: 1
repodata_threads: 2

# SSL设置
ssl_verify: true

# 减少网络超时
remote_connect_timeout_secs: 30.0
remote_read_timeout_secs: 60.0
remote_max_retries: 3
"""
    
    try:
        home_dir = Path.home()
        condarc_path = home_dir / ".condarc"
        
        # 备份现有配置
        if condarc_path.exists():
            backup_path = home_dir / ".condarc.backup"
            condarc_path.rename(backup_path)
            print(f"📋 已备份原配置到: {backup_path}")
        
        with open(condarc_path, 'w', encoding='utf-8') as f:
            f.write(condarc_content)
        
        print(f"✅ 新配置已创建: {condarc_path}")
        return True
    except Exception as e:
        print(f"❌ 创建配置失败: {e}")
        return False

def create_startup_script():
    """创建快速启动脚本"""
    print("🚀 创建快速启动脚本...")
    
    # Windows批处理脚本
    bat_content = """@echo off
REM 快速启动conda环境脚本
title Conda快速启动

echo ========================================
echo 正在启动Conda环境...
echo ========================================

REM 设置环境变量以加速启动
set CONDA_ALWAYS_YES=true
set CONDA_AUTO_UPDATE_CONDA=false

REM 激活conda base环境
call D:\\anaconda\\Scripts\\activate.bat D:\\anaconda

REM 激活tf-env环境
echo 激活tf-env环境...
call conda activate tf-env

REM 验证环境
if "%CONDA_DEFAULT_ENV%"=="tf-env" (
    echo ✅ 环境激活成功！
    echo 当前环境: %CONDA_DEFAULT_ENV%
    python --version
) else (
    echo ❌ 环境激活失败
)

echo ========================================
echo 环境准备完成！
echo ========================================
cmd /k
"""
    
    try:
        bat_path = Path("quick_conda.bat")
        with open(bat_path, 'w', encoding='utf-8') as f:
            f.write(bat_content)
        print(f"✅ 启动脚本已创建: {bat_path}")
        
        # Python启动脚本
        py_content = """#!/usr/bin/env python3
import os
import sys
import subprocess

def quick_start():
    print("🚀 快速启动Python环境...")
    
    # 检查是否在conda环境中
    if 'CONDA_DEFAULT_ENV' in os.environ:
        env_name = os.environ['CONDA_DEFAULT_ENV']
        print(f"✅ 当前环境: {env_name}")
    else:
        print("⚠️ 未检测到conda环境")
    
    # 检查关键包
    try:
        import tensorflow as tf
        import numpy as np
        import pandas as pd
        import matplotlib.pyplot as plt
        
        print("✅ 所有关键包已加载")
        print(f"TensorFlow版本: {tf.__version__}")
        print(f"NumPy版本: {np.__version__}")
        print(f"Pandas版本: {pd.__version__}")
        
        return True
    except ImportError as e:
        print(f"❌ 包导入失败: {e}")
        return False

if __name__ == "__main__":
    quick_start()
"""
        
        py_path = Path("quick_start.py")
        with open(py_path, 'w', encoding='utf-8') as f:
            f.write(py_content)
        print(f"✅ Python启动脚本已创建: {py_path}")
        
        return True
    except Exception as e:
        print(f"❌ 创建启动脚本失败: {e}")
        return False

def optimize_system():
    """系统级优化"""
    print("⚙️ 执行系统级优化...")
    
    optimizations = [
        ("conda config --set solver libmamba", "设置快速solver"),
        ("conda config --set channel_priority strict", "设置严格通道优先级"),
        ("conda config --set auto_update_conda false", "禁用自动更新"),
        ("conda config --set anaconda_anon_usage false", "禁用使用统计"),
        ("conda clean --all -y", "清理缓存"),
    ]
    
    for cmd, desc in optimizations:
        try:
            print(f"执行: {desc}")
            result = subprocess.run(cmd.split(), capture_output=True, text=True, timeout=30)
            if result.returncode == 0:
                print(f"✅ {desc} - 成功")
            else:
                print(f"⚠️ {desc} - 警告: {result.stderr}")
        except Exception as e:
            print(f"❌ {desc} - 失败: {e}")

def create_memory_optimization():
    """内存优化建议"""
    print("🧠 内存优化建议:")
    
    suggestions = [
        "1. 关闭不必要的程序和浏览器标签页",
        "2. 重启计算机清理内存",
        "3. 增加虚拟内存大小",
        "4. 使用任务管理器结束高内存占用进程",
        "5. 考虑升级内存条"
    ]
    
    for suggestion in suggestions:
        print(f"   {suggestion}")

def create_troubleshooting_guide():
    """创建故障排除指南"""
    print("📚 创建故障排除指南...")
    
    guide_content = """# Conda启动慢问题故障排除指南

## 问题症状
- conda命令执行缓慢
- 环境激活时间长
- 包安装/更新缓慢

## 解决步骤

### 1. 立即解决方案
```bash
# 清理缓存
conda clean --all

# 使用快速solver
conda config --set solver libmamba

# 禁用自动更新
conda config --set auto_update_conda false
```

### 2. 网络优化
```bash
# 添加国内镜像源
conda config --add channels https://mirrors.tuna.tsinghua.edu.cn/anaconda/pkgs/main/
conda config --add channels https://mirrors.tuna.tsinghua.edu.cn/anaconda/pkgs/free/
conda config --set channel_priority strict
```

### 3. 系统优化
- 重启计算机
- 关闭杀毒软件实时扫描（临时）
- 清理磁盘空间
- 检查内存使用情况

### 4. 替代方案
```bash
# 安装mamba（更快的conda替代品）
conda install mamba -c conda-forge

# 使用mamba替代conda
mamba install package_name
mamba create -n env_name python=3.8
```

### 5. 环境重建（最后手段）
```bash
# 导出环境
conda env export -n tf-env > tf-env.yml

# 删除环境
conda env remove -n tf-env

# 重建环境
conda env create -f tf-env.yml
```

## 预防措施
1. 定期清理conda缓存
2. 使用国内镜像源
3. 避免在同一时间运行多个conda命令
4. 保持系统内存充足
5. 定期更新conda到最新版本

## 性能监控
使用以下命令监控conda性能：
```bash
# 检查conda信息
conda info

# 检查环境列表
conda env list

# 检查包列表
conda list
```
"""
    
    try:
        guide_path = Path("conda_troubleshooting.md")
        with open(guide_path, 'w', encoding='utf-8') as f:
            f.write(guide_content)
        print(f"✅ 故障排除指南已创建: {guide_path}")
        return True
    except Exception as e:
        print(f"❌ 创建指南失败: {e}")
        return False

def main():
    """主函数"""
    print("🔧 Conda启动慢问题完整解决方案")
    print("=" * 50)
    
    # 1. 创建优化配置
    create_optimized_condarc()
    
    # 2. 系统优化
    optimize_system()
    
    # 3. 创建启动脚本
    create_startup_script()
    
    # 4. 内存优化建议
    create_memory_optimization()
    
    # 5. 创建故障排除指南
    create_troubleshooting_guide()
    
    print("\n" + "=" * 50)
    print("✨ 解决方案部署完成！")
    print("\n📋 后续步骤:")
    print("1. 重启终端或命令提示符")
    print("2. 使用 quick_conda.bat 快速启动")
    print("3. 如果问题持续，参考 conda_troubleshooting.md")
    print("4. 考虑重启计算机清理内存")
    
    print("\n🚀 快速测试:")
    print("运行以下命令测试效果:")
    print("   conda --version")
    print("   conda env list")

if __name__ == "__main__":
    main()

@echo off
REM ========================================
REM 创建桌面快捷方式脚本
REM 为常用工具创建桌面快捷方式
REM ========================================

echo 🔗 创建桌面快捷方式工具
echo ========================================

REM 获取当前脚本所在目录
set SCRIPT_DIR=%~dp0
set DESKTOP=%USERPROFILE%\Desktop

echo ✅ 脚本目录：%SCRIPT_DIR%
echo ✅ 桌面目录：%DESKTOP%
echo.

REM 创建VBS脚本来生成快捷方式
set VBS_FILE=%TEMP%\create_shortcut.vbs

echo 🔧 正在创建快捷方式...

REM 1. 风电预测项目启动器快捷方式
echo Set oWS = WScript.CreateObject("WScript.Shell") > "%VBS_FILE%"
echo sLinkFile = "%DESKTOP%\🌪️ 风电功率预测系统.lnk" >> "%VBS_FILE%"
echo Set oLink = oWS.CreateShortcut(sLinkFile) >> "%VBS_FILE%"
echo oLink.TargetPath = "%SCRIPT_DIR%start_wind_power_project.bat" >> "%VBS_FILE%"
echo oLink.WorkingDirectory = "%SCRIPT_DIR%" >> "%VBS_FILE%"
echo oLink.Description = "风电功率预测系统快速启动器" >> "%VBS_FILE%"
echo oLink.Save >> "%VBS_FILE%"

cscript //nologo "%VBS_FILE%"
if exist "%DESKTOP%\🌪️ 风电功率预测系统.lnk" (
    echo ✅ 已创建：🌪️ 风电功率预测系统.lnk
) else (
    echo ❌ 创建失败：风电功率预测系统快捷方式
)

REM 2. Anaconda环境修复工具快捷方式
echo Set oWS = WScript.CreateObject("WScript.Shell") > "%VBS_FILE%"
echo sLinkFile = "%DESKTOP%\🔧 修复Anaconda环境.lnk" >> "%VBS_FILE%"
echo Set oLink = oWS.CreateShortcut(sLinkFile) >> "%VBS_FILE%"
echo oLink.TargetPath = "%SCRIPT_DIR%fix_conda_path.bat" >> "%VBS_FILE%"
echo oLink.WorkingDirectory = "%SCRIPT_DIR%" >> "%VBS_FILE%"
echo oLink.Description = "修复Anaconda环境变量配置" >> "%VBS_FILE%"
echo oLink.Save >> "%VBS_FILE%"

cscript //nologo "%VBS_FILE%"
if exist "%DESKTOP%\🔧 修复Anaconda环境.lnk" (
    echo ✅ 已创建：🔧 修复Anaconda环境.lnk
) else (
    echo ❌ 创建失败：修复Anaconda环境快捷方式
)

REM 3. 自动激活环境快捷方式
echo Set oWS = WScript.CreateObject("WScript.Shell") > "%VBS_FILE%"
echo sLinkFile = "%DESKTOP%\⚡ 激活tf-env环境.lnk" >> "%VBS_FILE%"
echo Set oLink = oWS.CreateShortcut(sLinkFile) >> "%VBS_FILE%"
echo oLink.TargetPath = "%SCRIPT_DIR%auto_conda_env.bat" >> "%VBS_FILE%"
echo oLink.WorkingDirectory = "%SCRIPT_DIR%" >> "%VBS_FILE%"
echo oLink.Description = "自动激活Anaconda tf-env环境" >> "%VBS_FILE%"
echo oLink.Save >> "%VBS_FILE%"

cscript //nologo "%VBS_FILE%"
if exist "%DESKTOP%\⚡ 激活tf-env环境.lnk" (
    echo ✅ 已创建：⚡ 激活tf-env环境.lnk
) else (
    echo ❌ 创建失败：激活tf-env环境快捷方式
)

REM 清理临时文件
del "%VBS_FILE%" 2>nul

echo.
echo 🎉 桌面快捷方式创建完成！
echo.
echo 📋 已创建的快捷方式：
echo 1. 🌪️ 风电功率预测系统 - 一键启动项目
echo 2. 🔧 修复Anaconda环境 - 修复环境变量（需管理员权限）
echo 3. ⚡ 激活tf-env环境 - 手动激活环境
echo.
echo 💡 使用建议：
echo 1. 首次使用请先运行"修复Anaconda环境"（以管理员身份）
echo 2. 然后就可以直接使用"风电功率预测系统"启动项目了
echo 3. 如果遇到问题，可以使用"激活tf-env环境"手动激活
echo.

pause

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Conda环境诊断和修复脚本
"""

import os
import sys
import time
import subprocess
import psutil
from pathlib import Path

def check_system_resources():
    """检查系统资源使用情况"""
    print("🖥️ 系统资源检查:")
    print(f"   CPU使用率: {psutil.cpu_percent(interval=1):.1f}%")
    print(f"   内存使用率: {psutil.virtual_memory().percent:.1f}%")
    print(f"   磁盘使用率: {psutil.disk_usage('/').percent:.1f}%")
    
    # 检查是否有大量进程占用资源
    high_cpu_processes = []
    for proc in psutil.process_iter(['pid', 'name', 'cpu_percent']):
        try:
            if proc.info['cpu_percent'] > 10:
                high_cpu_processes.append(proc.info)
        except (psutil.NoSuchProcess, psutil.AccessDenied):
            pass
    
    if high_cpu_processes:
        print("⚠️ 高CPU使用率进程:")
        for proc in high_cpu_processes[:5]:  # 只显示前5个
            print(f"   {proc['name']} (PID: {proc['pid']}) - {proc['cpu_percent']:.1f}%")

def check_conda_installation():
    """检查conda安装状态"""
    print("\n🔍 Conda安装检查:")
    
    try:
        result = subprocess.run(['conda', '--version'], capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            print(f"✅ Conda版本: {result.stdout.strip()}")
        else:
            print("❌ Conda命令执行失败")
            return False
    except subprocess.TimeoutExpired:
        print("⏰ Conda命令超时 - 这可能是启动慢的原因")
        return False
    except FileNotFoundError:
        print("❌ 找不到conda命令")
        return False
    
    return True

def check_environment_integrity():
    """检查环境完整性"""
    print("\n🔧 环境完整性检查:")
    
    # 检查tf-env环境
    try:
        result = subprocess.run(['conda', 'list', '-n', 'tf-env'], 
                              capture_output=True, text=True, timeout=30)
        if result.returncode == 0:
            lines = result.stdout.strip().split('\n')
            package_count = len([line for line in lines if not line.startswith('#') and line.strip()])
            print(f"✅ tf-env环境包含 {package_count} 个包")
            
            # 检查关键包
            key_packages = ['tensorflow', 'numpy', 'pandas', 'matplotlib', 'scikit-learn']
            missing_packages = []
            
            for package in key_packages:
                if package not in result.stdout:
                    missing_packages.append(package)
            
            if missing_packages:
                print(f"⚠️ 缺少关键包: {', '.join(missing_packages)}")
            else:
                print("✅ 所有关键包都已安装")
                
        else:
            print("❌ 无法检查tf-env环境")
            return False
    except subprocess.TimeoutExpired:
        print("⏰ 环境检查超时")
        return False
    
    return True

def check_disk_space():
    """检查磁盘空间"""
    print("\n💾 磁盘空间检查:")
    
    # 检查conda安装目录
    conda_path = Path("D:/anaconda")
    if conda_path.exists():
        try:
            total_size = sum(f.stat().st_size for f in conda_path.rglob('*') if f.is_file())
            size_gb = total_size / (1024**3)
            print(f"📁 Conda安装大小: {size_gb:.2f} GB")
            
            # 检查可用空间
            free_space = psutil.disk_usage(str(conda_path.drive)).free / (1024**3)
            print(f"💿 可用磁盘空间: {free_space:.2f} GB")
            
            if free_space < 5:
                print("⚠️ 磁盘空间不足，可能影响conda性能")
                return False
        except Exception as e:
            print(f"❌ 无法检查磁盘使用情况: {e}")
    
    return True

def measure_conda_startup_time():
    """测量conda启动时间"""
    print("\n⏱️ Conda启动时间测试:")
    
    commands = [
        ('conda --version', 'conda版本查询'),
        ('conda info', 'conda信息查询'),
        ('conda env list', '环境列表查询')
    ]
    
    for cmd, desc in commands:
        print(f"测试: {desc}")
        start_time = time.time()
        
        try:
            result = subprocess.run(cmd.split(), capture_output=True, text=True, timeout=30)
            end_time = time.time()
            duration = end_time - start_time
            
            if result.returncode == 0:
                print(f"✅ 耗时: {duration:.2f}秒")
                if duration > 10:
                    print("⚠️ 启动时间过长")
            else:
                print(f"❌ 命令失败: {result.stderr}")
                
        except subprocess.TimeoutExpired:
            print("⏰ 命令超时（>30秒）")
        except Exception as e:
            print(f"❌ 执行异常: {e}")

def suggest_solutions():
    """提供解决方案建议"""
    print("\n💡 解决方案建议:")
    print("1. 🔄 重启计算机（清理内存和进程）")
    print("2. 🧹 运行 'conda clean --all' 清理缓存")
    print("3. 🚀 使用 mamba 替代 conda（更快的包管理器）")
    print("4. 🔧 重建tf-env环境")
    print("5. 💾 清理磁盘空间")
    print("6. 🛡️ 检查杀毒软件是否影响conda")
    print("7. 🌐 使用国内镜像源")
    
    print("\n🔧 快速修复命令:")
    print("   conda clean --all")
    print("   conda update conda")
    print("   conda install mamba -c conda-forge")

def install_mamba():
    """安装mamba作为更快的替代方案"""
    print("\n🚀 安装mamba（更快的conda替代品）:")
    
    try:
        print("正在安装mamba...")
        result = subprocess.run(['conda', 'install', 'mamba', '-c', 'conda-forge', '-y'], 
                              capture_output=True, text=True, timeout=300)
        
        if result.returncode == 0:
            print("✅ mamba安装成功！")
            print("💡 使用建议: 用 'mamba' 替代 'conda' 命令，如:")
            print("   mamba install package_name")
            print("   mamba create -n env_name python=3.8")
            return True
        else:
            print(f"❌ mamba安装失败: {result.stderr}")
            return False
            
    except subprocess.TimeoutExpired:
        print("⏰ mamba安装超时")
        return False
    except Exception as e:
        print(f"❌ 安装异常: {e}")
        return False

def main():
    """主函数"""
    print("🔍 Conda环境诊断工具")
    print("=" * 50)
    
    # 系统资源检查
    check_system_resources()
    
    # Conda安装检查
    if not check_conda_installation():
        print("❌ Conda安装有问题，请重新安装conda")
        return
    
    # 环境完整性检查
    check_environment_integrity()
    
    # 磁盘空间检查
    check_disk_space()
    
    # 启动时间测试
    measure_conda_startup_time()
    
    # 提供解决方案
    suggest_solutions()
    
    # 询问是否安装mamba
    print("\n" + "=" * 50)
    response = input("是否要安装mamba来提高性能？(y/n): ").lower().strip()
    if response in ['y', 'yes', '是']:
        install_mamba()
    
    print("\n✨ 诊断完成！")

if __name__ == "__main__":
    try:
        import psutil
    except ImportError:
        print("正在安装psutil...")
        subprocess.run([sys.executable, '-m', 'pip', 'install', 'psutil'])
        import psutil
    
    main()

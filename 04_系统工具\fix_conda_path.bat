@echo off
chcp 65001 >nul
REM ========================================
REM Fix Anaconda Environment Variables
REM Add Anaconda paths to system PATH
REM Requires Administrator privileges
REM ========================================

echo [INFO] Anaconda Environment Fix Tool
echo ========================================

REM Check if running as administrator
net session >nul 2>&1
if %errorLevel% == 0 (
    echo [OK] Administrator privileges detected
) else (
    echo [ERROR] This script requires administrator privileges
    echo Please right-click this file and select "Run as administrator"
    pause
    exit /b 1
)

REM Set Anaconda path
set ANACONDA_PATH=D:\anaconda

REM Check if Anaconda exists
if not exist "%ANACONDA_PATH%" (
    echo [ERROR] Cannot find Anaconda directory: %ANACONDA_PATH%
    echo Please modify ANACONDA_PATH variable in this script
    pause
    exit /b 1
)

echo [OK] Found Anaconda directory: %ANACONDA_PATH%

REM Paths to add to PATH
set PATHS_TO_ADD=%ANACONDA_PATH%;%ANACONDA_PATH%\Scripts;%ANACONDA_PATH%\condabin;%ANACONDA_PATH%\Library\bin

echo [INFO] Adding the following paths to system PATH:
echo %PATHS_TO_ADD%

REM Get current system PATH
for /f "tokens=2*" %%a in ('reg query "HKLM\SYSTEM\CurrentControlSet\Control\Session Manager\Environment" /v PATH 2^>nul') do set CURRENT_PATH=%%b

REM Check if path already exists
echo %CURRENT_PATH% | findstr /i "%ANACONDA_PATH%" >nul
if %errorLevel% == 0 (
    echo [WARNING] Anaconda paths already exist in system PATH
    echo Reconfiguring to ensure correct order...
)

REM Add paths to system PATH (put at front for priority)
set NEW_PATH=%PATHS_TO_ADD%;%CURRENT_PATH%

REM Update system PATH
reg add "HKLM\SYSTEM\CurrentControlSet\Control\Session Manager\Environment" /v PATH /t REG_EXPAND_SZ /d "%NEW_PATH%" /f

if %errorLevel% == 0 (
    echo [OK] System PATH updated successfully!
    echo.
    echo [INFO] Configuring conda initialization...

    REM Initialize conda for cmd
    "%ANACONDA_PATH%\Scripts\conda.exe" init cmd.exe

    REM Initialize conda for PowerShell
    "%ANACONDA_PATH%\Scripts\conda.exe" init powershell

    echo [OK] Conda initialization completed!
    echo.
    echo [SUCCESS] Configuration completed! Please restart your computer
    echo [TIP] After restart, you can directly use: conda activate tf-env
    echo.
) else (
    echo [ERROR] Failed to update system PATH
    echo Please check administrator privileges or add paths manually
)

echo Press any key to exit...
pause >nul

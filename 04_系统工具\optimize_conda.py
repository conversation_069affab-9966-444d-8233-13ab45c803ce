#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Conda环境优化脚本
解决conda启动慢的问题
"""

import os
import subprocess
import sys
from pathlib import Path

def run_command(cmd, description=""):
    """执行命令并显示结果"""
    print(f"\n{'='*50}")
    print(f"正在执行: {description}")
    print(f"命令: {cmd}")
    print(f"{'='*50}")
    
    try:
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=60)
        if result.returncode == 0:
            print("✅ 成功!")
            if result.stdout.strip():
                print(f"输出: {result.stdout.strip()}")
        else:
            print("❌ 失败!")
            if result.stderr.strip():
                print(f"错误: {result.stderr.strip()}")
        return result.returncode == 0
    except subprocess.TimeoutExpired:
        print("⏰ 命令超时")
        return False
    except Exception as e:
        print(f"❌ 异常: {e}")
        return False

def optimize_conda():
    """优化conda配置"""
    print("🚀 开始优化Conda环境...")
    
    # 1. 设置更快的solver
    print("\n1️⃣ 设置libmamba solver（更快的依赖解析器）")
    run_command("conda config --set solver libmamba", "设置libmamba solver")
    
    # 2. 禁用不必要的功能
    print("\n2️⃣ 禁用不必要的功能")
    run_command("conda config --set auto_update_conda false", "禁用自动更新conda")
    run_command("conda config --set anaconda_anon_usage false", "禁用匿名使用统计")
    run_command("conda config --set notify_outdated_conda false", "禁用过期通知")
    
    # 3. 优化通道配置
    print("\n3️⃣ 优化通道配置")
    run_command("conda config --set channel_priority strict", "设置严格通道优先级")
    
    # 4. 添加国内镜像源（提高下载速度）
    print("\n4️⃣ 添加国内镜像源")
    mirrors = [
        "https://mirrors.tuna.tsinghua.edu.cn/anaconda/pkgs/main/",
        "https://mirrors.tuna.tsinghua.edu.cn/anaconda/pkgs/free/",
        "https://mirrors.tuna.tsinghua.edu.cn/anaconda/pkgs/r/",
        "https://mirrors.tuna.tsinghua.edu.cn/anaconda/pkgs/pro/",
        "https://mirrors.tuna.tsinghua.edu.cn/anaconda/pkgs/msys2/"
    ]
    
    for mirror in mirrors:
        run_command(f"conda config --add channels {mirror}", f"添加镜像源: {mirror}")
    
    # 5. 清理缓存
    print("\n5️⃣ 清理conda缓存")
    run_command("conda clean --all -y", "清理所有缓存")
    
    # 6. 更新conda
    print("\n6️⃣ 更新conda到最新版本")
    run_command("conda update conda -y", "更新conda")
    
    print("\n🎉 Conda优化完成!")
    print("\n📋 建议的后续操作:")
    print("1. 重启终端或命令提示符")
    print("2. 重新激活您的conda环境")
    print("3. 如果问题仍然存在，考虑重建环境")

def create_conda_rc():
    """创建优化的.condarc配置文件"""
    print("\n📝 创建优化的.condarc配置文件...")
    
    condarc_content = """# Conda配置文件 - 优化版本
# 设置更快的solver
solver: libmamba

# 禁用不必要的功能
auto_update_conda: false
anaconda_anon_usage: false
notify_outdated_conda: false

# 通道配置
channel_priority: strict
channels:
  - https://mirrors.tuna.tsinghua.edu.cn/anaconda/pkgs/main/
  - https://mirrors.tuna.tsinghua.edu.cn/anaconda/pkgs/free/
  - https://mirrors.tuna.tsinghua.edu.cn/anaconda/pkgs/r/
  - https://mirrors.tuna.tsinghua.edu.cn/anaconda/pkgs/pro/
  - https://mirrors.tuna.tsinghua.edu.cn/anaconda/pkgs/msys2/
  - defaults

# 其他优化设置
always_yes: false
changeps1: true
use_pip: true
pip_interop_enabled: true

# SSL设置
ssl_verify: true

# 缓存设置
local_repodata_ttl: 1
"""
    
    try:
        home_dir = Path.home()
        condarc_path = home_dir / ".condarc"
        
        with open(condarc_path, 'w', encoding='utf-8') as f:
            f.write(condarc_content)
        
        print(f"✅ .condarc文件已创建: {condarc_path}")
        return True
    except Exception as e:
        print(f"❌ 创建.condarc文件失败: {e}")
        return False

def check_environment():
    """检查当前环境状态"""
    print("\n🔍 检查当前环境状态...")
    
    run_command("conda --version", "检查conda版本")
    run_command("conda info", "显示conda信息")
    run_command("conda env list", "列出所有环境")

if __name__ == "__main__":
    print("🔧 Conda环境优化工具")
    print("此工具将帮助您解决conda启动慢的问题")
    
    # 检查当前环境
    check_environment()
    
    # 创建优化配置
    create_conda_rc()
    
    # 执行优化
    optimize_conda()
    
    print("\n✨ 优化完成! 请重启终端后测试效果。")

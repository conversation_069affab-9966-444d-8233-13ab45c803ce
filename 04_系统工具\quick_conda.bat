@echo off
REM 快速启动conda环境脚本
title Conda快速启动

echo ========================================
echo 正在启动Conda环境...
echo ========================================

REM 设置环境变量以加速启动
set CONDA_ALWAYS_YES=true
set CONDA_AUTO_UPDATE_CONDA=false

REM 激活conda base环境
call D:\anaconda\Scripts\activate.bat D:\anaconda

REM 激活tf-env环境
echo 激活tf-env环境...
call conda activate tf-env

REM 验证环境
if "%CONDA_DEFAULT_ENV%"=="tf-env" (
    echo ✅ 环境激活成功！
    echo 当前环境: %CONDA_DEFAULT_ENV%
    python --version
) else (
    echo ❌ 环境激活失败
)

echo ========================================
echo 环境准备完成！
echo ========================================
cmd /k

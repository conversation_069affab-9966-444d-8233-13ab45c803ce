@echo off
chcp 65001 >nul
REM ========================================
REM Quick Fix for Anaconda Environment
REM Simple solution for conda activation issues
REM ========================================

echo [INFO] Quick Conda Environment Fix
echo ========================================

REM Set Anaconda path
set ANACONDA_PATH=D:\anaconda

REM Check if Anaconda exists
if not exist "%ANACONDA_PATH%" (
    echo [ERROR] Cannot find <PERSON><PERSON><PERSON> at: %ANACONDA_PATH%
    echo [INFO] Please check your Anaconda installation path
    echo [INFO] Common paths:
    echo   - C:\Users\<USER>\anaconda3
    echo   - C:\anaconda3
    echo   - D:\anaconda
    echo.
    set /p ANACONDA_PATH="Enter your Anaconda path: "
)

if not exist "%ANACONDA_PATH%" (
    echo [ERROR] Invalid Anaconda path: %ANACONDA_PATH%
    pause
    exit /b 1
)

echo [OK] Using Anaconda path: %ANACONDA_PATH%

REM Add conda to current session PATH
set PATH=%ANACONDA_PATH%\Scripts;%ANACONDA_PATH%\condabin;%PATH%

REM Test conda command
echo [INFO] Testing conda command...
conda --version >nul 2>&1
if %errorLevel% == 0 (
    echo [OK] Conda command is working
) else (
    echo [ERROR] Conda command not working
    echo [INFO] Trying to initialize conda...
    call "%ANACONDA_PATH%\Scripts\activate.bat" "%ANACONDA_PATH%"
)

REM List available environments
echo [INFO] Available conda environments:
conda env list

REM Try to activate tf-env
echo [INFO] Attempting to activate tf-env...
call conda activate tf-env

if "%CONDA_DEFAULT_ENV%"=="tf-env" (
    echo [SUCCESS] tf-env environment activated!
    echo [INFO] Environment: %CONDA_DEFAULT_ENV%
    echo [INFO] Python path:
    where python
    echo.
    echo [SUCCESS] You can now run your Python scripts!
    echo [TIP] To run the wind power prediction:
    echo   cd "01_Python项目\python作业"
    echo   python 风电功率预测模型_MLP.py
) else (
    echo [ERROR] Failed to activate tf-env environment
    echo [INFO] Available environments:
    conda env list
    echo.
    echo [TIP] If tf-env doesn't exist, create it with:
    echo   conda create -n tf-env python=3.8 tensorflow
)

echo.
echo [INFO] This window will stay open for you to use
cmd /k

@echo off
REM 快速启动conda环境脚本
REM 解决conda启动慢的问题

echo ========================================
echo 快速启动Conda环境
echo ========================================

REM 设置conda路径（根据您的安装路径调整）
set CONDA_PATH=D:\anaconda

REM 初始化conda（如果需要）
call "%CONDA_PATH%\Scripts\activate.bat" "%CONDA_PATH%"

REM 激活tf-env环境
echo 正在激活tf-env环境...
call conda activate tf-env

REM 检查环境是否成功激活
if "%CONDA_DEFAULT_ENV%"=="tf-env" (
    echo ✅ tf-env环境已成功激活
    echo 当前Python路径: 
    where python
    echo.
    echo 当前环境包列表:
    conda list --name tf-env | findstr /C:"tensorflow" /C:"numpy" /C:"pandas" /C:"matplotlib"
) else (
    echo ❌ 环境激活失败
    echo 尝试手动激活...
    call "%CONDA_PATH%\Scripts\activate.bat" tf-env
)

echo.
echo ========================================
echo 环境准备完成！您现在可以运行Python代码了
echo ========================================
echo.

REM 保持窗口打开
cmd /k

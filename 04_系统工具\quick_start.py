#!/usr/bin/env python3
import os
import sys
import subprocess

def quick_start():
    print("🚀 快速启动Python环境...")
    
    # 检查是否在conda环境中
    if 'CONDA_DEFAULT_ENV' in os.environ:
        env_name = os.environ['CONDA_DEFAULT_ENV']
        print(f"✅ 当前环境: {env_name}")
    else:
        print("⚠️ 未检测到conda环境")
    
    # 检查关键包
    try:
        import tensorflow as tf
        import numpy as np
        import pandas as pd
        import matplotlib.pyplot as plt
        
        print("✅ 所有关键包已加载")
        print(f"TensorFlow版本: {tf.__version__}")
        print(f"NumPy版本: {np.__version__}")
        print(f"Pandas版本: {pd.__version__}")
        
        return True
    except ImportError as e:
        print(f"❌ 包导入失败: {e}")
        return False

if __name__ == "__main__":
    quick_start()

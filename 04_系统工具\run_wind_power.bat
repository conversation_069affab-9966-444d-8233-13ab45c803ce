@echo off
REM Simple Wind Power Prediction Launcher
REM English only, no special characters

echo Wind Power Prediction Launcher
echo ===============================

REM Set paths
set ANACONDA_PATH=D:\anaconda
set PROJECT_PATH=%~dp0..\01_Python项目\python作业

REM Check Anaconda
if not exist "%ANACONDA_PATH%" (
    echo ERROR: Anaconda not found at %ANACONDA_PATH%
    pause
    exit /b 1
)

REM Check project
if not exist "%PROJECT_PATH%" (
    echo ERROR: Project not found at %PROJECT_PATH%
    pause
    exit /b 1
)

echo OK: Anaconda found at %ANACONDA_PATH%
echo OK: Project found at %PROJECT_PATH%

REM Setup conda
set PATH=%ANACONDA_PATH%\Scripts;%ANACONDA_PATH%\condabin;%PATH%
call "%ANACONDA_PATH%\Scripts\activate.bat" "%ANACONDA_PATH%"

REM Activate tf-env
echo INFO: Activating tf-env environment...
call conda activate tf-env

REM Check activation
if "%CONDA_DEFAULT_ENV%"=="tf-env" (
    echo SUCCESS: tf-env environment activated
) else (
    echo ERROR: Failed to activate tf-env
    pause
    exit /b 1
)

REM Go to project directory
cd /d "%PROJECT_PATH%"
echo INFO: Changed to project directory

REM Show available files
echo.
echo Available Python files:
echo 1. 风电功率预测模型_MLP.py (Main Program)
echo 2. 风电功率预测模型_TCN.py (TCN Version)
echo.

REM Ask user choice
set /p choice="Select program (1 or 2, default=1): "

if "%choice%"=="2" (
    set PYTHON_FILE=风电功率预测模型_TCN.py
) else (
    set PYTHON_FILE=风电功率预测模型_MLP.py
)

echo.
echo LAUNCHING: %PYTHON_FILE%
echo ===============================
echo.

REM Run the program
python "%PYTHON_FILE%"

echo.
echo Program finished. Press any key to exit.
pause >nul

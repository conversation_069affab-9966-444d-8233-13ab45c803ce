@echo off
REM Simple Conda Environment Fix
REM No special characters, English only

echo Simple Conda Environment Fix
echo ================================

set ANACONDA_PATH=D:\anaconda

if not exist "%ANACONDA_PATH%" (
    echo ERROR: Cannot find <PERSON><PERSON><PERSON> at %ANACONDA_PATH%
    pause
    exit /b 1
)

echo OK: Found Anacon<PERSON> at %ANACONDA_PATH%

REM Add conda to PATH for this session
set PATH=%ANACONDA_PATH%\Scripts;%ANACONDA_PATH%\condabin;%PATH%

echo INFO: Testing conda command...
conda --version
if %errorLevel% == 0 (
    echo OK: Conda command works
) else (
    echo ERROR: Conda command failed
    call "%ANACONDA_PATH%\Scripts\activate.bat" "%ANACONDA_PATH%"
)

echo INFO: Available environments:
conda env list

echo INFO: Activating tf-env...
call conda activate tf-env

if "%CONDA_DEFAULT_ENV%"=="tf-env" (
    echo SUCCESS: tf-env activated
    echo Current environment: %CONDA_DEFAULT_ENV%
    where python
) else (
    echo ERROR: Failed to activate tf-env
)

echo.
echo Window will stay open for your use
cmd /k

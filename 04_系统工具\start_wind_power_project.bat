@echo off
chcp 65001 >nul
REM ========================================
REM Wind Power Prediction Project Quick Launcher
REM Auto-activate tf-env environment and run project
REM ========================================

title Wind Power Prediction System Launcher

echo.
echo Wind Power Prediction System Launcher
echo ========================================
echo.

REM Set project path
set PROJECT_PATH=%~dp0..\01_Python项目\python作业
set ANACONDA_PATH=D:\anaconda

REM Check if project path exists
if not exist "%PROJECT_PATH%" (
    echo [ERROR] Cannot find project directory
    echo Path: %PROJECT_PATH%
    pause
    exit /b 1
)

echo [OK] Project path: %PROJECT_PATH%

REM Check if Anaconda exists
if not exist "%ANACONDA_PATH%" (
    echo [ERROR] Cannot find Anaconda installation directory
    echo Path: %ANACONDA_PATH%
    echo.
    echo [TIP] Please run fix_conda_path.bat first to fix environment
    pause
    exit /b 1
)

echo [OK] Anaconda path: %ANACONDA_PATH%
echo.

REM Switch to project directory
cd /d "%PROJECT_PATH%"
echo [INFO] Switched to project directory: %cd%

REM Initialize conda environment
echo [INFO] Initializing conda environment...
call "%ANACONDA_PATH%\Scripts\activate.bat" "%ANACONDA_PATH%"

REM Activate tf-env environment
echo [INFO] Activating tf-env environment...
call conda activate tf-env

REM Check if environment is activated successfully
if "%CONDA_DEFAULT_ENV%"=="tf-env" (
    echo [OK] tf-env environment activated successfully!
    echo.
    echo [SUCCESS] Environment configured, ready to start project...
    echo.

    REM Show available Python files
    echo [INFO] Available project files:
    echo 1. 风电功率预测模型_MLP.py (Main Program)
    echo 2. 风电功率预测模型_TCN.py (TCN Version)
    echo 3. 风电功率预测模型_MLP_实验报告自动生成.py (Report Generator)
    echo.

    REM Ask user which file to run
    set /p choice="Please select program to run (1-3) or press Enter for main program: "

    if "%choice%"=="1" (
        set PYTHON_FILE=风电功率预测模型_MLP.py
    ) else if "%choice%"=="2" (
        set PYTHON_FILE=风电功率预测模型_TCN.py
    ) else if "%choice%"=="3" (
        set PYTHON_FILE=风电功率预测模型_MLP_实验报告自动生成.py
    ) else (
        set PYTHON_FILE=风电功率预测模型_MLP.py
    )

    echo.
    echo [LAUNCH] Starting: %PYTHON_FILE%
    echo ========================================
    echo.

    REM Run Python program
    python "%PYTHON_FILE%"

    echo.
    echo [INFO] Program execution completed

) else (
    echo [ERROR] tf-env environment activation failed
    echo [TIP] Please check if environment exists: conda env list
    echo [TIP] Or run fix_conda_path.bat to fix environment configuration
)

echo.
echo Press any key to exit...
pause >nul

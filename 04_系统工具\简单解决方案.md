# 🔧 Anaconda环境问题简单解决方案

## 问题现象
每次开机后需要先启动Anaconda Navigator才能使用tf-env虚拟环境，否则运行代码没反应。

## 🎯 最简单的解决方法

### 方法一：手动添加环境变量（推荐）

1. **打开系统环境变量设置**
   - 按 `Win + R`，输入 `sysdm.cpl`，回车
   - 点击"环境变量"按钮
   - 在"系统变量"中找到"Path"，点击"编辑"

2. **添加Anaconda路径**
   添加以下路径（根据你的实际安装路径调整）：
   ```
   D:\anaconda
   D:\anaconda\Scripts
   D:\anaconda\condabin
   D:\anaconda\Library\bin
   ```

3. **重启计算机**
   重启后就可以直接使用conda命令了

### 方法二：使用批处理脚本

创建一个批处理文件 `activate_env.bat`：
```batch
@echo off
set PATH=D:\anaconda\Scripts;D:\anaconda\condabin;%PATH%
call conda activate tf-env
cmd /k
```

双击运行即可激活环境。

### 方法三：PowerShell脚本

使用我们提供的 `ActivateCondaEnv.ps1`：
1. 右键点击文件
2. 选择"使用PowerShell运行"

## 🚀 快速启动风电预测项目

创建桌面快捷方式，目标设置为：
```
cmd /k "cd /d D:\.cursor\01_Python项目\python作业 && D:\anaconda\Scripts\activate.bat D:\anaconda && conda activate tf-env && python 风电功率预测模型_MLP.py"
```

## 🔍 验证环境是否正常

打开命令行，依次运行：
```bash
conda --version
conda env list
conda activate tf-env
python --version
where python
```

如果都能正常显示结果，说明环境配置成功。

## 💡 常见问题

**Q: 提示"conda不是内部或外部命令"**
A: 说明环境变量没有配置好，请按方法一重新配置

**Q: 环境激活失败**
A: 检查tf-env环境是否存在：`conda env list`

**Q: Python代码运行没反应**
A: 确保使用的是tf-env环境中的Python：`where python`

## 🎯 推荐流程

1. 先尝试方法一（永久解决）
2. 如果不想修改系统设置，使用方法二或三
3. 创建桌面快捷方式方便日常使用

---
*这个方案避免了复杂的脚本和编码问题，直接解决根本原因*

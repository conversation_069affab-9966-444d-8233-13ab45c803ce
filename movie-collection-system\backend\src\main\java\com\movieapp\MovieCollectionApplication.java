package com.movieapp;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.data.jpa.repository.config.EnableJpaAuditing;

/**
 * 电影收藏管理系统主启动类
 */
@SpringBootApplication
@EnableJpaAuditing
public class MovieCollectionApplication {

    public static void main(String[] args) {
        SpringApplication.run(MovieCollectionApplication.class, args);
        System.out.println("========================================");
        System.out.println("电影收藏管理系统后端启动成功！");
        System.out.println("API地址: http://localhost:8081/api");
        System.out.println("========================================");
    }
}

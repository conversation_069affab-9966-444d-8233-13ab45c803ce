package com.movieapp.controller;

import com.movieapp.entity.Collection;
import com.movieapp.entity.Movie;
import com.movieapp.service.CollectionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 收藏控制器
 */
@RestController
@RequestMapping("/collections")
@CrossOrigin(origins = "*")
public class CollectionController {

    @Autowired
    private CollectionService collectionService;

    /**
     * 添加收藏
     */
    @PostMapping
    public ResponseEntity<Map<String, Object>> addToCollection(@RequestBody Map<String, Object> request) {
        Map<String, Object> response = new HashMap<>();
        
        try {
            Long userId = Long.valueOf(request.get("userId").toString());
            Long movieId = Long.valueOf(request.get("movieId").toString());

            Collection collection = collectionService.addToCollection(userId, movieId);
            
            response.put("success", true);
            response.put("message", "收藏成功");
            response.put("collection", Map.of(
                "id", collection.getId(),
                "userId", collection.getUser().getId(),
                "movieId", collection.getMovie().getId(),
                "createdAt", collection.getCreatedAt()
            ));
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }

    /**
     * 取消收藏
     */
    @DeleteMapping("/{userId}/{movieId}")
    public ResponseEntity<Map<String, Object>> removeFromCollection(
            @PathVariable Long userId, 
            @PathVariable Long movieId) {
        Map<String, Object> response = new HashMap<>();
        
        try {
            collectionService.removeFromCollection(userId, movieId);
            
            response.put("success", true);
            response.put("message", "取消收藏成功");
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }

    /**
     * 检查是否已收藏
     */
    @GetMapping("/check/{userId}/{movieId}")
    public ResponseEntity<Map<String, Object>> checkCollection(
            @PathVariable Long userId, 
            @PathVariable Long movieId) {
        Map<String, Object> response = new HashMap<>();
        
        try {
            boolean isCollected = collectionService.isCollected(userId, movieId);
            
            response.put("success", true);
            response.put("isCollected", isCollected);
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }

    /**
     * 获取用户收藏列表
     */
    @GetMapping("/user/{userId}")
    public ResponseEntity<Map<String, Object>> getUserCollections(
            @PathVariable Long userId,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size,
            @RequestParam(defaultValue = "createdAt") String sortBy,
            @RequestParam(defaultValue = "desc") String sortDir) {
        Map<String, Object> response = new HashMap<>();
        
        try {
            Sort.Direction direction = sortDir.equalsIgnoreCase("desc") ? 
                Sort.Direction.DESC : Sort.Direction.ASC;
            Pageable pageable = PageRequest.of(page, size, Sort.by(direction, sortBy));
            
            Page<Collection> collectionsPage = collectionService.getUserCollections(userId, pageable);
            List<Movie> movies = collectionsPage.getContent().stream()
                .map(Collection::getMovie)
                .toList();
            
            response.put("success", true);
            response.put("movies", movies);
            response.put("currentPage", collectionsPage.getNumber());
            response.put("totalPages", collectionsPage.getTotalPages());
            response.put("totalElements", collectionsPage.getTotalElements());
            response.put("size", collectionsPage.getSize());
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }

    /**
     * 获取收藏统计
     */
    @GetMapping("/stats/{userId}")
    public ResponseEntity<Map<String, Object>> getCollectionStats(@PathVariable Long userId) {
        Map<String, Object> response = new HashMap<>();
        
        try {
            long totalCollections = collectionService.getUserCollectionCount(userId);
            
            response.put("success", true);
            response.put("totalCollections", totalCollections);
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }

    /**
     * 获取热门收藏电影
     */
    @GetMapping("/popular")
    public ResponseEntity<Map<String, Object>> getPopularCollections(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {
        Map<String, Object> response = new HashMap<>();

        try {
            Pageable pageable = PageRequest.of(page, size);
            Page<Movie> moviesPage = collectionService.getPopularCollectedMovies(pageable);

            response.put("success", true);
            response.put("movies", moviesPage.getContent());
            response.put("currentPage", moviesPage.getNumber());
            response.put("totalPages", moviesPage.getTotalPages());
            response.put("totalElements", moviesPage.getTotalElements());
            response.put("size", moviesPage.getSize());

            return ResponseEntity.ok(response);
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }

    /**
     * 根据类型获取用户收藏
     */
    @GetMapping("/user/{userId}/genre")
    public ResponseEntity<Map<String, Object>> getUserCollectionsByGenre(
            @PathVariable Long userId,
            @RequestParam String genre,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size) {
        Map<String, Object> response = new HashMap<>();

        try {
            Pageable pageable = PageRequest.of(page, size);
            Page<Collection> collectionsPage = collectionService.getCollectionsByGenre(userId, genre, pageable);
            List<Movie> movies = collectionsPage.getContent().stream()
                .map(Collection::getMovie)
                .toList();

            response.put("success", true);
            response.put("movies", movies);
            response.put("currentPage", collectionsPage.getNumber());
            response.put("totalPages", collectionsPage.getTotalPages());
            response.put("totalElements", collectionsPage.getTotalElements());
            response.put("size", collectionsPage.getSize());

            return ResponseEntity.ok(response);
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }
}

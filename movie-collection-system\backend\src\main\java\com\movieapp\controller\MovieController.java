package com.movieapp.controller;

import com.movieapp.entity.Movie;
import com.movieapp.repository.MovieRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

/**
 * 电影控制器
 */
@RestController
@RequestMapping("/movies")
@CrossOrigin(origins = "*")
public class MovieController {

    @Autowired
    private MovieRepository movieRepository;

    /**
     * 获取电影列表（分页）
     */
    @GetMapping
    public ResponseEntity<Map<String, Object>> getMovies(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size,
            @RequestParam(defaultValue = "averageRating") String sortBy,
            @RequestParam(defaultValue = "desc") String sortDir,
            @RequestParam(required = false) String search) {
        
        Map<String, Object> response = new HashMap<>();
        
        try {
            Sort sort = sortDir.equalsIgnoreCase("desc") ? 
                Sort.by(sortBy).descending() : Sort.by(sortBy).ascending();
            
            Pageable pageable = PageRequest.of(page, size, sort);
            Page<Movie> moviePage;
            
            if (search != null && !search.trim().isEmpty()) {
                moviePage = movieRepository.searchMovies(search.trim(), pageable);
            } else {
                moviePage = movieRepository.findAll(pageable);
            }
            
            response.put("success", true);
            response.put("movies", moviePage.getContent());
            response.put("currentPage", moviePage.getNumber());
            response.put("totalPages", moviePage.getTotalPages());
            response.put("totalElements", moviePage.getTotalElements());
            response.put("size", moviePage.getSize());
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "获取电影列表失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }

    /**
     * 根据ID获取电影详情
     */
    @GetMapping("/{id}")
    public ResponseEntity<Map<String, Object>> getMovieById(@PathVariable Long id) {
        Map<String, Object> response = new HashMap<>();
        
        try {
            Optional<Movie> movieOpt = movieRepository.findById(id);
            
            if (movieOpt.isPresent()) {
                response.put("success", true);
                response.put("movie", movieOpt.get());
            } else {
                response.put("success", false);
                response.put("message", "电影不存在");
            }
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "获取电影详情失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }

    /**
     * 获取热门电影
     */
    @GetMapping("/popular")
    public ResponseEntity<Map<String, Object>> getPopularMovies(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {
        
        Map<String, Object> response = new HashMap<>();
        
        try {
            Pageable pageable = PageRequest.of(page, size);
            Page<Movie> moviePage = movieRepository.findPopularMovies(pageable);
            
            response.put("success", true);
            response.put("movies", moviePage.getContent());
            response.put("currentPage", moviePage.getNumber());
            response.put("totalPages", moviePage.getTotalPages());
            response.put("totalElements", moviePage.getTotalElements());
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "获取热门电影失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }

    /**
     * 获取高评分电影
     */
    @GetMapping("/top-rated")
    public ResponseEntity<Map<String, Object>> getTopRatedMovies(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(defaultValue = "8.0") double minRating,
            @RequestParam(defaultValue = "1000") int minRatingCount) {
        
        Map<String, Object> response = new HashMap<>();
        
        try {
            Pageable pageable = PageRequest.of(page, size);
            Page<Movie> moviePage = movieRepository.findHighRatedMovies(minRating, minRatingCount, pageable);
            
            response.put("success", true);
            response.put("movies", moviePage.getContent());
            response.put("currentPage", moviePage.getNumber());
            response.put("totalPages", moviePage.getTotalPages());
            response.put("totalElements", moviePage.getTotalElements());
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "获取高评分电影失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }

    /**
     * 获取最新电影
     */
    @GetMapping("/latest")
    public ResponseEntity<Map<String, Object>> getLatestMovies(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {
        
        Map<String, Object> response = new HashMap<>();
        
        try {
            Pageable pageable = PageRequest.of(page, size);
            Page<Movie> moviePage = movieRepository.findLatestMovies(pageable);
            
            response.put("success", true);
            response.put("movies", moviePage.getContent());
            response.put("currentPage", moviePage.getNumber());
            response.put("totalPages", moviePage.getTotalPages());
            response.put("totalElements", moviePage.getTotalElements());
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "获取最新电影失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }

    /**
     * 搜索电影
     */
    @GetMapping("/search")
    public ResponseEntity<Map<String, Object>> searchMovies(
            @RequestParam String keyword,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size) {
        
        Map<String, Object> response = new HashMap<>();
        
        try {
            if (keyword == null || keyword.trim().isEmpty()) {
                response.put("success", false);
                response.put("message", "搜索关键词不能为空");
                return ResponseEntity.badRequest().body(response);
            }
            
            Pageable pageable = PageRequest.of(page, size);
            Page<Movie> moviePage = movieRepository.searchMovies(keyword.trim(), pageable);
            
            response.put("success", true);
            response.put("movies", moviePage.getContent());
            response.put("keyword", keyword);
            response.put("currentPage", moviePage.getNumber());
            response.put("totalPages", moviePage.getTotalPages());
            response.put("totalElements", moviePage.getTotalElements());
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "搜索电影失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }
}

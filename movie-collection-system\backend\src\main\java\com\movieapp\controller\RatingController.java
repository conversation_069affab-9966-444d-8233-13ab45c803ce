package com.movieapp.controller;

import com.movieapp.entity.Rating;
import com.movieapp.service.RatingService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 评分控制器
 */
@RestController
@RequestMapping("/ratings")
@CrossOrigin(origins = "*")
public class RatingController {

    @Autowired
    private RatingService ratingService;

    /**
     * 添加或更新评分
     */
    @PostMapping
    public ResponseEntity<Map<String, Object>> addOrUpdateRating(@RequestBody Map<String, Object> request) {
        Map<String, Object> response = new HashMap<>();
        
        try {
            Long userId = Long.valueOf(request.get("userId").toString());
            Long movieId = Long.valueOf(request.get("movieId").toString());
            Double score = Double.valueOf(request.get("score").toString());
            String comment = (String) request.get("comment");

            Rating rating = ratingService.addOrUpdateRating(userId, movieId, score, comment);
            
            response.put("success", true);
            response.put("message", "评分成功");
            response.put("rating", Map.of(
                "id", rating.getId(),
                "userId", rating.getUser().getId(),
                "movieId", rating.getMovie().getId(),
                "score", rating.getScore(),
                "comment", rating.getComment() != null ? rating.getComment() : "",
                "createdAt", rating.getCreatedAt(),
                "updatedAt", rating.getUpdatedAt()
            ));
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }

    /**
     * 删除评分
     */
    @DeleteMapping("/{userId}/{movieId}")
    public ResponseEntity<Map<String, Object>> deleteRating(
            @PathVariable Long userId, 
            @PathVariable Long movieId) {
        Map<String, Object> response = new HashMap<>();
        
        try {
            ratingService.deleteRating(userId, movieId);
            
            response.put("success", true);
            response.put("message", "删除评分成功");
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }

    /**
     * 获取用户对电影的评分
     */
    @GetMapping("/{userId}/{movieId}")
    public ResponseEntity<Map<String, Object>> getUserMovieRating(
            @PathVariable Long userId, 
            @PathVariable Long movieId) {
        Map<String, Object> response = new HashMap<>();
        
        try {
            Rating rating = ratingService.getUserMovieRating(userId, movieId);
            
            response.put("success", true);
            if (rating != null) {
                response.put("rating", Map.of(
                    "id", rating.getId(),
                    "score", rating.getScore(),
                    "comment", rating.getComment() != null ? rating.getComment() : "",
                    "createdAt", rating.getCreatedAt(),
                    "updatedAt", rating.getUpdatedAt()
                ));
            } else {
                response.put("rating", null);
            }
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }

    /**
     * 获取用户评分列表
     */
    @GetMapping("/user/{userId}")
    public ResponseEntity<Map<String, Object>> getUserRatings(
            @PathVariable Long userId,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size,
            @RequestParam(defaultValue = "updatedAt") String sortBy,
            @RequestParam(defaultValue = "desc") String sortDir) {
        Map<String, Object> response = new HashMap<>();
        
        try {
            Sort.Direction direction = sortDir.equalsIgnoreCase("desc") ? 
                Sort.Direction.DESC : Sort.Direction.ASC;
            Pageable pageable = PageRequest.of(page, size, Sort.by(direction, sortBy));
            
            Page<Rating> ratingsPage = ratingService.getUserRatings(userId, pageable);
            
            response.put("success", true);
            response.put("ratings", ratingsPage.getContent().stream().map(rating -> Map.of(
                "id", rating.getId(),
                "score", rating.getScore(),
                "comment", rating.getComment() != null ? rating.getComment() : "",
                "createdAt", rating.getCreatedAt(),
                "updatedAt", rating.getUpdatedAt(),
                "movie", Map.of(
                    "id", rating.getMovie().getId(),
                    "title", rating.getMovie().getTitle(),
                    "posterPath", rating.getMovie().getPosterPath() != null ? rating.getMovie().getPosterPath() : "",
                    "releaseDate", rating.getMovie().getReleaseDate() != null ? rating.getMovie().getReleaseDate() : "",
                    "averageRating", rating.getMovie().getAverageRating()
                )
            )).toList());
            response.put("currentPage", ratingsPage.getNumber());
            response.put("totalPages", ratingsPage.getTotalPages());
            response.put("totalElements", ratingsPage.getTotalElements());
            response.put("size", ratingsPage.getSize());
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }

    /**
     * 获取电影评分列表
     */
    @GetMapping("/movie/{movieId}")
    public ResponseEntity<Map<String, Object>> getMovieRatings(
            @PathVariable Long movieId,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size,
            @RequestParam(defaultValue = "updatedAt") String sortBy,
            @RequestParam(defaultValue = "desc") String sortDir) {
        Map<String, Object> response = new HashMap<>();
        
        try {
            Sort.Direction direction = sortDir.equalsIgnoreCase("desc") ? 
                Sort.Direction.DESC : Sort.Direction.ASC;
            Pageable pageable = PageRequest.of(page, size, Sort.by(direction, sortBy));
            
            Page<Rating> ratingsPage = ratingService.getMovieRatings(movieId, pageable);
            
            response.put("success", true);
            response.put("ratings", ratingsPage.getContent().stream().map(rating -> Map.of(
                "id", rating.getId(),
                "score", rating.getScore(),
                "comment", rating.getComment() != null ? rating.getComment() : "",
                "createdAt", rating.getCreatedAt(),
                "updatedAt", rating.getUpdatedAt(),
                "user", Map.of(
                    "id", rating.getUser().getId(),
                    "username", rating.getUser().getUsername(),
                    "nickname", rating.getUser().getNickname() != null ? rating.getUser().getNickname() : rating.getUser().getUsername(),
                    "avatar", rating.getUser().getAvatar() != null ? rating.getUser().getAvatar() : ""
                )
            )).toList());
            response.put("currentPage", ratingsPage.getNumber());
            response.put("totalPages", ratingsPage.getTotalPages());
            response.put("totalElements", ratingsPage.getTotalElements());
            response.put("size", ratingsPage.getSize());
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }

    /**
     * 获取评分统计
     */
    @GetMapping("/stats/{userId}")
    public ResponseEntity<Map<String, Object>> getRatingStats(@PathVariable Long userId) {
        Map<String, Object> response = new HashMap<>();
        
        try {
            long totalRatings = ratingService.getUserRatingCount(userId);
            Double averageScore = ratingService.getUserAverageScore(userId);
            
            response.put("success", true);
            response.put("totalRatings", totalRatings);
            response.put("averageScore", averageScore != null ? averageScore : 0.0);
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }

    /**
     * 获取电影评分统计
     */
    @GetMapping("/movie/{movieId}/stats")
    public ResponseEntity<Map<String, Object>> getMovieRatingStats(@PathVariable Long movieId) {
        Map<String, Object> response = new HashMap<>();

        try {
            long totalRatings = ratingService.getMovieRatingCount(movieId);
            Double averageScore = ratingService.getMovieAverageScore(movieId);
            Map<Integer, Long> scoreDistribution = ratingService.getMovieScoreDistribution(movieId);

            response.put("success", true);
            response.put("totalRatings", totalRatings);
            response.put("averageScore", averageScore != null ? averageScore : 0.0);
            response.put("scoreDistribution", scoreDistribution);

            return ResponseEntity.ok(response);
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }

    /**
     * 获取推荐电影
     */
    @GetMapping("/recommendations/{userId}")
    public ResponseEntity<Map<String, Object>> getRecommendedMovies(
            @PathVariable Long userId,
            @RequestParam(defaultValue = "10") int limit) {
        Map<String, Object> response = new HashMap<>();

        try {
            List<com.movieapp.entity.Movie> recommendedMovies = ratingService.getRecommendedMovies(userId, limit);

            response.put("success", true);
            response.put("movies", recommendedMovies);
            response.put("count", recommendedMovies.size());

            return ResponseEntity.ok(response);
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }
}

package com.movieapp.controller;

import com.movieapp.entity.Movie;
import com.movieapp.entity.User;
import com.movieapp.repository.MovieRepository;
import com.movieapp.repository.UserRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.sql.DataSource;
import java.sql.Connection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 测试控制器 - 用于测试数据库连接和基本功能
 */
@RestController
@RequestMapping("/test")
@CrossOrigin(origins = "*")
public class TestController {

    @Autowired
    private DataSource dataSource;

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private MovieRepository movieRepository;

    /**
     * 测试数据库连接
     */
    @GetMapping("/db-connection")
    public ResponseEntity<Map<String, Object>> testDatabaseConnection() {
        Map<String, Object> response = new HashMap<>();
        
        try {
            Connection connection = dataSource.getConnection();
            String databaseName = connection.getCatalog();
            String url = connection.getMetaData().getURL();
            
            response.put("success", true);
            response.put("message", "数据库连接成功");
            response.put("database", databaseName);
            response.put("url", url);
            
            connection.close();
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "数据库连接失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }

    /**
     * 测试用户表
     */
    @GetMapping("/users")
    public ResponseEntity<Map<String, Object>> testUsers() {
        Map<String, Object> response = new HashMap<>();
        
        try {
            List<User> users = userRepository.findAll();
            long userCount = userRepository.count();
            
            response.put("success", true);
            response.put("message", "用户表查询成功");
            response.put("count", userCount);
            response.put("users", users);
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "用户表查询失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }

    /**
     * 测试电影表
     */
    @GetMapping("/movies")
    public ResponseEntity<Map<String, Object>> testMovies() {
        Map<String, Object> response = new HashMap<>();
        
        try {
            List<Movie> movies = movieRepository.findAll();
            long movieCount = movieRepository.count();
            
            response.put("success", true);
            response.put("message", "电影表查询成功");
            response.put("count", movieCount);
            response.put("movies", movies);
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "电影表查询失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }

    /**
     * 系统状态检查
     */
    @GetMapping("/status")
    public ResponseEntity<Map<String, Object>> systemStatus() {
        Map<String, Object> response = new HashMap<>();
        Map<String, Object> status = new HashMap<>();
        
        try {
            // 检查数据库连接
            Connection connection = dataSource.getConnection();
            status.put("database", "connected");
            status.put("databaseName", connection.getCatalog());
            connection.close();
            
            // 检查表数据
            status.put("userCount", userRepository.count());
            status.put("movieCount", movieRepository.count());
            
            response.put("success", true);
            response.put("message", "系统运行正常");
            response.put("status", status);
            response.put("timestamp", System.currentTimeMillis());
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "系统检查失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }

    /**
     * 健康检查
     */
    @GetMapping("/health")
    public ResponseEntity<Map<String, String>> health() {
        Map<String, String> response = new HashMap<>();
        response.put("status", "UP");
        response.put("message", "电影收藏管理系统运行正常");
        return ResponseEntity.ok(response);
    }

    /**
     * 更新电影海报
     */
    @PostMapping("/update-posters")
    public ResponseEntity<Map<String, Object>> updateMoviePosters() {
        Map<String, Object> response = new HashMap<>();
        try {
            int updatedCount = 0;

            // 更新电影海报 - 使用稳定的占位图片
            updatedCount += updatePosterForMovie("肖申克的救赎", "https://via.placeholder.com/300x450/1e3a8a/ffffff?text=肖申克的救赎");
            updatedCount += updatePosterForMovie("霸王别姬", "https://via.placeholder.com/300x450/dc2626/ffffff?text=霸王别姬");
            updatedCount += updatePosterForMovie("阿甘正传", "https://via.placeholder.com/300x450/059669/ffffff?text=阿甘正传");
            updatedCount += updatePosterForMovie("泰坦尼克号", "https://via.placeholder.com/300x450/0284c7/ffffff?text=泰坦尼克号");
            updatedCount += updatePosterForMovie("这个杀手不太冷", "https://via.placeholder.com/300x450/7c3aed/ffffff?text=这个杀手不太冷");
            updatedCount += updatePosterForMovie("辛德勒的名单", "https://via.placeholder.com/300x450/ea580c/ffffff?text=辛德勒的名单");
            updatedCount += updatePosterForMovie("美丽人生", "https://via.placeholder.com/300x450/16a34a/ffffff?text=美丽人生");
            updatedCount += updatePosterForMovie("千与千寻", "https://via.placeholder.com/300x450/db2777/ffffff?text=千与千寻");
            updatedCount += updatePosterForMovie("黑暗骑士", "https://via.placeholder.com/300x450/374151/ffffff?text=黑暗骑士");
            updatedCount += updatePosterForMovie("教父", "https://via.placeholder.com/300x450/92400e/ffffff?text=教父");

            response.put("success", true);
            response.put("message", "电影海报更新成功");
            response.put("updatedCount", updatedCount);
            response.put("timestamp", System.currentTimeMillis());
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "更新海报失败: " + e.getMessage());
            response.put("timestamp", System.currentTimeMillis());
            return ResponseEntity.badRequest().body(response);
        }
    }

    private int updatePosterForMovie(String title, String posterPath) {
        try {
            List<Movie> movies = movieRepository.findAll();
            for (Movie movie : movies) {
                if (movie.getTitle().equals(title)) {
                    movie.setPosterPath(posterPath);
                    movieRepository.save(movie);
                    return 1;
                }
            }
            return 0;
        } catch (Exception e) {
            System.err.println("更新电影 " + title + " 的海报失败: " + e.getMessage());
            return 0;
        }
    }
}

package com.movieapp.entity;

import jakarta.persistence.*;

/**
 * 收藏实体类
 */
@Entity
@Table(name = "collections")
public class Collection extends BaseEntity {

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "user_id", nullable = false)
    private User user;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "movie_id", nullable = false)
    private Movie movie;

    @Column(name = "notes", columnDefinition = "TEXT")
    private String notes;

    // 构造函数
    public Collection() {}

    public Collection(User user, Movie movie) {
        this.user = user;
        this.movie = movie;
    }

    public Collection(User user, Movie movie, String notes) {
        this.user = user;
        this.movie = movie;
        this.notes = notes;
    }

    // Getters and Setters
    public User getUser() {
        return user;
    }

    public void setUser(User user) {
        this.user = user;
    }

    public Movie getMovie() {
        return movie;
    }

    public void setMovie(Movie movie) {
        this.movie = movie;
    }

    public String getNotes() {
        return notes;
    }

    public void setNotes(String notes) {
        this.notes = notes;
    }
}

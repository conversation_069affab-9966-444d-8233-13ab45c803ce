package com.movieapp.entity;

import jakarta.persistence.*;
import jakarta.validation.constraints.DecimalMax;
import jakarta.validation.constraints.DecimalMin;
import jakarta.validation.constraints.NotNull;

/**
 * 评分实体类
 */
@Entity
@Table(name = "ratings")
public class Rating extends BaseEntity {

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "user_id", nullable = false)
    private User user;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "movie_id", nullable = false)
    private Movie movie;

    @NotNull(message = "评分不能为空")
    @DecimalMin(value = "0.0", message = "评分不能小于0.0")
    @DecimalMax(value = "10.0", message = "评分不能大于10.0")
    @Column(name = "score", nullable = false)
    private Double score;

    @Column(name = "comment", columnDefinition = "TEXT")
    private String comment;

    // 构造函数
    public Rating() {}

    public Rating(User user, Movie movie, Double score) {
        this.user = user;
        this.movie = movie;
        this.score = score;
    }

    public Rating(User user, Movie movie, Double score, String comment) {
        this.user = user;
        this.movie = movie;
        this.score = score;
        this.comment = comment;
    }

    // Getters and Setters
    public User getUser() {
        return user;
    }

    public void setUser(User user) {
        this.user = user;
    }

    public Movie getMovie() {
        return movie;
    }

    public void setMovie(Movie movie) {
        this.movie = movie;
    }

    public Double getScore() {
        return score;
    }

    public void setScore(Double score) {
        this.score = score;
    }

    public String getComment() {
        return comment;
    }

    public void setComment(String comment) {
        this.comment = comment;
    }
}

package com.movieapp.repository;

import com.movieapp.entity.Collection;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 收藏数据访问接口
 */
@Repository
public interface CollectionRepository extends JpaRepository<Collection, Long> {

    /**
     * 查找用户的收藏
     */
    Page<Collection> findByUserId(Long userId, Pageable pageable);

    /**
     * 查找电影的收藏
     */
    Page<Collection> findByMovieId(Long movieId, Pageable pageable);

    /**
     * 查找用户对特定电影的收藏
     */
    Optional<Collection> findByUserIdAndMovieId(Long userId, Long movieId);

    /**
     * 检查用户是否收藏了某电影
     */
    boolean existsByUserIdAndMovieId(Long userId, Long movieId);

    /**
     * 统计用户收藏数量
     */
    long countByUserId(Long userId);

    /**
     * 统计电影被收藏数量
     */
    long countByMovieId(Long movieId);

    /**
     * 查找用户在指定时间段内的收藏
     */
    @Query("SELECT c FROM Collection c WHERE c.user.id = :userId AND c.createdAt BETWEEN :startDate AND :endDate ORDER BY c.createdAt DESC")
    List<Collection> findByUserIdAndCreatedAtBetween(@Param("userId") Long userId, 
                                                    @Param("startDate") LocalDateTime startDate, 
                                                    @Param("endDate") LocalDateTime endDate);

    /**
     * 查找最近收藏的电影
     */
    @Query("SELECT c FROM Collection c WHERE c.user.id = :userId ORDER BY c.createdAt DESC")
    Page<Collection> findRecentCollections(@Param("userId") Long userId, Pageable pageable);

    /**
     * 根据电影类型统计用户收藏
     */
    @Query("SELECT mg.genre.name, COUNT(c) FROM Collection c " +
           "JOIN c.movie.movieGenres mg " +
           "WHERE c.user.id = :userId " +
           "GROUP BY mg.genre.id, mg.genre.name " +
           "ORDER BY COUNT(c) DESC")
    List<Object[]> countCollectionsByGenre(@Param("userId") Long userId);

    /**
     * 查找收藏了相同电影的其他用户
     */
    @Query("SELECT DISTINCT c.user FROM Collection c WHERE c.movie.id IN (" +
           "SELECT c2.movie.id FROM Collection c2 WHERE c2.user.id = :userId" +
           ") AND c.user.id != :userId")
    List<com.movieapp.entity.User> findUsersWithSimilarTaste(@Param("userId") Long userId);

    /**
     * 按创建时间倒序查找用户收藏
     */
    Page<Collection> findByUserIdOrderByCreatedAtDesc(Long userId, Pageable pageable);

    /**
     * 搜索用户收藏的电影
     */
    @Query("SELECT c FROM Collection c WHERE c.user.id = :userId AND c.movie.title LIKE %:keyword%")
    Page<Collection> findByUserIdAndMovieTitleContainingIgnoreCase(@Param("userId") Long userId,
                                                                  @Param("keyword") String keyword,
                                                                  Pageable pageable);

    /**
     * 获取热门收藏电影
     */
    @Query("SELECT c.movie FROM Collection c GROUP BY c.movie ORDER BY COUNT(c) DESC")
    Page<com.movieapp.entity.Movie> findPopularCollectedMovies(Pageable pageable);

    /**
     * 根据类型筛选用户收藏
     */
    @Query("SELECT c FROM Collection c WHERE c.user.id = :userId AND c.movie.genres LIKE %:genre% ORDER BY c.createdAt DESC")
    Page<Collection> findByUserIdAndMovieGenresContaining(@Param("userId") Long userId,
                                                         @Param("genre") String genre,
                                                         Pageable pageable);
}

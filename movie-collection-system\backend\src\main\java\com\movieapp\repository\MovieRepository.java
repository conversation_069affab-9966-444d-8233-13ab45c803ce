package com.movieapp.repository;

import com.movieapp.entity.Movie;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.Optional;

/**
 * 电影数据访问接口
 */
@Repository
public interface MovieRepository extends JpaRepository<Movie, Long> {

    /**
     * 根据标题模糊查询电影
     */
    @Query("SELECT m FROM Movie m WHERE m.title LIKE %:title% OR m.originalTitle LIKE %:title%")
    Page<Movie> findByTitleContaining(@Param("title") String title, Pageable pageable);

    /**
     * 根据导演查询电影
     */
    Page<Movie> findByDirectorContaining(String director, Pageable pageable);

    /**
     * 根据上映年份查询电影
     */
    @Query("SELECT m FROM Movie m WHERE YEAR(m.releaseDate) = :year")
    Page<Movie> findByReleaseYear(@Param("year") int year, Pageable pageable);

    /**
     * 根据上映日期范围查询电影
     */
    Page<Movie> findByReleaseDateBetween(LocalDate startDate, LocalDate endDate, Pageable pageable);

    /**
     * 根据TMDB ID查找电影
     */
    Optional<Movie> findByTmdbId(Integer tmdbId);

    /**
     * 根据IMDB ID查找电影
     */
    Optional<Movie> findByImdbId(String imdbId);

    /**
     * 查询热门电影（按收藏数排序）
     */
    @Query("SELECT m FROM Movie m ORDER BY m.collectionCount DESC, m.averageRating DESC")
    Page<Movie> findPopularMovies(Pageable pageable);

    /**
     * 查询高评分电影
     */
    @Query("SELECT m FROM Movie m WHERE m.averageRating >= :minRating AND m.ratingCount >= :minRatingCount ORDER BY m.averageRating DESC")
    Page<Movie> findHighRatedMovies(@Param("minRating") double minRating, @Param("minRatingCount") int minRatingCount, Pageable pageable);

    /**
     * 查询最新电影
     */
    @Query("SELECT m FROM Movie m ORDER BY m.releaseDate DESC")
    Page<Movie> findLatestMovies(Pageable pageable);

    /**
     * 根据类型查询电影
     */
    @Query("SELECT DISTINCT m FROM Movie m JOIN m.movieGenres mg WHERE mg.genre.id = :genreId")
    Page<Movie> findByGenreId(@Param("genreId") Integer genreId, Pageable pageable);

    /**
     * 搜索电影（标题、导演、演员）
     */
    @Query("SELECT m FROM Movie m WHERE " +
           "m.title LIKE %:keyword% OR " +
           "m.originalTitle LIKE %:keyword% OR " +
           "m.director LIKE %:keyword% OR " +
           "m.cast LIKE %:keyword%")
    Page<Movie> searchMovies(@Param("keyword") String keyword, Pageable pageable);

    /**
     * 获取推荐电影（基于用户收藏的类型）
     */
    @Query("SELECT DISTINCT m FROM Movie m " +
           "JOIN m.movieGenres mg " +
           "WHERE mg.genre.id IN (" +
           "  SELECT DISTINCT mg2.genre.id FROM Collection c " +
           "  JOIN c.movie.movieGenres mg2 " +
           "  WHERE c.user.id = :userId" +
           ") " +
           "AND m.id NOT IN (" +
           "  SELECT c2.movie.id FROM Collection c2 WHERE c2.user.id = :userId" +
           ") " +
           "ORDER BY m.averageRating DESC, m.collectionCount DESC")
    Page<Movie> findRecommendedMovies(@Param("userId") Long userId, Pageable pageable);
}

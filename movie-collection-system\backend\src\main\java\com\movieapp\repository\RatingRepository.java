package com.movieapp.repository;

import com.movieapp.entity.Rating;
import com.movieapp.entity.Movie;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 评分数据访问接口
 */
@Repository
public interface RatingRepository extends JpaRepository<Rating, Long> {

    /**
     * 查找用户对特定电影的评分
     */
    Optional<Rating> findByUserIdAndMovieId(Long userId, Long movieId);

    /**
     * 检查用户是否已评分某电影
     */
    boolean existsByUserIdAndMovieId(Long userId, Long movieId);

    /**
     * 查找用户的所有评分
     */
    Page<Rating> findByUserId(Long userId, Pageable pageable);

    /**
     * 查找用户的所有评分（按更新时间倒序）
     */
    Page<Rating> findByUserIdOrderByUpdatedAtDesc(Long userId, Pageable pageable);

    /**
     * 查找电影的所有评分
     */
    Page<Rating> findByMovieId(Long movieId, Pageable pageable);

    /**
     * 查找电影的所有评分（按更新时间倒序）
     */
    Page<Rating> findByMovieIdOrderByUpdatedAtDesc(Long movieId, Pageable pageable);

    /**
     * 统计用户评分数量
     */
    long countByUserId(Long userId);

    /**
     * 统计电影评分数量
     */
    long countByMovieId(Long movieId);

    /**
     * 计算用户平均评分
     */
    @Query("SELECT AVG(r.score) FROM Rating r WHERE r.user.id = :userId")
    Double findAverageScoreByUserId(@Param("userId") Long userId);

    /**
     * 计算电影平均评分
     */
    @Query("SELECT AVG(r.score) FROM Rating r WHERE r.movie.id = :movieId")
    Double findAverageScoreByMovieId(@Param("movieId") Long movieId);

    /**
     * 获取电影评分分布
     */
    @Query("SELECT CAST(r.score AS int), COUNT(r) FROM Rating r WHERE r.movie.id = :movieId GROUP BY CAST(r.score AS int) ORDER BY CAST(r.score AS int)")
    List<Object[]> findScoreDistributionByMovieId(@Param("movieId") Long movieId);

    /**
     * 查找用户在指定时间段内的评分
     */
    @Query("SELECT r FROM Rating r WHERE r.user.id = :userId AND r.createdAt BETWEEN :startDate AND :endDate ORDER BY r.createdAt DESC")
    List<Rating> findByUserIdAndCreatedAtBetween(@Param("userId") Long userId, 
                                                @Param("startDate") LocalDateTime startDate, 
                                                @Param("endDate") LocalDateTime endDate);

    /**
     * 查找用户高分评分的电影
     */
    Page<Rating> findByUserIdAndScoreGreaterThanEqualOrderByScoreDesc(Long userId, Double minScore, Pageable pageable);

    /**
     * 搜索用户评分的电影
     */
    @Query("SELECT r FROM Rating r WHERE r.user.id = :userId AND r.movie.title LIKE %:keyword%")
    Page<Rating> findByUserIdAndMovieTitleContainingIgnoreCase(@Param("userId") Long userId, 
                                                              @Param("keyword") String keyword, 
                                                              Pageable pageable);

    /**
     * 批量获取用户对指定电影的评分
     */
    List<Rating> findByUserIdAndMovieIdIn(Long userId, List<Long> movieIds);

    /**
     * 查找评分相似的用户
     */
    @Query("SELECT r.user.id, COUNT(r) as commonRatings FROM Rating r1 " +
           "JOIN Rating r ON r1.movie.id = r.movie.id " +
           "WHERE r1.user.id = :userId AND r.user.id != :userId " +
           "AND ABS(r1.score - r.score) <= :scoreDifference " +
           "GROUP BY r.user.id " +
           "HAVING COUNT(r) >= :minCommonRatings " +
           "ORDER BY COUNT(r) DESC")
    List<Object[]> findSimilarUsers(@Param("userId") Long userId, 
                                   @Param("scoreDifference") Double scoreDifference, 
                                   @Param("minCommonRatings") Long minCommonRatings);

    /**
     * 获取最新评分
     */
    @Query("SELECT r FROM Rating r ORDER BY r.createdAt DESC")
    Page<Rating> findLatestRatings(Pageable pageable);

    /**
     * 获取热门评分（评分数量多的电影）
     */
    @Query("SELECT r.movie, COUNT(r) as ratingCount FROM Rating r " +
           "GROUP BY r.movie " +
           "ORDER BY COUNT(r) DESC")
    Page<Object[]> findMostRatedMovies(Pageable pageable);

    /**
     * 获取高分电影
     */
    @Query("SELECT r.movie, AVG(r.score) as avgScore FROM Rating r " +
           "GROUP BY r.movie " +
           "HAVING COUNT(r) >= :minRatingCount " +
           "ORDER BY AVG(r.score) DESC")
    Page<Object[]> findHighestRatedMovies(@Param("minRatingCount") Long minRatingCount, Pageable pageable);

    /**
     * 根据类型统计用户评分
     */
    @Query("SELECT mg.genre.name, AVG(r.score), COUNT(r) FROM Rating r " +
           "JOIN r.movie.movieGenres mg " +
           "WHERE r.user.id = :userId " +
           "GROUP BY mg.genre.id, mg.genre.name " +
           "ORDER BY AVG(r.score) DESC")
    List<Object[]> findUserRatingsByGenre(@Param("userId") Long userId);

    /**
     * 获取用户评分趋势（按月统计）
     */
    @Query("SELECT YEAR(r.createdAt), MONTH(r.createdAt), AVG(r.score), COUNT(r) FROM Rating r " +
           "WHERE r.user.id = :userId " +
           "GROUP BY YEAR(r.createdAt), MONTH(r.createdAt) " +
           "ORDER BY YEAR(r.createdAt) DESC, MONTH(r.createdAt) DESC")
    List<Object[]> findUserRatingTrends(@Param("userId") Long userId);

    /**
     * 获取相似用户高分评价的电影（推荐算法用）
     */
    @Query("SELECT DISTINCT r.movie FROM Rating r " +
           "WHERE r.user.id IN :similarUserIds " +
           "AND r.user.id != :userId " +
           "AND r.score >= :minScore " +
           "AND r.movie.id NOT IN (" +
           "    SELECT r2.movie.id FROM Rating r2 WHERE r2.user.id = :userId" +
           ") " +
           "ORDER BY r.score DESC")
    List<Movie> findHighRatedMoviesBySimilarUsers(@Param("userId") Long userId,
                                                 @Param("similarUserIds") List<Long> similarUserIds,
                                                 @Param("minScore") Double minScore,
                                                 Pageable pageable);
}

package com.movieapp.service;

import com.movieapp.entity.Collection;
import com.movieapp.entity.Movie;
import com.movieapp.entity.User;
import com.movieapp.repository.CollectionRepository;
import com.movieapp.repository.MovieRepository;
import com.movieapp.repository.UserRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.Optional;

/**
 * 收藏服务类
 */
@Service
@Transactional
public class CollectionService {

    @Autowired
    private CollectionRepository collectionRepository;

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private MovieRepository movieRepository;

    /**
     * 添加收藏
     */
    public Collection addToCollection(Long userId, Long movieId) {
        // 检查用户是否存在
        User user = userRepository.findById(userId)
            .orElseThrow(() -> new RuntimeException("用户不存在"));

        // 检查电影是否存在
        Movie movie = movieRepository.findById(movieId)
            .orElseThrow(() -> new RuntimeException("电影不存在"));

        // 检查是否已经收藏
        Optional<Collection> existingCollection = collectionRepository
            .findByUserIdAndMovieId(userId, movieId);
        
        if (existingCollection.isPresent()) {
            throw new RuntimeException("已经收藏过该电影");
        }

        // 创建收藏记录
        Collection collection = new Collection();
        collection.setUser(user);
        collection.setMovie(movie);
        collection.setCreatedAt(LocalDateTime.now());

        Collection savedCollection = collectionRepository.save(collection);

        // 更新电影收藏数
        updateMovieCollectionCount(movieId);

        return savedCollection;
    }

    /**
     * 取消收藏
     */
    public void removeFromCollection(Long userId, Long movieId) {
        Optional<Collection> collection = collectionRepository
            .findByUserIdAndMovieId(userId, movieId);
        
        if (collection.isEmpty()) {
            throw new RuntimeException("未收藏该电影");
        }

        collectionRepository.delete(collection.get());

        // 更新电影收藏数
        updateMovieCollectionCount(movieId);
    }

    /**
     * 检查是否已收藏
     */
    public boolean isCollected(Long userId, Long movieId) {
        return collectionRepository.findByUserIdAndMovieId(userId, movieId).isPresent();
    }

    /**
     * 获取用户收藏列表
     */
    public Page<Collection> getUserCollections(Long userId, Pageable pageable) {
        return collectionRepository.findByUserIdOrderByCreatedAtDesc(userId, pageable);
    }

    /**
     * 获取用户收藏数量
     */
    public long getUserCollectionCount(Long userId) {
        return collectionRepository.countByUserId(userId);
    }

    /**
     * 获取热门收藏电影
     */
    public Page<Movie> getPopularCollectedMovies(Pageable pageable) {
        return collectionRepository.findPopularCollectedMovies(pageable);
    }

    /**
     * 更新电影收藏数
     */
    private void updateMovieCollectionCount(Long movieId) {
        long collectionCount = collectionRepository.countByMovieId(movieId);
        
        Movie movie = movieRepository.findById(movieId).orElse(null);
        if (movie != null) {
            movie.setCollectionCount((int) collectionCount);
            movieRepository.save(movie);
        }
    }

    /**
     * 批量检查收藏状态
     */
    public boolean[] checkMultipleCollections(Long userId, Long[] movieIds) {
        boolean[] results = new boolean[movieIds.length];
        
        for (int i = 0; i < movieIds.length; i++) {
            results[i] = isCollected(userId, movieIds[i]);
        }
        
        return results;
    }

    /**
     * 获取最近收藏的电影
     */
    public Page<Collection> getRecentCollections(Long userId, Pageable pageable) {
        return collectionRepository.findByUserIdOrderByCreatedAtDesc(userId, pageable);
    }

    /**
     * 根据类型获取收藏电影
     */
    public Page<Collection> getCollectionsByGenre(Long userId, String genre, Pageable pageable) {
        if (genre == null || genre.trim().isEmpty()) {
            return collectionRepository.findByUserIdOrderByCreatedAtDesc(userId, pageable);
        }
        return collectionRepository.findByUserIdAndMovieGenresContaining(userId, genre.trim(), pageable);
    }

    /**
     * 搜索用户收藏
     */
    public Page<Collection> searchUserCollections(Long userId, String keyword, Pageable pageable) {
        return collectionRepository.findByUserIdAndMovieTitleContainingIgnoreCase(
            userId, keyword, pageable);
    }
}

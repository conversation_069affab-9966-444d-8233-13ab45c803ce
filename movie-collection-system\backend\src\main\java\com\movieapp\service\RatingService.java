package com.movieapp.service;

import com.movieapp.entity.Movie;
import com.movieapp.entity.Rating;
import com.movieapp.entity.User;
import com.movieapp.repository.MovieRepository;
import com.movieapp.repository.RatingRepository;
import com.movieapp.repository.UserRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;

/**
 * 评分服务类
 */
@Service
@Transactional
public class RatingService {

    @Autowired
    private RatingRepository ratingRepository;

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private MovieRepository movieRepository;

    /**
     * 添加或更新评分
     */
    public Rating addOrUpdateRating(Long userId, Long movieId, Double score, String comment) {
        // 验证评分范围
        if (score < 0.0 || score > 10.0) {
            throw new RuntimeException("评分必须在0-10之间");
        }

        // 检查用户是否存在
        User user = userRepository.findById(userId)
            .orElseThrow(() -> new RuntimeException("用户不存在"));

        // 检查电影是否存在
        Movie movie = movieRepository.findById(movieId)
            .orElseThrow(() -> new RuntimeException("电影不存在"));

        // 查找现有评分
        Optional<Rating> existingRating = ratingRepository.findByUserIdAndMovieId(userId, movieId);

        Rating rating;
        if (existingRating.isPresent()) {
            // 更新现有评分
            rating = existingRating.get();
            rating.setScore(score);
            rating.setComment(comment);
            rating.setUpdatedAt(LocalDateTime.now());
        } else {
            // 创建新评分
            rating = new Rating();
            rating.setUser(user);
            rating.setMovie(movie);
            rating.setScore(score);
            rating.setComment(comment);
            rating.setCreatedAt(LocalDateTime.now());
            rating.setUpdatedAt(LocalDateTime.now());
        }

        Rating savedRating = ratingRepository.save(rating);

        // 更新电影平均评分
        updateMovieAverageRating(movieId);

        return savedRating;
    }

    /**
     * 删除评分
     */
    public void deleteRating(Long userId, Long movieId) {
        Optional<Rating> rating = ratingRepository.findByUserIdAndMovieId(userId, movieId);
        
        if (rating.isEmpty()) {
            throw new RuntimeException("评分不存在");
        }

        ratingRepository.delete(rating.get());

        // 更新电影平均评分
        updateMovieAverageRating(movieId);
    }

    /**
     * 获取用户对电影的评分
     */
    public Rating getUserMovieRating(Long userId, Long movieId) {
        return ratingRepository.findByUserIdAndMovieId(userId, movieId).orElse(null);
    }

    /**
     * 获取用户评分列表
     */
    public Page<Rating> getUserRatings(Long userId, Pageable pageable) {
        return ratingRepository.findByUserIdOrderByUpdatedAtDesc(userId, pageable);
    }

    /**
     * 获取电影评分列表
     */
    public Page<Rating> getMovieRatings(Long movieId, Pageable pageable) {
        return ratingRepository.findByMovieIdOrderByUpdatedAtDesc(movieId, pageable);
    }

    /**
     * 获取用户评分数量
     */
    public long getUserRatingCount(Long userId) {
        return ratingRepository.countByUserId(userId);
    }

    /**
     * 获取电影评分数量
     */
    public long getMovieRatingCount(Long movieId) {
        return ratingRepository.countByMovieId(movieId);
    }

    /**
     * 获取用户平均评分
     */
    public Double getUserAverageScore(Long userId) {
        return ratingRepository.findAverageScoreByUserId(userId);
    }

    /**
     * 获取电影平均评分
     */
    public Double getMovieAverageScore(Long movieId) {
        return ratingRepository.findAverageScoreByMovieId(movieId);
    }

    /**
     * 获取电影评分分布
     */
    public Map<Integer, Long> getMovieScoreDistribution(Long movieId) {
        List<Object[]> distribution = ratingRepository.findScoreDistributionByMovieId(movieId);
        Map<Integer, Long> result = new HashMap<>();
        
        // 初始化所有评分等级
        for (int i = 1; i <= 10; i++) {
            result.put(i, 0L);
        }
        
        // 填充实际数据
        for (Object[] row : distribution) {
            Integer score = ((Number) row[0]).intValue();
            Long count = ((Number) row[1]).longValue();
            result.put(score, count);
        }
        
        return result;
    }

    /**
     * 更新电影平均评分
     */
    private void updateMovieAverageRating(Long movieId) {
        Double averageRating = getMovieAverageScore(movieId);
        long ratingCount = getMovieRatingCount(movieId);
        
        Movie movie = movieRepository.findById(movieId).orElse(null);
        if (movie != null) {
            movie.setAverageRating(averageRating != null ? BigDecimal.valueOf(averageRating) : BigDecimal.ZERO);
            movie.setRatingCount((int) ratingCount);
            movieRepository.save(movie);
        }
    }

    /**
     * 获取用户最近评分
     */
    public Page<Rating> getRecentRatings(Long userId, Pageable pageable) {
        return ratingRepository.findByUserIdOrderByUpdatedAtDesc(userId, pageable);
    }

    /**
     * 获取高分电影
     */
    public Page<Rating> getHighRatedMovies(Long userId, Double minScore, Pageable pageable) {
        return ratingRepository.findByUserIdAndScoreGreaterThanEqualOrderByScoreDesc(userId, minScore, pageable);
    }

    /**
     * 搜索用户评分
     */
    public Page<Rating> searchUserRatings(Long userId, String keyword, Pageable pageable) {
        return ratingRepository.findByUserIdAndMovieTitleContainingIgnoreCase(userId, keyword, pageable);
    }

    /**
     * 批量获取用户对多部电影的评分
     */
    public Map<Long, Rating> getUserRatingsForMovies(Long userId, List<Long> movieIds) {
        List<Rating> ratings = ratingRepository.findByUserIdAndMovieIdIn(userId, movieIds);
        Map<Long, Rating> result = new HashMap<>();
        
        for (Rating rating : ratings) {
            result.put(rating.getMovie().getId(), rating);
        }
        
        return result;
    }

    /**
     * 获取推荐评分（基于相似用户）
     */
    public List<Movie> getRecommendedMovies(Long userId, int limit) {
        try {
            // 1. 找到评分相似的用户（评分差异在1分以内，至少有3部共同评分的电影）
            List<Object[]> similarUsers = ratingRepository.findSimilarUsers(userId, 1.0, 3L);

            if (similarUsers.isEmpty()) {
                // 如果没有相似用户，返回高分电影推荐
                return getHighRatedMoviesRecommendation(limit);
            }

            // 2. 获取相似用户高分评价的电影（评分>=8分）
            List<Long> similarUserIds = similarUsers.stream()
                .map(row -> ((Number) row[0]).longValue())
                .limit(5) // 最多考虑5个相似用户
                .toList();

            List<Movie> recommendedMovies = ratingRepository.findHighRatedMoviesBySimilarUsers(
                userId, similarUserIds, 8.0, PageRequest.of(0, limit));

            // 3. 如果推荐的电影不够，补充一些高分电影
            if (recommendedMovies.size() < limit) {
                List<Movie> additionalMovies = getHighRatedMoviesRecommendation(limit - recommendedMovies.size());

                // 去重并合并
                Set<Long> existingIds = recommendedMovies.stream()
                    .map(Movie::getId)
                    .collect(java.util.stream.Collectors.toSet());

                additionalMovies.stream()
                    .filter(movie -> !existingIds.contains(movie.getId()))
                    .forEach(recommendedMovies::add);
            }

            return recommendedMovies.stream().limit(limit).toList();

        } catch (Exception e) {
            // 如果推荐算法出错，返回高分电影作为备选
            return getHighRatedMoviesRecommendation(limit);
        }
    }

    /**
     * 获取高分电影推荐（备选方案）
     */
    private List<Movie> getHighRatedMoviesRecommendation(int limit) {
        try {
            // 获取平均评分>=8.5且评分人数>=100的电影
            Page<Object[]> highRatedMoviesPage = ratingRepository.findHighestRatedMovies(100L,
                PageRequest.of(0, limit));

            return highRatedMoviesPage.getContent().stream()
                .map(row -> (Movie) row[0])
                .toList();
        } catch (Exception e) {
            return List.of();
        }
    }
}

server:
  port: 8081
  servlet:
    context-path: /api

spring:
  application:
    name: movie-collection-backend
  
  # 数据库配置
  datasource:
    url: *******************************************************************************************************************************************************************************
    username: ${DB_USERNAME:root}
    password: ${DB_PASSWORD:Hu060729}
    driver-class-name: com.mysql.cj.jdbc.Driver
    
  # JPA配置
  jpa:
    hibernate:
      ddl-auto: update
    show-sql: true
    properties:
      hibernate:
        dialect: org.hibernate.dialect.MySQLDialect
        format_sql: true
        jdbc:
          time_zone: Asia/Shanghai
    
  # 文件上传配置
  servlet:
    multipart:
      max-file-size: 10MB
      max-request-size: 10MB

# JWT配置
jwt:
  secret: ${JWT_SECRET:movieCollectionSecretKey2024}
  expiration: ${JWT_EXPIRATION:86400000}

# 应用配置
app:
  upload-path: ${UPLOAD_PATH:./uploads}
  
# 外部API配置
tmdb:
  api-key: ${TMDB_API_KEY:}
  base-url: ${TMDB_BASE_URL:https://api.themoviedb.org/3}

# 日志配置
logging:
  level:
    com.movieapp: DEBUG
    org.springframework.security: DEBUG
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: logs/movie-collection.log

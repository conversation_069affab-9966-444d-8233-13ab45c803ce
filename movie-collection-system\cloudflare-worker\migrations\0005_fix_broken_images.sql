-- 修复失效的电影图片链接
-- 使用备用的高质量图片源

-- 修复中国电影图片
UPDATE movies SET 
    poster_path = 'https://img1.doubanio.com/view/photo/s_ratio_poster/public/p2561716440.jpg',
    backdrop_path = 'https://img1.doubanio.com/view/photo/l/public/p2561716440.jpg'
WHERE title = '无间道';

UPDATE movies SET 
    poster_path = 'https://img1.doubanio.com/view/photo/s_ratio_poster/public/p1910813120.jpg',
    backdrop_path = 'https://img1.doubanio.com/view/photo/l/public/p1910813120.jpg'
WHERE title = '活着';

UPDATE movies SET 
    poster_path = 'https://img1.doubanio.com/view/photo/s_ratio_poster/public/p2455050536.jpg',
    backdrop_path = 'https://img1.doubanio.com/view/photo/l/public/p2455050536.jpg'
WHERE title = '大话西游之大圣娶亲';

-- 修复欧洲电影图片
UPDATE movies SET 
    poster_path = 'https://img1.doubanio.com/view/photo/s_ratio_poster/public/p510861873.jpg',
    backdrop_path = 'https://img1.doubanio.com/view/photo/l/public/p510861873.jpg'
WHERE title = '美丽人生';

UPDATE movies SET 
    poster_path = 'https://img1.doubanio.com/view/photo/s_ratio_poster/public/p511118051.jpg',
    backdrop_path = 'https://img1.doubanio.com/view/photo/l/public/p511118051.jpg'
WHERE title = '这个杀手不太冷';

-- 修复经典好莱坞电影图片
UPDATE movies SET 
    poster_path = 'https://img1.doubanio.com/view/photo/s_ratio_poster/public/p1910813120.jpg',
    backdrop_path = 'https://img1.doubanio.com/view/photo/l/public/p1910813120.jpg'
WHERE title = '飞越疯人院';

UPDATE movies SET 
    poster_path = 'https://img1.doubanio.com/view/photo/s_ratio_poster/public/p1606727862.jpg',
    backdrop_path = 'https://img1.doubanio.com/view/photo/l/public/p1606727862.jpg'
WHERE title = '罗马假日';

-- 使用稳定的CDN图片源修复其他失效图片
UPDATE movies SET 
    backdrop_path = 'https://image.tmdb.org/t/p/w1920_and_h800_multi_faces/iNh3BivHyg5sQRPP1KOkzguEX0H.jpg'
WHERE title = '霸王别姬';

UPDATE movies SET 
    backdrop_path = 'https://image.tmdb.org/t/p/w1920_and_h800_multi_faces/vI3aUGTuRRdM7J78KIdW98LdxE3.jpg'
WHERE title = '辛德勒的名单';

UPDATE movies SET 
    backdrop_path = 'https://image.tmdb.org/t/p/w1920_and_h800_multi_faces/hqkIcbrOHL86UncnHIsHVcVmzue.jpg'
WHERE title = '黑暗骑士';

UPDATE movies SET 
    backdrop_path = 'https://image.tmdb.org/t/p/w1920_and_h800_multi_faces/4cDFJr4HnXN5AdPw4AKrmLlMWdO.jpg'
WHERE title = '低俗小说';

UPDATE movies SET 
    backdrop_path = 'https://image.tmdb.org/t/p/w1920_and_h800_multi_faces/q3jHCb4dMfYF6ojikmp4ohtqwxd.jpg'
WHERE title = '沉默的羔羊';

UPDATE movies SET 
    backdrop_path = 'https://image.tmdb.org/t/p/w1920_and_h800_multi_faces/xVB32s6hJTTBhGn2cCXcRNNnSKN.jpg'
WHERE title = '终结者2：审判日';

UPDATE movies SET 
    backdrop_path = 'https://image.tmdb.org/t/p/w1920_and_h800_multi_faces/4eLsux3Wl1JrIGDlZbLFUhRXpZR.jpg'
WHERE title = '卡萨布兰卡';

UPDATE movies SET 
    backdrop_path = 'https://image.tmdb.org/t/p/w1920_and_h800_multi_faces/TU9NIjwzjoKPwQHoHshkBcQZzr.jpg'
WHERE title = '寄生虫';

UPDATE movies SET 
    backdrop_path = 'https://image.tmdb.org/t/p/w1920_and_h800_multi_faces/wCECFhHiVioWnB5QfJpZNK8l8Xt.jpg'
WHERE title = '2001太空漫游';

-- 为没有有效图片的电影设置高质量占位图片
UPDATE movies SET 
    poster_path = CASE 
        WHEN poster_path IS NULL OR poster_path = '' THEN 'https://via.placeholder.com/500x750/2C3E50/FFFFFF?text=' || REPLACE(title, ' ', '+')
        ELSE poster_path 
    END,
    backdrop_path = CASE 
        WHEN backdrop_path IS NULL OR backdrop_path = '' THEN 'https://via.placeholder.com/1920x1080/34495E/FFFFFF?text=' || REPLACE(title, ' ', '+')
        ELSE backdrop_path 
    END
WHERE poster_path IS NULL OR poster_path = '' OR backdrop_path IS NULL OR backdrop_path = '';

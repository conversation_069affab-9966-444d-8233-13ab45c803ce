-- 简单有效的图片修复方案
-- 使用稳定的占位图片服务，确保所有图片都能正常显示

-- 为所有电影设置统一的高质量占位图片
-- 使用不同的颜色主题来区分不同类型的电影

-- 经典剧情片 - 深蓝色主题
UPDATE movies SET 
    poster_path = 'https://via.placeholder.com/500x750/2C3E50/FFFFFF?text=' || REPLACE(title, ' ', '%20'),
    backdrop_path = 'https://via.placeholder.com/1920x1080/34495E/FFFFFF?text=' || REPLACE(title, ' ', '%20')
WHERE title IN ('教父', '教父2', '肖申克的救赎', '辛德勒的名单', '阿甘正传');

-- 动作片 - 红色主题
UPDATE movies SET 
    poster_path = 'https://via.placeholder.com/500x750/E74C3C/FFFFFF?text=' || REPLACE(title, ' ', '%20'),
    backdrop_path = 'https://via.placeholder.com/1920x1080/C0392B/FFFFFF?text=' || REPLACE(title, ' ', '%20')
WHERE title IN ('黑暗骑士', '终结者2：审判日', '黑客帝国', '星球大战');

-- 中国电影 - 金色主题
UPDATE movies SET 
    poster_path = 'https://via.placeholder.com/500x750/F39C12/FFFFFF?text=' || REPLACE(title, ' ', '%20'),
    backdrop_path = 'https://via.placeholder.com/1920x1080/E67E22/FFFFFF?text=' || REPLACE(title, ' ', '%20')
WHERE title IN ('大话西游之大圣娶亲', '活着', '无间道', '霸王别姬');

-- 日本电影 - 紫色主题
UPDATE movies SET 
    poster_path = 'https://via.placeholder.com/500x750/9B59B6/FFFFFF?text=' || REPLACE(title, ' ', '%20'),
    backdrop_path = 'https://via.placeholder.com/1920x1080/8E44AD/FFFFFF?text=' || REPLACE(title, ' ', '%20')
WHERE title IN ('七武士', '千与千寻');

-- 欧洲电影 - 绿色主题
UPDATE movies SET 
    poster_path = 'https://via.placeholder.com/500x750/27AE60/FFFFFF?text=' || REPLACE(title, ' ', '%20'),
    backdrop_path = 'https://via.placeholder.com/1920x1080/229954/FFFFFF?text=' || REPLACE(title, ' ', '%20')
WHERE title IN ('美丽人生', '这个杀手不太冷');

-- 科幻片 - 青色主题
UPDATE movies SET 
    poster_path = 'https://via.placeholder.com/500x750/1ABC9C/FFFFFF?text=' || REPLACE(title, ' ', '%20'),
    backdrop_path = 'https://via.placeholder.com/1920x1080/16A085/FFFFFF?text=' || REPLACE(title, ' ', '%20')
WHERE title IN ('盗梦空间', '星际穿越', '2001太空漫游');

-- 爱情片 - 粉色主题
UPDATE movies SET 
    poster_path = 'https://via.placeholder.com/500x750/E91E63/FFFFFF?text=' || REPLACE(title, ' ', '%20'),
    backdrop_path = 'https://via.placeholder.com/1920x1080/C2185B/FFFFFF?text=' || REPLACE(title, ' ', '%20')
WHERE title IN ('泰坦尼克号', '卡萨布兰卡', '罗马假日');

-- 犯罪片 - 深灰色主题
UPDATE movies SET 
    poster_path = 'https://via.placeholder.com/500x750/95A5A6/FFFFFF?text=' || REPLACE(title, ' ', '%20'),
    backdrop_path = 'https://via.placeholder.com/1920x1080/7F8C8D/FFFFFF?text=' || REPLACE(title, ' ', '%20')
WHERE title IN ('低俗小说', '好家伙', '搏击俱乐部', '沉默的羔羊');

-- 其他经典电影 - 橙色主题
UPDATE movies SET 
    poster_path = 'https://via.placeholder.com/500x750/FF9800/FFFFFF?text=' || REPLACE(title, ' ', '%20'),
    backdrop_path = 'https://via.placeholder.com/1920x1080/F57C00/FFFFFF?text=' || REPLACE(title, ' ', '%20')
WHERE title IN ('十二怒汉', '指环王：王者归来', '飞越疯人院', '寄生虫', '现代启示录');

-- 确保所有电影都有图片（备用方案）
UPDATE movies SET 
    poster_path = CASE 
        WHEN poster_path IS NULL OR poster_path = '' OR poster_path LIKE '%placeholder.jpg%' 
        THEN 'https://via.placeholder.com/500x750/607D8B/FFFFFF?text=' || REPLACE(title, ' ', '%20')
        ELSE poster_path 
    END,
    backdrop_path = CASE 
        WHEN backdrop_path IS NULL OR backdrop_path = '' OR backdrop_path LIKE '%placeholder.jpg%'
        THEN 'https://via.placeholder.com/1920x1080/546E7A/FFFFFF?text=' || REPLACE(title, ' ', '%20')
        ELSE backdrop_path 
    END
WHERE poster_path IS NULL OR poster_path = '' OR poster_path LIKE '%placeholder.jpg%' 
   OR backdrop_path IS NULL OR backdrop_path = '' OR backdrop_path LIKE '%placeholder.jpg%';

{"clientTcpRtt": 226, "requestHeaderNames": {}, "httpProtocol": "HTTP/1.1", "tlsCipher": "AEAD-AES256-GCM-SHA384", "continent": "AS", "asn": 4134, "clientAcceptEncoding": "br, gzip, deflate", "verifiedBotCategory": "", "country": "CN", "isEUCountry": false, "region": "Sichuan", "tlsClientCiphersSha1": "JZtiTn8H/ntxORk+XXvU2EvNoz8=", "tlsClientAuth": {"certIssuerDNLegacy": "", "certIssuerSKI": "", "certSubjectDNRFC2253": "", "certSubjectDNLegacy": "", "certFingerprintSHA256": "", "certNotBefore": "", "certSKI": "", "certSerial": "", "certIssuerDN": "", "certVerified": "NONE", "certNotAfter": "", "certSubjectDN": "", "certPresented": "0", "certRevoked": "0", "certIssuerSerial": "", "certIssuerDNRFC2253": "", "certFingerprintSHA1": ""}, "tlsClientRandom": "xkXwtpmr9km0hxwwR6bvSYnQnzo0Ne4ecPV1w9xUeGc=", "tlsExportedAuthenticator": {"clientFinished": "d11a513865624d210024cc51fc4bbc884d691ceed51fa53497dbf1d7a07803b3e9aaf013be169e06fd1c45b8741a1ab4", "clientHandshake": "f562dad8b0d8ce8fc4325b656b8127be04e35850d30e3fd04045f33a91a37cee91a2516f2aa7cf5416fef86c6ec2032f", "serverHandshake": "76008468b83616582a131491404c1d867c3b59cfbaca08fa992d233d78bd486484d453f96e7e833f466364c4e364eb51", "serverFinished": "9f2d72fbf65c04c5b8587a1bfa808592224d1c468b684fd462bb5c551b5e6a376b2153917f64182b376a2650ec472f3b"}, "tlsClientHelloLength": "386", "colo": "SJC", "timezone": "Asia/Shanghai", "longitude": "104.06667", "latitude": "30.66667", "edgeRequestKeepAliveStatus": 1, "requestPriority": "", "postalCode": "610000", "city": "Chengdu", "tlsVersion": "TLSv1.3", "regionCode": "SC", "asOrganization": "Chinatelecom IPv6 address for mobile", "tlsClientExtensionsSha1Le": "6e+q3vPm88rSgMTN/h7WTTxQ2wQ=", "tlsClientExtensionsSha1": "Y7DIC8A6G0/aXviZ8ie/xDbJb7g=", "botManagement": {"corporateProxy": false, "verifiedBot": false, "jsDetection": {"passed": false}, "staticResource": false, "detectionIds": {}, "score": 99}}
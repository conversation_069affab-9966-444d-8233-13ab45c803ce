"use strict";

function _toConsumableArray(arr) { if (Array.isArray(arr)) { for (var i = 0, arr2 = Array(arr.length); i < arr.length; i++) arr2[i] = arr[i]; return arr2; } else { return Array.from(arr); } }

const O = Object;

var _require = require('printable-characters');

const first = _require.first,
      strlen = _require.strlen,
      limit = (s, n) => first(s, n - 1) + '…';

const asColumns = (rows, cfg_) => {

    const zip = (arrs, f) => arrs.reduce((a, b) => b.map((b, i) => [].concat(_toConsumableArray(a[i] || []), [b])), []).map(args => f.apply(undefined, _toConsumableArray(args))),


    /*  Convert cell data to string (converting multiline text to singleline) */

    cells = rows.map(r => r.map(c => c.replace(/\n/g, '\\n'))),


    /*  Compute column widths (per row) and max widths (per column)     */

    cellWidths = cells.map(r => r.map(strlen)),
          maxWidths = zip(cellWidths, Math.max),


    /*  Default config     */

    cfg = O.assign({
        delimiter: '  ',
        minColumnWidths: maxWidths.map(x => 0),
        maxTotalWidth: 0 }, cfg_),
          delimiterLength = strlen(cfg.delimiter),


    /*  Project desired column widths, taking maxTotalWidth and minColumnWidths in account.     */

    totalWidth = maxWidths.reduce((a, b) => a + b, 0),
          relativeWidths = maxWidths.map(w => w / totalWidth),
          maxTotalWidth = cfg.maxTotalWidth - delimiterLength * (maxWidths.length - 1),
          excessWidth = Math.max(0, totalWidth - maxTotalWidth),
          computedWidths = zip([cfg.minColumnWidths, maxWidths, relativeWidths], (min, max, relative) => Math.max(min, Math.floor(max - excessWidth * relative))),


    /*  This is how many symbols we should pad or cut (per column).  */

    restCellWidths = cellWidths.map(widths => zip([computedWidths, widths], (a, b) => a - b));

    /*  Perform final composition.   */

    return zip([cells, restCellWidths], (a, b) => zip([a, b], (str, w) => w >= 0 ? cfg.right ? ' '.repeat(w) + str : str + ' '.repeat(w) : limit(str, strlen(str) + w)).join(cfg.delimiter));
};

const asTable = cfg => O.assign(arr => {
    var _ref;

    /*  Print arrays  */

    if (arr[0] && Array.isArray(arr[0])) {
        return asColumns(arr.map(r => r.map((c, i) => c === undefined ? '' : cfg.print(c, i))), cfg).join('\n');
    }

    /*  Print objects   */

    const colNames = [].concat(_toConsumableArray(new Set((_ref = []).concat.apply(_ref, _toConsumableArray(arr.map(O.keys)))))),
          columns = [colNames.map(cfg.title)].concat(_toConsumableArray(arr.map(o => colNames.map(key => o[key] === undefined ? '' : cfg.print(o[key], key))))),
          lines = asColumns(columns, cfg);

    return (cfg.dash ? [lines[0], cfg.dash.repeat(strlen(lines[0]))].concat(_toConsumableArray(lines.slice(1))) : lines).join('\n');
}, cfg, {

    configure: newConfig => asTable(O.assign({}, cfg, newConfig))
});

module.exports = asTable({

    maxTotalWidth: Number.MAX_SAFE_INTEGER,
    print: String,
    title: String,
    dash: '-',
    right: false
});

//# sourceMappingURL=data:application/json;charset=utf-8;base64,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
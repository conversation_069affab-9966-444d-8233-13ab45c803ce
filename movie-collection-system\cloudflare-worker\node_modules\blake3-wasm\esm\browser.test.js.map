{"version": 3, "file": "browser.test.js", "sourceRoot": "", "sources": ["../ts/browser.test.ts"], "names": [], "mappings": ";;;;;;;;;AAAA,OAAO,EAAE,MAAM,EAAE,MAAM,MAAM,CAAC;AAC9B,OAAO,EAAE,SAAS,EAAE,aAAa,EAAE,MAAM,IAAI,CAAC;AAC9C,OAAO,EAAE,YAAY,EAAU,MAAM,MAAM,CAAC;AAE5C,OAAO,EAAE,MAAM,EAAE,MAAM,IAAI,CAAC;AAC5B,OAAO,EAAE,OAAO,EAAE,MAAM,MAAM,CAAC;AAC/B,OAAO,SAAmB,MAAM,WAAW,CAAC;AAC5C,OAAO,OAAO,MAAM,eAAe,CAAC;AACpC,OAAO,OAAO,MAAM,SAAS,CAAC;AAC9B,OAAO,EAAE,OAAO,EAAE,MAAM,EAAE,aAAa,EAAE,MAAM,qBAAqB,CAAC;AAErE,4EAA4E;AAC5E,4EAA4E;AAC5E,mEAAmE;AACnE,QAAQ,CAAC,SAAS,EAAE,GAAG,EAAE;IACvB,MAAM,SAAS,GAAG,mBAAmB,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,EAAE,CAAC;IAE9D,QAAQ,CAAC,SAAS,EAAE,GAAG,EAAE;QACvB,MAAM,OAAO,GAAG,OAAO,CAAC,MAAM,EAAE,EAAE,qBAAqB,CAAC,CAAC;QACzD,IAAI,MAAc,CAAC;QACnB,IAAI,IAAoB,CAAC;QAEzB;;WAEG;QACH,SAAe,YAAY;;gBACzB,IAAI;oBACF,SAAS,CAAC,OAAO,CAAC,CAAC;iBACpB;gBAAC,WAAM;oBACN,2BAA2B;iBAC5B;gBAED,aAAa,CACX,OAAO,CAAC,OAAO,EAAE,cAAc,CAAC,EAChC,0DAA0D,CAC3D,CAAC;gBAEF,MAAM,KAAK,GAAG,MAAM,IAAI,OAAO,CAAgB,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE,CAC1D,OAAO,CACL;oBACE,IAAI,EAAE,YAAY;oBAClB,OAAO,EAAE,YAAY;oBACrB,KAAK,EAAE,OAAO,CAAC,OAAO,EAAE,cAAc,CAAC;oBACvC,MAAM,EAAE;wBACN,IAAI,EAAE,OAAO;wBACb,QAAQ,EAAE,SAAS;qBACpB;oBACD,OAAO,EAAE;wBACP,KAAK,EAAE;4BACL,gBAAgB,EAAE,OAAO,CAAC,SAAS,EAAE,KAAK,EAAE,YAAY,CAAC;yBAC1D;qBACF;iBACF,EACD,CAAC,GAAG,EAAE,KAAK,EAAE,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAC9C,CACF,CAAC;gBAEF,IAAI,KAAK,CAAC,SAAS,EAAE,EAAE;oBACrB,MAAM,KAAK,CAAC,QAAQ,CAAC,aAAa,CAAC,CAAC;iBACrC;gBAED,aAAa,CAAC,OAAO,CAAC,OAAO,EAAE,YAAY,CAAC,EAAE,kCAAkC,CAAC,CAAC;YACpF,CAAC;SAAA;QAED,SAAe,KAAK;;gBAClB,MAAM,GAAG,YAAY,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE,CAAC,OAAO,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE,MAAM,EAAE,OAAO,EAAE,CAAC,CAAC,CAAC;gBAC5E,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,CAAC;YAC1D,CAAC;SAAA;QAED,MAAM,CAAC;;gBACL,MAAM,YAAY,EAAE,CAAC;gBACrB,MAAM,KAAK,EAAE,CAAC;gBAEd,IAAI,CAAC,OAAO,CAAC,EAAE,GAAG,IAAI,CAAC,CAAC;gBAExB,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,CAAC,OAAO,EAAiB,CAAC;gBACjD,MAAM,OAAO,GAAG,MAAM,SAAS,CAAC,MAAM,CAAC;oBACrC,cAAc,EAAE,sBAAsB;oBACtC,IAAI,EAAE,CAAC,cAAc,CAAC;iBACvB,CAAC,CAAC;gBACH,IAAI,GAAG,MAAM,OAAO,CAAC,OAAO,EAAE,CAAC;gBAC/B,MAAM,IAAI,CAAC,IAAI,CAAC,oBAAoB,IAAI,EAAE,CAAC,CAAC;gBAC5C,MAAM,IAAI,CAAC,eAAe,CAAC,iBAAiB,CAAC,CAAC;gBAC9C,MAAM,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC;YACjC,CAAC;SAAA,CAAC,CAAC;QAEH,QAAQ,CAAC;YACP,IAAI,IAAI;gBACN,OAAO,IAAI,CAAC;YACd,CAAC;SACF,CAAC,CAAC;QAEH,KAAK,CAAC,GAAG,EAAE;YACT,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,OAAO,GAAG,KAAK,GAAG;YACxB,MAAM,aAAN,MAAM,uBAAN,MAAM,CAAE,KAAK,GAAG;QAClB,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,gBAAgB,EAAE,GAAG,EAAE;QAC9B,IAAI,MAAc,CAAC;QACnB,IAAI,IAAoB,CAAC;QAEzB,SAAe,KAAK;;gBAClB,MAAM,GAAG,YAAY,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE,CAAC,OAAO,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE,MAAM,EAAE,OAAO,CAAC,SAAS,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC;gBAC7F,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,CAAC;YAC1D,CAAC;SAAA;QAED,MAAM,CAAC;;gBACL,MAAM,KAAK,EAAE,CAAC;gBAEd,IAAI,CAAC,OAAO,CAAC,EAAE,GAAG,IAAI,CAAC,CAAC;gBAExB,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,CAAC,OAAO,EAAiB,CAAC;gBACjD,MAAM,OAAO,GAAG,MAAM,SAAS,CAAC,MAAM,CAAC;oBACrC,cAAc,EAAE,sBAAsB;oBACtC,IAAI,EAAE,CAAC,cAAc,CAAC;iBACvB,CAAC,CAAC;gBACH,IAAI,GAAG,MAAM,OAAO,CAAC,OAAO,EAAE,CAAC;gBAC/B,IAAI,CAAC,EAAE,CAAC,SAAS,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC;gBAChC,IAAI,CAAC,EAAE,CAAC,WAAW,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC;gBAClC,IAAI,CAAC,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC;gBAC9B,MAAM,IAAI,CAAC,IAAI,CAAC,oBAAoB,IAAI,0BAA0B,CAAC,CAAC;gBACpE,MAAM,IAAI,CAAC,eAAe,CAAC,iBAAiB,CAAC,CAAC;gBAC9C,MAAM,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC;YACjC,CAAC;SAAA,CAAC,CAAC;QAEH,QAAQ,CAAC;YACP,IAAI,IAAI;gBACN,OAAO,IAAI,CAAC;YACd,CAAC;SACF,CAAC,CAAC;QAEH,KAAK,CAAC,GAAG,EAAE;YACT,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,OAAO,GAAG,KAAK,GAAG;YACxB,MAAM,CAAC,KAAK,EAAE,CAAC;QACjB,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC;AAEH,SAAS,QAAQ,CAAC,IAAoB;IACpC,EAAE,CAAC,iBAAiB,EAAE,GAAS,EAAE;QAC/B,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,iDAAiD,CAAC,CAAC;QAC3F,MAAM,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;IAC7D,CAAC,CAAA,CAAC,CAAC;IAEH,QAAQ,CAAC,gBAAgB,EAAE,GAAG,EAAE;QAC9B,EAAE,CAAC,qBAAqB,EAAE,GAAS,EAAE;YACnC,MAAM,QAAQ,GAAG,CAAC,GAAG,IAAI,UAAU,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;YACtE,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,IAAI,CAAC,QAAQ,CACrC,+BAA+B,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,qBAAqB,CACvE,CAAC;YACF,MAAM,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;QAC7D,CAAC,CAAA,CAAC,CAAC;QAEH,EAAE,CAAC,iBAAiB,EAAE,GAAS,EAAE;YAC/B,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,iDAAiD,CAAC,CAAC;YAC3F,MAAM,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;QAC7D,CAAC,CAAA,CAAC,CAAC;QAEH,EAAE,CAAC,0BAA0B,EAAE,GAAS,EAAE;YACxC,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,IAAI,CAAC,QAAQ,CACrC,iEAAiE,CAClE,CAAC;YACF,MAAM,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;QAC1E,CAAC,CAAA,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,iBAAiB,EAAE,GAAG,EAAE;QAC/B,MAAM,MAAM,GAAG;YACb,EAAE,QAAQ,EAAE,KAAK,EAAE,QAAQ,EAAE,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE;YAChE,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE;YACtE,EAAE,QAAQ,EAAE,MAAM,EAAE,QAAQ,EAAE,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE;SACnE,CAAC;QAEF,MAAM,CAAC,OAAO,CAAC,CAAC,EAAE,QAAQ,EAAE,QAAQ,EAAE,EAAE,EAAE,CACxC,EAAE,CAAC,QAAQ,EAAE,GAAS,EAAE;YACtB,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,IAAI,CAAC,QAAQ,CACrC,6CAA6C,QAAQ,IAAI,CAC1D,CAAC;YACF,MAAM,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;QACpC,CAAC,CAAA,CAAC,CACH,CAAC;QAEF,EAAE,CAAC,KAAK,EAAE,GAAS,EAAE;YACnB,MAAM,MAAM,GAAG,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,iCAAiC,CAAC,CAG1E,CAAC;YACF,MAAM,MAAM,GAAG,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;YAChC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;gBACtC,MAAM,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,gEAAgE;aACxF;YACD,MAAM,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QAClD,CAAC,CAAA,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,YAAY,EAAE,GAAG,EAAE;QAC1B,EAAE,CAAC,SAAS,EAAE,GAAS,EAAE;YACvB,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC;;UAEpC,CAAC,GAAG,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;iBACnC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,+BAA+B,IAAI,MAAM,CAAC;iBACtD,IAAI,CAAC,IAAI,CAAC;;WAEV,CAAC,CAAC;YAEP,MAAM,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;QAC7D,CAAC,CAAA,CAAC,CAAC;QAEH,EAAE,CAAC,8BAA8B,EAAE,GAAS,EAAE;YAC5C,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC;;sBAExB,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC;;WAE7C,CAAC,CAAC;YAEP,MAAM,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;QAC1E,CAAC,CAAA,CAAC,CAAC;QAEH,EAAE,CAAC,qCAAqC,EAAE,GAAS,EAAE;YACnD,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC;;UAEpC,CAAC,GAAG,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;iBACnC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,+BAA+B,IAAI,MAAM,CAAC;iBACtD,IAAI,CAAC,IAAI,CAAC;;WAEV,CAAC,CAAC;YAEP,MAAM,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;QAC7D,CAAC,CAAA,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,QAAQ,EAAE,GAAG,EAAE;QACtB,EAAE,CAAC,qBAAqB,EAAE,GAAS,EAAE;YACnC,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC;;;;;;;;;WASnC,CAAC,CAAC;YAEP,MAAM,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC;gBAC3B,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC;gBACvB,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC;gBACjC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC;aAClC,CAAC,CAAC;QACL,CAAC,CAAA,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,uBAAuB,EAAE,GAAG,EAAE;QACrC,KAAK,MAAM,EACT,QAAQ,EACR,cAAc,EACd,YAAY,EACZ,aAAa,GACd,IAAI,aAAa,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;YACpC,QAAQ,CAAC,GAAG,QAAQ,EAAE,EAAE,GAAS,EAAE;gBACjC,MAAM,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;gBACrC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,EAAE,CAAC,EAAE,EAAE;oBACjC,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC;iBACpB;gBAED,MAAM,QAAQ,GAAG,mBAAmB,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC;gBAExD,EAAE,CAAC,QAAQ,EAAE,GAAS,EAAE;oBACtB,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC;cACpC,QAAQ;wBACE,YAAY,CAAC,MAAM,GAAG,CAAC;4BACnB,CAAC,CAAC;oBAEpB,MAAM,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC;gBACxC,CAAC,CAAA,CAAC,CAAC;gBAEH,EAAE,CAAC,aAAa,EAAE,GAAS,EAAE;oBAC3B,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC;cACpC,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,OAAO,CAAC;cACrC,QAAQ;wBACE,YAAY,CAAC,MAAM,GAAG,CAAC;4BACnB,CAAC,CAAC;oBAEpB,MAAM,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,cAAc,CAAC,CAAC;gBAC1C,CAAC,CAAA,CAAC,CAAC;gBAEH,EAAE,CAAC,eAAe,EAAE,GAAS,EAAE;oBAC7B,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC;gEACc,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC,IAAI,CACrF,GAAG,CACJ;4BACe,QAAQ;6CACS,YAAY,CAAC,MAAM,GAAG,CAAC;eACrD,CAAC,CAAC;oBAEP,MAAM,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,aAAa,CAAC,CAAC;gBACzC,CAAC,CAAA,CAAC,CAAC;gBAEH,EAAE,CAAC,aAAa,EAAE,GAAS,EAAE;oBAC3B,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC;8BACpB,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC;cACxD,QAAQ;wBACE,YAAY,CAAC,MAAM,GAAG,CAAC;4BACnB,CAAC,CAAC;oBAEpB,MAAM,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,aAAa,CAAC,CAAC;gBACzC,CAAC,CAAA,CAAC,CAAC;YACL,CAAC,CAAA,CAAC,CAAC;SACJ;IACH,CAAC,CAAC,CAAC;AACL,CAAC"}
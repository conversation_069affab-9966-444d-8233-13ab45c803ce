"use strict";exports.Router=({base:e="",routes:r=[],...t}={})=>({__proto__:new Proxy({},{get:(t,o,a,p)=>"handle"==o?a.fetch:(t,...l)=>r.push([o.toUpperCase?.(),RegExp(`^${(p=(e+t).replace(/\/+(\/|$)/g,"$1")).replace(/(\/?\.?):(\w+)\+/g,"($1(?<$2>*))").replace(/(\/?\.?):(\w+)/g,"($1(?<$2>[^$1/]+?))").replace(/\./g,"\\.").replace(/(\/?)\*/g,"($1.*)?")}/*$`),l,p])&&a}),routes:r,...t,async fetch(e,...t){let o,a,p=new URL(e.url),l=e.query={__proto__:null};for(let[e,r]of p.searchParams)l[e]=l[e]?[].concat(l[e],r):r;for(let[l,c,s,u]of r)if((l==e.method||"ALL"==l)&&(a=p.pathname.match(c))){e.params=a.groups||{},e.route=u;for(let r of s)if(null!=(o=await r(e.proxy??e,...t)))return o}}});
//# sourceMappingURL=Router.js.map

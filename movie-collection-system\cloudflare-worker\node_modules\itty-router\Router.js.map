{"version": 3, "file": "Router.js", "sources": ["../src/src/Router.ts"], "sourcesContent": [null], "names": ["base", "routes", "other", "__proto__", "Proxy", "get", "target", "prop", "receiver", "path", "fetch", "route", "handlers", "push", "toUpperCase", "RegExp", "replace", "async", "request", "args", "response", "match", "url", "URL", "query", "k", "v", "searchParams", "concat", "method", "regex", "pathname", "params", "groups", "handler", "proxy"], "mappings": "4BA2EsB,EAIlBA,OAAO,GAAIC,SAAS,MAAOC,GAAyB,CAAE,KAExD,CACEC,UAAW,IAAIC,MAAM,GAAI,CAEvBC,IAAK,CAACC,EAAaC,EAAcC,EAAsBC,IAC7C,UAARF,EAAmBC,EAASE,MAE5B,CAACC,KAAkBC,IACjBX,EAAOY,KACL,CACEN,EAAKO,gBACLC,OAAO,KAAKN,GAAQT,EAAOW,GACxBK,QAAQ,aAAc,OACtBA,QAAQ,oBAAqB,gBAC7BA,QAAQ,kBAAmB,uBAC3BA,QAAQ,MAAO,OACfA,QAAQ,WAAY,iBAEvBJ,EACAH,KAECD,IAEXP,YACGC,EACHe,YAAaC,KAAyBC,GACpC,IAAIC,EAAUC,EAAOC,EAAM,IAAIC,IAAIL,EAAQI,KAAME,EAA6BN,EAAQM,MAAQ,CAAErB,UAAW,MAG3G,IAAK,IAAKsB,EAAGC,KAAMJ,EAAIK,aACrBH,EAAMC,GAAKD,EAAMC,GAAM,GAAgBG,OAAOJ,EAAMC,GAAIC,GAAKA,EAG/D,IAAK,IAAKG,EAAQC,EAAOlB,EAAUH,KAASR,EAC1C,IAAK4B,GAAUX,EAAQW,QAAoB,OAAVA,KAAqBR,EAAQC,EAAIS,SAASV,MAAMS,IAAS,CACxFZ,EAAQc,OAASX,EAAMY,QAAU,CAAA,EACjCf,EAAQP,MAAQF,EAChB,IAAK,IAAIyB,KAAWtB,EAClB,GAAqE,OAAhEQ,QAAiBc,EAAQhB,EAAQiB,OAASjB,KAAYC,IAAgB,OAAOC,CACrF,CACJ"}
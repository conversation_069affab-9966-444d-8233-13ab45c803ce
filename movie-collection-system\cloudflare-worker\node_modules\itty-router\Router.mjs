const e=({base:e="",routes:r=[],...o}={})=>({__proto__:new Proxy({},{get:(o,t,a,p)=>"handle"==t?a.fetch:(o,...l)=>r.push([t.toUpperCase?.(),RegExp(`^${(p=(e+o).replace(/\/+(\/|$)/g,"$1")).replace(/(\/?\.?):(\w+)\+/g,"($1(?<$2>*))").replace(/(\/?\.?):(\w+)/g,"($1(?<$2>[^$1/]+?))").replace(/\./g,"\\.").replace(/(\/?)\*/g,"($1.*)?")}/*$`),l,p])&&a}),routes:r,...o,async fetch(e,...o){let t,a,p=new URL(e.url),l=e.query={__proto__:null};for(let[e,r]of p.searchParams)l[e]=l[e]?[].concat(l[e],r):r;for(let[l,c,s,f]of r)if((l==e.method||"ALL"==l)&&(a=p.pathname.match(c))){e.params=a.groups||{},e.route=f;for(let r of s)if(null!=(t=await r(e.proxy??e,...o)))return t}}});export{e as Router};
//# sourceMappingURL=Router.mjs.map

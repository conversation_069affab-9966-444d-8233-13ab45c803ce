"use strict";exports.createCors=(e={})=>{const{origins:s=["*"],maxAge:o,methods:t=["GET"],headers:n={}}=e;let r;const c="function"==typeof s?s:e=>s.includes(e)||s.includes("*"),l={"content-type":"application/json","Access-Control-Allow-Methods":t.join(", "),...n};o&&(l["Access-Control-Max-Age"]=o);return{corsify:e=>{if(!e)throw new Error("No fetch handler responded and no upstream to proxy to specified.");const{headers:s,status:o,body:t}=e;return[101,301,302,308].includes(o)||s.get("access-control-allow-origin")?e:new Response(t,{status:o,headers:{...Object.fromEntries(s),...l,...r,"content-type":s.get("content-type")}})},preflight:e=>{const s=[...new Set(["OPTIONS",...t])],o=e.headers.get("origin")||"";if(r=c(o)&&{"Access-Control-Allow-Origin":o},"OPTIONS"===e.method){const o={...l,"Access-Control-Allow-Methods":s.join(", "),"Access-Control-Allow-Headers":e.headers.get("Access-Control-Request-Headers"),...r};return new Response(null,{headers:e.headers.get("Origin")&&e.headers.get("Access-Control-Request-Method")&&e.headers.get("Access-Control-Request-Headers")?o:{Allow:s.join(", ")}})}}}};
//# sourceMappingURL=createCors.js.map

const e=(e={})=>{const{origins:o=["*"],maxAge:s,methods:t=["GET"],headers:n={}}=e;let r;const c="function"==typeof o?o:e=>o.includes(e)||o.includes("*"),l={"content-type":"application/json","Access-Control-Allow-Methods":t.join(", "),...n};s&&(l["Access-Control-Max-Age"]=s);return{corsify:e=>{if(!e)throw new Error("No fetch handler responded and no upstream to proxy to specified.");const{headers:o,status:s,body:t}=e;return[101,301,302,308].includes(s)||o.get("access-control-allow-origin")?e:new Response(t,{status:s,headers:{...Object.fromEntries(o),...l,...r,"content-type":o.get("content-type")}})},preflight:e=>{const o=[...new Set(["OPTIONS",...t])],s=e.headers.get("origin")||"";if(r=c(s)&&{"Access-Control-Allow-Origin":s},"OPTIONS"===e.method){const s={...l,"Access-Control-Allow-Methods":o.join(", "),"Access-Control-Allow-Headers":e.headers.get("Access-Control-Request-Headers"),...r};return new Response(null,{headers:e.headers.get("Origin")&&e.headers.get("Access-Control-Request-Method")&&e.headers.get("Access-Control-Request-Headers")?s:{Allow:o.join(", ")}})}}}};export{e as createCors};
//# sourceMappingURL=createCors.mjs.map

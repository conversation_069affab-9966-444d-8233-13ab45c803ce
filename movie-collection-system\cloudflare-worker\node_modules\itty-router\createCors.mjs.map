{"version": 3, "file": "createCors.mjs", "sources": ["../src/src/createCors.ts"], "sourcesContent": [null], "names": ["createCors", "options", "origins", "maxAge", "methods", "headers", "<PERSON><PERSON><PERSON><PERSON>", "isAllowOrigin", "origin", "includes", "rHeaders", "join", "corsify", "response", "Error", "status", "body", "get", "Response", "Object", "fromEntries", "preflight", "r", "useMethods", "Set", "method", "reqHeaders", "Allow"], "mappings": "MAUaA,EAAa,CAACC,EAAuB,MAEhD,MAAMC,QAAEA,EAAU,CAAC,KAAIC,OAAEA,EAAMC,QAAEA,EAAU,CAAC,OAAMC,QAAEA,EAAU,CAAE,GAAKJ,EAErE,IAAIK,EACJ,MAAMC,EAAmC,mBAAZL,EACzBA,EACCM,GAAoBN,EAAQO,SAASD,IAAWN,EAAQO,SAAS,KAGhEC,EAAW,CACf,eAAgB,mBAChB,+BAAgCN,EAAQO,KAAK,SAC1CN,GAIDF,IAAQO,EAAS,0BAA4BP,GA+DjD,MAAO,CAAES,QA5BQC,IACf,IAAKA,EACH,MAAM,IAAIC,MACR,qEAGJ,MAAMT,QAAEA,EAAOU,OAAEA,EAAMC,KAAEA,GAASH,EAGlC,MACE,CAAC,IAAK,IAAK,IAAK,KAAKJ,SAASM,IAC9BV,EAAQY,IAAI,+BAELJ,EAGF,IAAIK,SAASF,EAAM,CACxBD,SACAV,QAAS,IACJc,OAAOC,YAAYf,MACnBK,KACAJ,EACH,eAAgBD,EAAQY,IAAI,kBAE9B,EAIcI,UA5DCC,IAEjB,MAAMC,EAAa,IAAI,IAAIC,IAAI,CAAC,aAAcpB,KACxCI,EAASc,EAAEjB,QAAQY,IAAI,WAAa,GAM1C,GAHAX,EAAcC,EAAcC,IAAW,CAAE,8BAA+BA,GAGvD,YAAbc,EAAEG,OAAsB,CAC1B,MAAMC,EAAa,IACdhB,EACH,+BAAgCa,EAAWZ,KAAK,MAChD,+BAAgCW,EAAEjB,QAAQY,IACxC,qCAECX,GAIL,OAAO,IAAIY,SAAS,KAAM,CACxBb,QACEiB,EAAEjB,QAAQY,IAAI,WACdK,EAAEjB,QAAQY,IAAI,kCACdK,EAAEjB,QAAQY,IAAI,kCACVS,EACA,CAAEC,MAAOJ,EAAWZ,KAAK,QAElC,GAgC0B"}
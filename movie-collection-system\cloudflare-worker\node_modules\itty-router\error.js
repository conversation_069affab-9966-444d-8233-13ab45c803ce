"use strict";const e=((e="text/plain; charset=utf-8",r)=>(t,{headers:s={},...n}={})=>void 0===t||"Response"===t?.constructor.name?t:new Response(r?r(t):t,{headers:{"content-type":e,...s.entries?Object.fromEntries(s):s},...n}))("application/json; charset=utf-8",JSON.stringify),r=e=>({400:"Bad Request",401:"Unauthorized",403:"Forbidden",404:"Not Found",500:"Internal Server Error"}[e]||"Unknown Error");exports.error=(t=500,s)=>{if(t instanceof Error){const{message:e,...n}=t;t=t.status||500,s={error:e||r(t),...n}}return s={status:t,..."object"==typeof s?s:{error:s||r(t)}},e(s,{status:t})};
//# sourceMappingURL=error.js.map

{"version": 3, "file": "error.js", "sources": ["../src/src/createResponse.ts", "../src/src/json.ts", "../src/src/error.ts"], "sourcesContent": [null, null, null], "names": ["json", "format", "transform", "body", "headers", "rest", "undefined", "constructor", "name", "Response", "entries", "Object", "fromEntries", "createResponse", "JSON", "stringify", "getMessage", "code", "a", "b", "Error", "message", "err", "status", "error"], "mappings": "aAQO,MCNMA,EDOX,EACEC,EAAS,4BACTC,IAEF,CAACC,GAAQC,UAAU,CAAA,KAAOC,GAAS,CAAA,SACxBC,IAATH,GAAiD,aAA3BA,GAAMI,YAAYC,KACtCL,EACA,IAAIM,SAASP,EAAYA,EAAUC,GAAQA,EAAM,CACnCC,QAAS,CACP,eAAgBH,KACZG,EAAQM,QAENC,OAAOC,YAAYR,GACnBA,MAGLC,ICvBHQ,CAClB,kCACAC,KAAKC,WCUDC,EAAcC,IAAyB,CAC3C,IAAK,cACL,IAAK,eACL,IAAK,YACL,IAAK,YACL,IAAK,yBACJA,IAAS,+BAEyB,CAACC,EAAI,IAAKC,KAE7C,GAAID,aAAaE,MAAO,CACtB,MAAMC,QAAEA,KAAYC,GAAQJ,EAC5BA,EAAIA,EAAEK,QAAU,IAChBJ,EAAI,CACFK,MAAOH,GAAWL,EAAWE,MAC1BI,EAEN,CAOD,OALAH,EAAI,CACFI,OAAQL,KACS,iBAANC,EAAiBA,EAAI,CAAEK,MAAOL,GAAKH,EAAWE,KAGpDlB,EAAKmB,EAAG,CAAEI,OAAQL,GAAI"}
const e=((e="text/plain; charset=utf-8",r)=>(t,{headers:n={},...o}={})=>void 0===t||"Response"===t?.constructor.name?t:new Response(r?r(t):t,{headers:{"content-type":e,...n.entries?Object.fromEntries(n):n},...o}))("application/json; charset=utf-8",JSON.stringify),r=e=>({400:"Bad Request",401:"Unauthorized",403:"Forbidden",404:"Not Found",500:"Internal Server Error"}[e]||"Unknown Error"),t=(t=500,n)=>{if(t instanceof Error){const{message:e,...o}=t;t=t.status||500,n={error:e||r(t),...o}}return n={status:t,..."object"==typeof n?n:{error:n||r(t)}},e(n,{status:t})};export{t as error};
//# sourceMappingURL=error.mjs.map

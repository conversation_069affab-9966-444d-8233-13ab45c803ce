{"version": 3, "file": "error.mjs", "sources": ["../src/src/createResponse.ts", "../src/src/json.ts", "../src/src/error.ts"], "sourcesContent": [null, null, null], "names": ["json", "format", "transform", "body", "headers", "rest", "undefined", "constructor", "name", "Response", "entries", "Object", "fromEntries", "createResponse", "JSON", "stringify", "getMessage", "code", "error", "a", "b", "Error", "message", "err", "status"], "mappings": "AAQO,MCNMA,EDOX,EACEC,EAAS,4BACTC,IAEF,CAACC,GAAQC,UAAU,CAAA,KAAOC,GAAS,CAAA,SACxBC,IAATH,GAAiD,aAA3BA,GAAMI,YAAYC,KACtCL,EACA,IAAIM,SAASP,EAAYA,EAAUC,GAAQA,EAAM,CACnCC,QAAS,CACP,eAAgBH,KACZG,EAAQM,QAENC,OAAOC,YAAYR,GACnBA,MAGLC,ICvBHQ,CAClB,kCACAC,KAAKC,WCUDC,EAAcC,IAAyB,CAC3C,IAAK,cACL,IAAK,eACL,IAAK,YACL,IAAK,YACL,IAAK,yBACJA,IAAS,iBAECC,EAAwB,CAACC,EAAI,IAAKC,KAE7C,GAAID,aAAaE,MAAO,CACtB,MAAMC,QAAEA,KAAYC,GAAQJ,EAC5BA,EAAIA,EAAEK,QAAU,IAChBJ,EAAI,CACFF,MAAOI,GAAWN,EAAWG,MAC1BI,EAEN,CAOD,OALAH,EAAI,CACFI,OAAQL,KACS,iBAANC,EAAiBA,EAAI,CAAEF,MAAOE,GAAKJ,EAAWG,KAGpDnB,EAAKoB,EAAG,CAAEI,OAAQL,GAAI"}
"use strict";class e extends Error{status;constructor(e=500,t){super("object"==typeof t?t.error:t),"object"==typeof t&&Object.assign(this,t),this.status=e}}const t=(e="text/plain; charset=utf-8",t)=>(o,{headers:s={},...r}={})=>void 0===o||"Response"===o?.constructor.name?o:new Response(t?t(o):o,{headers:{"content-type":e,...s.entries?Object.fromEntries(s):s},...r}),o=t("application/json; charset=utf-8",JSON.stringify),s=e=>({400:"Bad Request",401:"Unauthorized",403:"Forbidden",404:"Not Found",500:"Internal Server Error"}[e]||"Unknown Error"),r=t("text/plain; charset=utf-8",String),n=t("text/html"),a=t("image/jpeg"),c=t("image/png"),p=t("image/webp");exports.Router=({base:e="",routes:t=[],...o}={})=>({__proto__:new Proxy({},{get:(o,s,r,n)=>"handle"==s?r.fetch:(o,...a)=>t.push([s.toUpperCase?.(),RegExp(`^${(n=(e+o).replace(/\/+(\/|$)/g,"$1")).replace(/(\/?\.?):(\w+)\+/g,"($1(?<$2>*))").replace(/(\/?\.?):(\w+)/g,"($1(?<$2>[^$1/]+?))").replace(/\./g,"\\.").replace(/(\/?)\*/g,"($1.*)?")}/*$`),a,n])&&r}),routes:t,...o,async fetch(e,...o){let s,r,n=new URL(e.url),a=e.query={__proto__:null};for(let[e,t]of n.searchParams)a[e]=a[e]?[].concat(a[e],t):t;for(let[a,c,p,i]of t)if((a==e.method||"ALL"==a)&&(r=n.pathname.match(c))){e.params=r.groups||{},e.route=i;for(let t of p)if(null!=(s=await t(e.proxy??e,...o)))return s}}}),exports.StatusError=e,exports.createCors=(e={})=>{const{origins:t=["*"],maxAge:o,methods:s=["GET"],headers:r={}}=e;let n;const a="function"==typeof t?t:e=>t.includes(e)||t.includes("*"),c={"content-type":"application/json","Access-Control-Allow-Methods":s.join(", "),...r};o&&(c["Access-Control-Max-Age"]=o);return{corsify:e=>{if(!e)throw new Error("No fetch handler responded and no upstream to proxy to specified.");const{headers:t,status:o,body:s}=e;return[101,301,302,308].includes(o)||t.get("access-control-allow-origin")?e:new Response(s,{status:o,headers:{...Object.fromEntries(t),...c,...n,"content-type":t.get("content-type")}})},preflight:e=>{const t=[...new Set(["OPTIONS",...s])],o=e.headers.get("origin")||"";if(n=a(o)&&{"Access-Control-Allow-Origin":o},"OPTIONS"===e.method){const o={...c,"Access-Control-Allow-Methods":t.join(", "),"Access-Control-Allow-Headers":e.headers.get("Access-Control-Request-Headers"),...n};return new Response(null,{headers:e.headers.get("Origin")&&e.headers.get("Access-Control-Request-Method")&&e.headers.get("Access-Control-Request-Headers")?o:{Allow:t.join(", ")}})}}}},exports.createResponse=t,exports.error=(e=500,t)=>{if(e instanceof Error){const{message:o,...r}=e;e=e.status||500,t={error:o||s(e),...r}}return t={status:e,..."object"==typeof t?t:{error:t||s(e)}},o(t,{status:e})},exports.html=n,exports.jpeg=a,exports.json=o,exports.png=c,exports.status=(e,t)=>new Response(null,{...t,status:e}),exports.text=r,exports.webp=p,exports.withContent=async e=>{e.content=e.body?await e.clone().json().catch((()=>e.clone().formData())).catch((()=>e.text())):void 0},exports.withCookies=e=>{e.cookies=(e.headers.get("Cookie")||"").split(/;\s*/).map((e=>e.split(/=(.+)/))).reduce(((e,[t,o])=>o?(e[t]=o,e):e),{})},exports.withParams=e=>{e.proxy=new Proxy(e.proxy||e,{get:(t,o)=>void 0!==t[o]?t[o].bind?.(e)||t[o]:t?.params?.[o]})};
//# sourceMappingURL=index.js.map

{"version": 3, "file": "index.js", "sources": ["../src/src/StatusError.ts", "../src/src/createResponse.ts", "../src/src/json.ts", "../src/src/error.ts", "../src/src/text.ts", "../src/src/html.ts", "../src/src/jpeg.ts", "../src/src/png.ts", "../src/src/webp.ts", "../src/src/Router.ts", "../src/src/createCors.ts", "../src/src/status.ts", "../src/src/withContent.ts", "../src/src/withCookies.ts", "../src/src/withParams.ts"], "sourcesContent": [null, null, null, null, null, null, null, null, null, null, null, null, null, null, null], "names": ["StatusError", "Error", "status", "constructor", "body", "super", "error", "Object", "assign", "this", "createResponse", "format", "transform", "headers", "rest", "undefined", "name", "Response", "entries", "fromEntries", "json", "JSON", "stringify", "getMessage", "code", "text", "String", "html", "jpeg", "png", "webp", "base", "routes", "other", "__proto__", "Proxy", "get", "target", "prop", "receiver", "path", "fetch", "route", "handlers", "push", "toUpperCase", "RegExp", "replace", "async", "request", "args", "response", "match", "url", "URL", "query", "k", "v", "searchParams", "concat", "method", "regex", "pathname", "params", "groups", "handler", "proxy", "options", "origins", "maxAge", "methods", "<PERSON><PERSON><PERSON><PERSON>", "isAllowOrigin", "origin", "includes", "rHeaders", "join", "corsify", "preflight", "r", "useMethods", "Set", "reqHeaders", "Allow", "a", "b", "message", "err", "content", "clone", "catch", "formData", "cookies", "split", "map", "p", "reduce", "obj", "bind"], "mappings": "aAKM,MAAOA,UAAoBC,MAC/BC,OAGAC,YAAYD,EAAS,IAAKE,GACxBC,MAAsB,iBAATD,EAAoBA,EAAKE,MAAQF,GAC9B,iBAATA,GAAqBG,OAAOC,OAAOC,KAAML,GAChDK,KAAKP,OAASA,CACf,ECLU,MAAAQ,EACX,CACEC,EAAS,4BACTC,IAEF,CAACR,GAAQS,UAAU,CAAA,KAAOC,GAAS,CAAA,SACxBC,IAATX,GAAiD,aAA3BA,GAAMD,YAAYa,KACtCZ,EACA,IAAIa,SAASL,EAAYA,EAAUR,GAAQA,EAAM,CACnCS,QAAS,CACP,eAAgBF,KACZE,EAAQK,QAENX,OAAOY,YAAYN,GACnBA,MAGLC,ICvBVM,EAAOV,EAClB,kCACAW,KAAKC,WCUDC,EAAcC,IAAyB,CAC3C,IAAK,cACL,IAAK,eACL,IAAK,YACL,IAAK,YACL,IAAK,yBACJA,IAAS,iBClBCC,EAAOf,EAClB,4BACAgB,QCFWC,EAAOjB,EAAe,aCAtBkB,EAAOlB,EAAe,cCAtBmB,EAAMnB,EAAe,aCArBoB,EAAOpB,EAAe,6BCyEb,EAIlBqB,OAAO,GAAIC,SAAS,MAAOC,GAAyB,CAAE,KAExD,CACEC,UAAW,IAAIC,MAAM,GAAI,CAEvBC,IAAK,CAACC,EAAaC,EAAcC,EAAsBC,IAC7C,UAARF,EAAmBC,EAASE,MAE5B,CAACC,KAAkBC,IACjBX,EAAOY,KACL,CACEN,EAAKO,gBACLC,OAAO,KAAKN,GAAQT,EAAOW,GACxBK,QAAQ,aAAc,OACtBA,QAAQ,oBAAqB,gBAC7BA,QAAQ,kBAAmB,uBAC3BA,QAAQ,MAAO,OACfA,QAAQ,WAAY,iBAEvBJ,EACAH,KAECD,IAEXP,YACGC,EACHe,YAAaC,KAAyBC,GACpC,IAAIC,EAAUC,EAAOC,EAAM,IAAIC,IAAIL,EAAQI,KAAME,EAA6BN,EAAQM,MAAQ,CAAErB,UAAW,MAG3G,IAAK,IAAKsB,EAAGC,KAAMJ,EAAIK,aACrBH,EAAMC,GAAKD,EAAMC,GAAM,GAAgBG,OAAOJ,EAAMC,GAAIC,GAAKA,EAG/D,IAAK,IAAKG,EAAQC,EAAOlB,EAAUH,KAASR,EAC1C,IAAK4B,GAAUX,EAAQW,QAAoB,OAAVA,KAAqBR,EAAQC,EAAIS,SAASV,MAAMS,IAAS,CACxFZ,EAAQc,OAASX,EAAMY,QAAU,CAAA,EACjCf,EAAQP,MAAQF,EAChB,IAAK,IAAIyB,KAAWtB,EAClB,GAAqE,OAAhEQ,QAAiBc,EAAQhB,EAAQiB,OAASjB,KAAYC,IAAgB,OAAOC,CACrF,CACJ,6CC9GqB,CAACgB,EAAuB,MAEhD,MAAMC,QAAEA,EAAU,CAAC,KAAIC,OAAEA,EAAMC,QAAEA,EAAU,CAAC,OAAMzD,QAAEA,EAAU,CAAE,GAAKsD,EAErE,IAAII,EACJ,MAAMC,EAAmC,mBAAZJ,EACzBA,EACCK,GAAoBL,EAAQM,SAASD,IAAWL,EAAQM,SAAS,KAGhEC,EAAW,CACf,eAAgB,mBAChB,+BAAgCL,EAAQM,KAAK,SAC1C/D,GAIDwD,IAAQM,EAAS,0BAA4BN,GA+DjD,MAAO,CAAEQ,QA5BQ1B,IACf,IAAKA,EACH,MAAM,IAAIlD,MACR,qEAGJ,MAAMY,QAAEA,EAAOX,OAAEA,EAAME,KAAEA,GAAS+C,EAGlC,MACE,CAAC,IAAK,IAAK,IAAK,KAAKuB,SAASxE,IAC9BW,EAAQuB,IAAI,+BAELe,EAGF,IAAIlC,SAASb,EAAM,CACxBF,SACAW,QAAS,IACJN,OAAOY,YAAYN,MACnB8D,KACAJ,EACH,eAAgB1D,EAAQuB,IAAI,kBAE9B,EAIc0C,UA5DCC,IAEjB,MAAMC,EAAa,IAAI,IAAIC,IAAI,CAAC,aAAcX,KACxCG,EAASM,EAAElE,QAAQuB,IAAI,WAAa,GAM1C,GAHAmC,EAAcC,EAAcC,IAAW,CAAE,8BAA+BA,GAGvD,YAAbM,EAAEnB,OAAsB,CAC1B,MAAMsB,EAAa,IACdP,EACH,+BAAgCK,EAAWJ,KAAK,MAChD,+BAAgCG,EAAElE,QAAQuB,IACxC,qCAECmC,GAIL,OAAO,IAAItD,SAAS,KAAM,CACxBJ,QACEkE,EAAElE,QAAQuB,IAAI,WACd2C,EAAElE,QAAQuB,IAAI,kCACd2C,EAAElE,QAAQuB,IAAI,kCACV8C,EACA,CAAEC,MAAOH,EAAWJ,KAAK,QAElC,GAgC0B,yCPpEM,CAACQ,EAAI,IAAKC,KAE7C,GAAID,aAAanF,MAAO,CACtB,MAAMqF,QAAEA,KAAYC,GAAQH,EAC5BA,EAAIA,EAAElF,QAAU,IAChBmF,EAAI,CACF/E,MAAOgF,GAAW/D,EAAW6D,MAC1BG,EAEN,CAOD,OALAF,EAAI,CACFnF,OAAQkF,KACS,iBAANC,EAAiBA,EAAI,CAAE/E,MAAO+E,GAAK9D,EAAW6D,KAGpDhE,EAAKiE,EAAG,CAAEnF,OAAQkF,GAAI,4EQtCT,CAAClF,EAAgBiE,IACrC,IAAIlD,SAAS,KAAM,IAAKkD,EAASjE,6DCMR8C,MAAOC,IAChCA,EAAQuC,QAAUvC,EAAQ7C,WAChB6C,EAAQwC,QAAQrE,OACnBsE,OAAM,IAAMzC,EAAQwC,QAAQE,aAC5BD,OAAM,IAAMzC,EAAQxB,cACvBV,CAAS,sBCLagE,IAC1BA,EAAEa,SAAWb,EAAElE,QAAQuB,IAAI,WAAa,IACrCyD,MAAM,QACNC,KAAKC,GAAsBA,EAAEF,MAAM,WACnCG,QAAO,CAACZ,GAAkB5B,EAAGC,KAAgBA,GAAM2B,EAAE5B,GAAKC,EAAI2B,GAAKA,GAAI,CAAE,EAAC,qBCTpDnC,IACzBA,EAAQiB,MAAQ,IAAI/B,MAAMc,EAAQiB,OAASjB,EAAS,CAClDb,IAAK,CAAC6D,EAAK3D,SAAuBvB,IAAdkF,EAAI3D,GACN2D,EAAI3D,GAAM4D,OAAOjD,IAAYgD,EAAI3D,GACjC2D,GAAKlC,SAASzB,IAChC"}
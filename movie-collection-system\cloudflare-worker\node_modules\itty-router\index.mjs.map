{"version": 3, "file": "index.mjs", "sources": ["../src/src/Router.ts", "../src/src/StatusError.ts", "../src/src/createResponse.ts", "../src/src/json.ts", "../src/src/error.ts", "../src/src/status.ts", "../src/src/text.ts", "../src/src/html.ts", "../src/src/jpeg.ts", "../src/src/png.ts", "../src/src/webp.ts", "../src/src/withContent.ts", "../src/src/withCookies.ts", "../src/src/withParams.ts", "../src/src/createCors.ts"], "sourcesContent": [null, null, null, null, null, null, null, null, null, null, null, null, null, null, null], "names": ["Router", "base", "routes", "other", "__proto__", "Proxy", "get", "target", "prop", "receiver", "path", "fetch", "route", "handlers", "push", "toUpperCase", "RegExp", "replace", "async", "request", "args", "response", "match", "url", "URL", "query", "k", "v", "searchParams", "concat", "method", "regex", "pathname", "params", "groups", "handler", "proxy", "StatusError", "Error", "status", "constructor", "body", "super", "error", "Object", "assign", "this", "createResponse", "format", "transform", "headers", "rest", "undefined", "name", "Response", "entries", "fromEntries", "json", "JSON", "stringify", "getMessage", "code", "a", "b", "message", "err", "options", "text", "String", "html", "jpeg", "png", "webp", "<PERSON><PERSON><PERSON><PERSON>", "content", "clone", "catch", "formData", "withCookies", "r", "cookies", "split", "map", "p", "reduce", "withParams", "obj", "bind", "createCors", "origins", "maxAge", "methods", "<PERSON><PERSON><PERSON><PERSON>", "isAllowOrigin", "origin", "includes", "rHeaders", "join", "corsify", "preflight", "useMethods", "Set", "reqHeaders", "Allow"], "mappings": "AA2Ea,MAAAA,EAAS,EAIlBC,OAAO,GAAIC,SAAS,MAAOC,GAAyB,CAAE,KAExD,CACEC,UAAW,IAAIC,MAAM,GAAI,CAEvBC,IAAK,CAACC,EAAaC,EAAcC,EAAsBC,IAC7C,UAARF,EAAmBC,EAASE,MAE5B,CAACC,KAAkBC,IACjBX,EAAOY,KACL,CACEN,EAAKO,gBACLC,OAAO,KAAKN,GAAQT,EAAOW,GACxBK,QAAQ,aAAc,OACtBA,QAAQ,oBAAqB,gBAC7BA,QAAQ,kBAAmB,uBAC3BA,QAAQ,MAAO,OACfA,QAAQ,WAAY,iBAEvBJ,EACAH,KAECD,IAEXP,YACGC,EACHe,YAAaC,KAAyBC,GACpC,IAAIC,EAAUC,EAAOC,EAAM,IAAIC,IAAIL,EAAQI,KAAME,EAA6BN,EAAQM,MAAQ,CAAErB,UAAW,MAG3G,IAAK,IAAKsB,EAAGC,KAAMJ,EAAIK,aACrBH,EAAMC,GAAKD,EAAMC,GAAM,GAAgBG,OAAOJ,EAAMC,GAAIC,GAAKA,EAG/D,IAAK,IAAKG,EAAQC,EAAOlB,EAAUH,KAASR,EAC1C,IAAK4B,GAAUX,EAAQW,QAAoB,OAAVA,KAAqBR,EAAQC,EAAIS,SAASV,MAAMS,IAAS,CACxFZ,EAAQc,OAASX,EAAMY,QAAU,CAAA,EACjCf,EAAQP,MAAQF,EAChB,IAAK,IAAIyB,KAAWtB,EAClB,GAAqE,OAAhEQ,QAAiBc,EAAQhB,EAAQiB,OAASjB,KAAYC,IAAgB,OAAOC,CACrF,CACJ,ICnHC,MAAOgB,UAAoBC,MAC/BC,OAGAC,YAAYD,EAAS,IAAKE,GACxBC,MAAsB,iBAATD,EAAoBA,EAAKE,MAAQF,GAC9B,iBAATA,GAAqBG,OAAOC,OAAOC,KAAML,GAChDK,KAAKP,OAASA,CACf,ECLU,MAAAQ,EACX,CACEC,EAAS,4BACTC,IAEF,CAACR,GAAQS,UAAU,CAAA,KAAOC,GAAS,CAAA,SACxBC,IAATX,GAAiD,aAA3BA,GAAMD,YAAYa,KACtCZ,EACA,IAAIa,SAASL,EAAYA,EAAUR,GAAQA,EAAM,CACnCS,QAAS,CACP,eAAgBF,KACZE,EAAQK,QAENX,OAAOY,YAAYN,GACnBA,MAGLC,ICvBVM,EAAOV,EAClB,kCACAW,KAAKC,WCUDC,EAAcC,IAAyB,CAC3C,IAAK,cACL,IAAK,eACL,IAAK,YACL,IAAK,YACL,IAAK,yBACJA,IAAS,iBAEClB,EAAwB,CAACmB,EAAI,IAAKC,KAE7C,GAAID,aAAaxB,MAAO,CACtB,MAAM0B,QAAEA,KAAYC,GAAQH,EAC5BA,EAAIA,EAAEvB,QAAU,IAChBwB,EAAI,CACFpB,MAAOqB,GAAWJ,EAAWE,MAC1BG,EAEN,CAOD,OALAF,EAAI,CACFxB,OAAQuB,KACS,iBAANC,EAAiBA,EAAI,CAAEpB,MAAOoB,GAAKH,EAAWE,KAGpDL,EAAKM,EAAG,CAAExB,OAAQuB,GAAI,ECtClBvB,EAAS,CAACA,EAAgB2B,IACrC,IAAIZ,SAAS,KAAM,IAAKY,EAAS3B,WCCtB4B,EAAOpB,EAClB,4BACAqB,QCFWC,EAAOtB,EAAe,aCAtBuB,EAAOvB,EAAe,cCAtBwB,EAAMxB,EAAe,aCArByB,EAAOzB,EAAe,cCKtB0B,EAAcvD,MAAOC,IAChCA,EAAQuD,QAAUvD,EAAQsB,WAChBtB,EAAQwD,QAAQlB,OACnBmB,OAAM,IAAMzD,EAAQwD,QAAQE,aAC5BD,OAAM,IAAMzD,EAAQgD,cACvBf,CAAS,ECLF0B,EAAeC,IAC1BA,EAAEC,SAAWD,EAAE7B,QAAQ5C,IAAI,WAAa,IACrC2E,MAAM,QACNC,KAAKC,GAAsBA,EAAEF,MAAM,WACnCG,QAAO,CAACtB,GAAkBpC,EAAGC,KAAgBA,GAAMmC,EAAEpC,GAAKC,EAAImC,GAAKA,GAAI,CAAE,EAAC,ECTlEuB,EAAclE,IACzBA,EAAQiB,MAAQ,IAAI/B,MAAMc,EAAQiB,OAASjB,EAAS,CAClDb,IAAK,CAACgF,EAAK9E,SAAuB4C,IAAdkC,EAAI9E,GACN8E,EAAI9E,GAAM+E,OAAOpE,IAAYmE,EAAI9E,GACjC8E,GAAKrD,SAASzB,IAChC,ECGSgF,EAAa,CAACtB,EAAuB,MAEhD,MAAMuB,QAAEA,EAAU,CAAC,KAAIC,OAAEA,EAAMC,QAAEA,EAAU,CAAC,OAAMzC,QAAEA,EAAU,CAAE,GAAKgB,EAErE,IAAI0B,EACJ,MAAMC,EAAmC,mBAAZJ,EACzBA,EACCK,GAAoBL,EAAQM,SAASD,IAAWL,EAAQM,SAAS,KAGhEC,EAAW,CACf,eAAgB,mBAChB,+BAAgCL,EAAQM,KAAK,SAC1C/C,GAIDwC,IAAQM,EAAS,0BAA4BN,GA+DjD,MAAO,CAAEQ,QA5BQ7E,IACf,IAAKA,EACH,MAAM,IAAIiB,MACR,qEAGJ,MAAMY,QAAEA,EAAOX,OAAEA,EAAME,KAAEA,GAASpB,EAGlC,MACE,CAAC,IAAK,IAAK,IAAK,KAAK0E,SAASxD,IAC9BW,EAAQ5C,IAAI,+BAELe,EAGF,IAAIiC,SAASb,EAAM,CACxBF,SACAW,QAAS,IACJN,OAAOY,YAAYN,MACnB8C,KACAJ,EACH,eAAgB1C,EAAQ5C,IAAI,kBAE9B,EAIc6F,UA5DCpB,IAEjB,MAAMqB,EAAa,IAAI,IAAIC,IAAI,CAAC,aAAcV,KACxCG,EAASf,EAAE7B,QAAQ5C,IAAI,WAAa,GAM1C,GAHAsF,EAAcC,EAAcC,IAAW,CAAE,8BAA+BA,GAGvD,YAAbf,EAAEjD,OAAsB,CAC1B,MAAMwE,EAAa,IACdN,EACH,+BAAgCI,EAAWH,KAAK,MAChD,+BAAgClB,EAAE7B,QAAQ5C,IACxC,qCAECsF,GAIL,OAAO,IAAItC,SAAS,KAAM,CACxBJ,QACE6B,EAAE7B,QAAQ5C,IAAI,WACdyE,EAAE7B,QAAQ5C,IAAI,kCACdyE,EAAE7B,QAAQ5C,IAAI,kCACVgG,EACA,CAAEC,MAAOH,EAAWH,KAAK,QAElC,GAgC0B"}
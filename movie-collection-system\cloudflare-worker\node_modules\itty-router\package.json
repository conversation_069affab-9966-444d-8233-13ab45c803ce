{"name": "itty-router", "version": "4.2.2", "description": "A tiny, zero-dependency router, designed to make beautiful APIs in any environment.", "main": "./index.js", "module": "./index.mjs", "types": "./index.d.ts", "exports": {".": {"import": "./index.mjs", "require": "./index.js", "types": "./index.d.ts"}, "./createCors": {"import": "./createCors.mjs", "require": "./createCors.js", "types": "./createCors.d.ts"}, "./createResponse": {"import": "./createResponse.mjs", "require": "./createResponse.js", "types": "./createResponse.d.ts"}, "./error": {"import": "./error.mjs", "require": "./error.js", "types": "./error.d.ts"}, "./html": {"import": "./html.mjs", "require": "./html.js", "types": "./html.d.ts"}, "./jpeg": {"import": "./jpeg.mjs", "require": "./jpeg.js", "types": "./jpeg.d.ts"}, "./json": {"import": "./json.mjs", "require": "./json.js", "types": "./json.d.ts"}, "./png": {"import": "./png.mjs", "require": "./png.js", "types": "./png.d.ts"}, "./Router": {"import": "./Router.mjs", "require": "./Router.js", "types": "./Router.d.ts"}, "./status": {"import": "./status.mjs", "require": "./status.js", "types": "./status.d.ts"}, "./StatusError": {"import": "./StatusError.mjs", "require": "./StatusError.js", "types": "./StatusError.d.ts"}, "./text": {"import": "./text.mjs", "require": "./text.js", "types": "./text.d.ts"}, "./webp": {"import": "./webp.mjs", "require": "./webp.js", "types": "./webp.d.ts"}, "./websocket": {"import": "./websocket.mjs", "require": "./websocket.js", "types": "./websocket.d.ts"}, "./withContent": {"import": "./withContent.mjs", "require": "./withContent.js", "types": "./withContent.d.ts"}, "./withCookies": {"import": "./withCookies.mjs", "require": "./withCookies.js", "types": "./withCookies.d.ts"}, "./withParams": {"import": "./withParams.mjs", "require": "./withParams.js", "types": "./withParams.d.ts"}}, "keywords": ["api", "router", "cloudflare", "workers", "worker", "serverless", "cors", "middleware", "rest", "serviceworker", "nested"], "scripts": {"dev": "yarn test", "lint": "yarn run eslint src", "prettier": "prettier --write src test example", "format": "yarn lint && yarn prettier", "test": "vitest --config ./vitest.config.ts --coverage --reporter verbose", "test:once": "vitest run", "coverage": "vitest run --coverage", "coveralls": "yarn coverage && cat ./coverage/lcov.info | coveralls", "verify": "echo 'verifying module...' && yarn build && yarn test:once", "prerelease": "yarn verify", "prerelease:next": "yarn verify", "prebuild": "rimraf dist && mkdir dist", "build": "rollup -c", "release": "release --tag --push --patch --src=dist", "release:next": "release --tag --push --type=next --src=dist", "runtime:bun": "bun example/bun.ts", "runtime:node": "node example/node.js"}, "repository": {"type": "git", "url": "git+https://github.com/kwhitley/itty-router.git"}, "author": "<PERSON> <<EMAIL>>", "license": "MIT", "bugs": {"url": "https://github.com/kwhitley/itty-router/issues"}, "homepage": "https://itty.dev/itty-router", "devDependencies": {"@cloudflare/workers-types": "^4.20240312.0", "@rollup/plugin-multi-entry": "^6.0.1", "@rollup/plugin-terser": "^0.4.4", "@rollup/plugin-typescript": "^11.1.6", "@skypack/package-check": "^0.2.2", "@types/node": "^20.11.27", "@typescript-eslint/eslint-plugin": "^7.2.0", "@typescript-eslint/parser": "^7.2.0", "@vitejs/plugin-vue": "^5.0.4", "@vitest/coverage-c8": "^0.33.0", "@vitest/coverage-v8": "^1.3.1", "@whatwg-node/server": "^0.9.29", "coveralls": "^3.1.1", "eslint": "^8.57.0", "eslint-config-prettier": "^9.1.0", "fetch-mock": "^9.11.0", "fs-extra": "^11.2.0", "globby": "^14.0.1", "gzip-size": "^7.0.0", "http": "^0.0.1-security", "itty-router": "^4.2.0", "jsdom": "^24.0.0", "npm-run-all": "^4.1.5", "prettier": "^3.2.5", "rimraf": "^5.0.5", "rollup": "^4.13.0", "rollup-plugin-bundle-size": "^1.0.3", "rollup-plugin-copy": "^3.5.0", "rollup-plugin-multi-input": "^1.4.1", "ts-node": "^10.9.2", "typescript": "^5.4.2", "vite": "^5.1.6", "vitest": "^1.3.1", "yarn": "^1.22.22", "yarn-release": "^1.10.6"}}
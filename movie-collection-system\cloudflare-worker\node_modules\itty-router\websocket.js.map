{"version": 3, "file": "websocket.js", "sources": ["../src/src/websocket.ts", "../src/src/createResponse.ts"], "sourcesContent": [null, null], "names": ["client", "options", "format", "transform", "body", "headers", "rest", "undefined", "constructor", "name", "Response", "entries", "Object", "fromEntries", "createResponse", "status", "webSocket"], "mappings": "+BAEyB,CAACA,EAAmBC,EAAkB,CAAA,ICO7D,EACEC,EAAS,4BACTC,IAEF,CAACC,GAAQC,UAAU,CAAA,KAAOC,GAAS,CAAA,SACxBC,IAATH,GAAiD,aAA3BA,GAAMI,YAAYC,KACtCL,EACA,IAAIM,SAASP,EAAYA,EAAUC,GAAQA,EAAM,CACnCC,QAAS,CACP,eAAgBH,KACZG,EAAQM,QAENC,OAAOC,YAAYR,GACnBA,MAGLC,IDtBrBQ,GAAiB,KAAM,CACrBC,OAAQ,IACRC,UAAWhB,KACRC"}
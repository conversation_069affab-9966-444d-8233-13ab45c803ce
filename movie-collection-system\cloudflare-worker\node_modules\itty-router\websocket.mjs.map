{"version": 3, "file": "websocket.mjs", "sources": ["../src/src/createResponse.ts", "../src/src/websocket.ts"], "sourcesContent": [null, null], "names": ["websocket", "client", "options", "format", "transform", "body", "headers", "rest", "undefined", "constructor", "name", "Response", "entries", "Object", "fromEntries", "createResponse", "status", "webSocket"], "mappings": "AAQO,MCNMA,EAAY,CAACC,EAAmBC,EAAkB,CAAA,IDO7D,EACEC,EAAS,4BACTC,IAEF,CAACC,GAAQC,UAAU,CAAA,KAAOC,GAAS,CAAA,SACxBC,IAATH,GAAiD,aAA3BA,GAAMI,YAAYC,KACtCL,EACA,IAAIM,SAASP,EAAYA,EAAUC,GAAQA,EAAM,CACnCC,QAAS,CACP,eAAgBH,KACZG,EAAQM,QAENC,OAAOC,YAAYR,GACnBA,MAGLC,ICtBrBQ,GAAiB,KAAM,CACrBC,OAAQ,IACRC,UAAWhB,KACRC"}
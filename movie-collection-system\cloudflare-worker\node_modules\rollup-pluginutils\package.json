{"name": "rollup-pluginutils", "description": "Functionality commonly needed by Rollup plugins", "version": "2.8.2", "main": "dist/pluginutils.cjs.js", "module": "dist/pluginutils.es.js", "jsnext:main": "dist/pluginutils.es.js", "typings": "dist/pluginutils.d.ts", "files": ["src", "dist", "README.md"], "devDependencies": {"@types/estree": "0.0.39", "@types/jest": "^24.0.13", "@types/micromatch": "^3.1.0", "@types/node": "^12.0.4", "husky": "^3.0.5", "jest": "^24.8.0", "lint-staged": "^9.2.5", "micromatch": "^4.0.2", "prettier": "^1.17.1", "rollup": "^1.13.1", "rollup-plugin-commonjs": "^10.0.0", "rollup-plugin-node-resolve": "^5.0.1", "rollup-plugin-typescript": "^1.0.1", "shx": "^0.3.2", "ts-jest": "^24.0.2", "tslint": "^5.17.0", "typescript": "^3.5.1", "typescript-eslint-parser": "^22.0.0"}, "scripts": {"test": "jest", "build": "rollup -c && shx cp src/pluginutils.d.ts dist/pluginutils.d.ts", "lint": "npm run lint:nofix -- --fix", "lint:nofix": "tslint --project .", "pretest": "npm run build", "prepublishOnly": "npm test", "prepare": "npm run build"}, "dependencies": {"estree-walker": "^0.6.1"}, "repository": "rollup/rollup-pluginutils", "keywords": ["rollup", "utils"], "author": "<PERSON> <<EMAIL>>", "license": "MIT", "bugs": {"url": "https://github.com/rollup/rollup-pluginutils/issues"}, "homepage": "https://github.com/rollup/rollup-pluginutils#readme"}
{"name": "undici", "version": "5.29.0", "description": "An HTTP/1.1 client, written from scratch for Node.js", "homepage": "https://undici.nodejs.org", "bugs": {"url": "https://github.com/nodejs/undici/issues"}, "repository": {"type": "git", "url": "git+https://github.com/nodejs/undici.git"}, "license": "MIT", "contributors": [{"name": "<PERSON><PERSON>", "url": "https://github.com/dnlup", "author": true}, {"name": "<PERSON>", "url": "https://github.com/ethan-arrowood", "author": true}, {"name": "<PERSON>", "url": "https://github.com/mcollina", "author": true}, {"name": "<PERSON>", "url": "https://github.com/KhafraDev", "author": true}, {"name": "<PERSON>", "url": "https://github.com/ronag", "author": true}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/szmarczak", "author": true}, {"name": "<PERSON>", "url": "https://github.com/delvedor", "author": true}], "keywords": ["fetch", "http", "https", "promise", "request", "curl", "wget", "xhr", "whatwg"], "main": "index.js", "types": "index.d.ts", "files": ["*.d.ts", "index.js", "index-fetch.js", "lib", "types", "docs"], "scripts": {"build:node": "npx esbuild@0.19.4 index-fetch.js --bundle --platform=node --outfile=undici-fetch.js --define:esbuildDetection=1 --keep-names", "prebuild:wasm": "node build/wasm.js --prebuild", "build:wasm": "node build/wasm.js --docker", "lint": "standard | snazzy", "lint:fix": "standard --fix | snazzy", "test": "node scripts/generate-pem && npm run test:tap && npm run test:node-fetch && npm run test:fetch && npm run test:cookies && npm run test:wpt && npm run test:websocket && npm run test:jest && npm run test:typescript", "test:cookies": "node scripts/verifyVersion 16 || tap test/cookie/*.js", "test:node-fetch": "node scripts/verifyVersion.js 16 || mocha --exit test/node-fetch", "test:fetch": "node scripts/verifyVersion.js 16 || (npm run build:node && tap --expose-gc test/fetch/*.js && tap test/webidl/*.js)", "test:jest": "node scripts/verifyVersion.js 14 || jest", "test:tap": "tap test/*.js test/diagnostics-channel/*.js", "test:tdd": "tap test/*.js test/diagnostics-channel/*.js -w", "test:typescript": "node scripts/verifyVersion.js 14 || tsd && tsc --skipLibCheck test/imports/undici-import.ts", "test:websocket": "node scripts/verifyVersion.js 18 || tap test/websocket/*.js", "test:wpt": "node scripts/verifyVersion 18 || (node test/wpt/start-fetch.mjs && node test/wpt/start-FileAPI.mjs && node test/wpt/start-mimesniff.mjs && node test/wpt/start-xhr.mjs && node test/wpt/start-websockets.mjs)", "coverage": "nyc --reporter=text --reporter=html npm run test", "coverage:ci": "nyc --reporter=lcov npm run test", "bench": "PORT=3042 concurrently -k -s first npm:bench:server npm:bench:run", "bench:server": "node benchmarks/server.js", "prebench:run": "node benchmarks/wait.js", "bench:run": "CONNECTIONS=1 node benchmarks/benchmark.js; CONNECTIONS=50 node benchmarks/benchmark.js", "serve:website": "docsify serve .", "prepare": "husky install", "fuzz": "jsfuzz test/fuzzing/fuzz.js corpus"}, "devDependencies": {"@sinonjs/fake-timers": "^11.1.0", "@types/node": "^18.0.3", "abort-controller": "^3.0.0", "atomic-sleep": "^1.0.0", "chai": "^4.3.4", "chai-as-promised": "^7.1.1", "chai-iterator": "^3.0.2", "chai-string": "^1.5.0", "concurrently": "^8.0.1", "cronometro": "^1.0.5", "delay": "^5.0.0", "dns-packet": "^5.4.0", "docsify-cli": "^4.4.3", "form-data": "^4.0.0", "formdata-node": "^4.3.1", "https-pem": "^3.0.0", "husky": "^8.0.1", "import-fresh": "^3.3.0", "jest": "^29.0.2", "jsdom": "^23.0.0", "jsfuzz": "^1.0.15", "mocha": "^10.0.0", "mockttp": "^3.9.2", "p-timeout": "^3.2.0", "pre-commit": "^1.2.2", "proxy": "^1.0.2", "proxyquire": "^2.1.3", "semver": "^7.5.4", "sinon": "^17.0.1", "snazzy": "^9.0.0", "standard": "^17.0.0", "table": "^6.8.0", "tap": "^16.1.0", "tsd": "^0.29.0", "typescript": "^5.0.2", "wait-on": "^7.0.1", "ws": "^8.11.0"}, "engines": {"node": ">=14.0"}, "standard": {"env": ["mocha"], "ignore": ["lib/llhttp/constants.js", "lib/llhttp/utils.js", "test/wpt/tests"]}, "tsd": {"directory": "test/types", "compilerOptions": {"esModuleInterop": true, "lib": ["esnext"]}}, "jest": {"testMatch": ["<rootDir>/test/jest/**"]}, "dependencies": {"@fastify/busboy": "^2.0.0"}}
import type nodeChildProcess from "node:child_process";
export declare const ChildProcess: typeof nodeChildProcess.ChildProcess;
export declare const _forkChild: unknown;
export declare const exec: typeof nodeChildProcess.exec;
export declare const execFile: typeof nodeChildProcess.execFile;
export declare const execFileSync: typeof nodeChildProcess.execFileSync;
export declare const execSync: typeof nodeChildProcess.execSync;
export declare const fork: typeof nodeChildProcess.fork;
export declare const spawn: typeof nodeChildProcess.spawn;
export declare const spawnSync: typeof nodeChildProcess.spawnSync;
declare const _default: typeof nodeChildProcess;
export default _default;

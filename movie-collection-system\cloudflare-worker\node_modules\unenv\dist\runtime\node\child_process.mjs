import { notImplemented, notImplementedClass } from "../_internal/utils.mjs";
export const ChildProcess = /*@__PURE__*/ notImplementedClass("child_process.ChildProcess");
export const _forkChild = /*@__PURE__*/ notImplemented("child_process.ChildProcess");
export const exec = /*@__PURE__*/ notImplemented("child_process.exec");
export const execFile = /*@__PURE__*/ notImplemented("child_process.execFile");
export const execFileSync = /*@__PURE__*/ notImplemented("child_process.execFileSync");
export const execSync = /*@__PURE__*/ notImplemented("child_process.execSyn");
export const fork = /*@__PURE__*/ notImplemented("child_process.fork");
export const spawn = /*@__PURE__*/ notImplemented("child_process.spawn");
export const spawnSync = /*@__PURE__*/ notImplemented("child_process.spawnSync");
export default {
	ChildProcess,
	_forkChild,
	exec,
	execFile,
	execFileSync,
	execSync,
	fork,
	spawn,
	spawnSync
};

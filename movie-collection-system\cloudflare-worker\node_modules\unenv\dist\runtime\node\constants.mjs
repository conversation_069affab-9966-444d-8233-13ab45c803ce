import { errno, priority, signals, dlopen } from "./internal/os/constants.mjs";
import { UV_FS_SYMLINK_DIR, UV_FS_SYMLINK_JUNCTION, O_RDONLY, O_WRONLY, O_RDWR, UV_DIRENT_UNKNOWN, UV_DIRENT_FILE, UV_DIRENT_DIR, UV_DIRENT_LINK, UV_DIRENT_FIFO, UV_DIRENT_SOCKET, UV_DIRENT_CHAR, UV_DIRENT_BLOCK, EXTENSIONLESS_FORMAT_JAVASCRIPT, EXTENSIONLESS_FORMAT_WASM, S_IFMT, S_IFREG, S_IFDIR, S_IFCHR, S_<PERSON><PERSON><PERSON>, S_<PERSON>IFO, S_IFLNK, S_IFSOCK, O_CREAT, O_EXCL, UV_FS_O_FILEMAP, O_NOCTTY, O_TRUNC, O_APPEND, O_DIRECTORY, O_<PERSON><PERSON>IM<PERSON>, O_NOF<PERSON>L<PERSON>, <PERSON>_<PERSON>Y<PERSON>, O_<PERSON>Y<PERSON>, O_DIREC<PERSON>, O_<PERSON><PERSON><PERSON><PERSON><PERSON>, S_IRWX<PERSON>, S_IRUS<PERSON>, S_IWUS<PERSON>, S_IXUSR, S_IRWXG, S_IRGRP, S_IWGRP, S_IXGRP, S_IRWXO, S_IROTH, S_IWOTH, S_IXOTH, F_OK, R_OK, W_OK, X_OK, UV_FS_COPYFILE_EXCL, COPYFILE_EXCL, UV_FS_COPYFILE_FICLONE, COPYFILE_FICLONE, UV_FS_COPYFILE_FICLONE_FORCE, COPYFILE_FICLONE_FORCE } from "./internal/fs/constants.mjs";
import { OPENSSL_VERSION_NUMBER, SSL_OP_ALL, SSL_OP_ALLOW_NO_DHE_KEX, SSL_OP_ALLOW_UNSAFE_LEGACY_RENEGOTIATION, SSL_OP_CIPHER_SERVER_PREFERENCE, SSL_OP_CISCO_ANYCONNECT, SSL_OP_COOKIE_EXCHANGE, SSL_OP_CRYPTOPRO_TLSEXT_BUG, SSL_OP_DONT_INSERT_EMPTY_FRAGMENTS, SSL_OP_LEGACY_SERVER_CONNECT, SSL_OP_NO_COMPRESSION, SSL_OP_NO_ENCRYPT_THEN_MAC, SSL_OP_NO_QUERY_MTU, SSL_OP_NO_RENEGOTIATION, SSL_OP_NO_SESSION_RESUMPTION_ON_RENEGOTIATION, SSL_OP_NO_SSLv2, SSL_OP_NO_SSLv3, SSL_OP_NO_TICKET, SSL_OP_NO_TLSv1, SSL_OP_NO_TLSv1_1, SSL_OP_NO_TLSv1_2, SSL_OP_NO_TLSv1_3, SSL_OP_PRIORITIZE_CHACHA, SSL_OP_TLS_ROLLBACK_BUG, ENGINE_METHOD_RSA, ENGINE_METHOD_DSA, ENGINE_METHOD_DH, ENGINE_METHOD_RAND, ENGINE_METHOD_EC, ENGINE_METHOD_CIPHERS, ENGINE_METHOD_DIGESTS, ENGINE_METHOD_PKEY_METHS, ENGINE_METHOD_PKEY_ASN1_METHS, ENGINE_METHOD_ALL, ENGINE_METHOD_NONE, DH_CHECK_P_NOT_SAFE_PRIME, DH_CHECK_P_NOT_PRIME, DH_UNABLE_TO_CHECK_GENERATOR, DH_NOT_SUITABLE_GENERATOR, RSA_PKCS1_PADDING, RSA_NO_PADDING, RSA_PKCS1_OAEP_PADDING, RSA_X931_PADDING, RSA_PKCS1_PSS_PADDING, RSA_PSS_SALTLEN_DIGEST, RSA_PSS_SALTLEN_MAX_SIGN, RSA_PSS_SALTLEN_AUTO, defaultCoreCipherList, TLS1_VERSION, TLS1_1_VERSION, TLS1_2_VERSION, TLS1_3_VERSION, POINT_CONVERSION_COMPRESSED, POINT_CONVERSION_UNCOMPRESSED, POINT_CONVERSION_HYBRID } from "./internal/crypto/constants.mjs";
export * from "./internal/fs/constants.mjs";
export { OPENSSL_VERSION_NUMBER, SSL_OP_ALL, SSL_OP_ALLOW_NO_DHE_KEX, SSL_OP_ALLOW_UNSAFE_LEGACY_RENEGOTIATION, SSL_OP_CIPHER_SERVER_PREFERENCE, SSL_OP_CISCO_ANYCONNECT, SSL_OP_COOKIE_EXCHANGE, SSL_OP_CRYPTOPRO_TLSEXT_BUG, SSL_OP_DONT_INSERT_EMPTY_FRAGMENTS, SSL_OP_LEGACY_SERVER_CONNECT, SSL_OP_NO_COMPRESSION, SSL_OP_NO_ENCRYPT_THEN_MAC, SSL_OP_NO_QUERY_MTU, SSL_OP_NO_RENEGOTIATION, SSL_OP_NO_SESSION_RESUMPTION_ON_RENEGOTIATION, SSL_OP_NO_SSLv2, SSL_OP_NO_SSLv3, SSL_OP_NO_TICKET, SSL_OP_NO_TLSv1, SSL_OP_NO_TLSv1_1, SSL_OP_NO_TLSv1_2, SSL_OP_NO_TLSv1_3, SSL_OP_PRIORITIZE_CHACHA, SSL_OP_TLS_ROLLBACK_BUG, ENGINE_METHOD_RSA, ENGINE_METHOD_DSA, ENGINE_METHOD_DH, ENGINE_METHOD_RAND, ENGINE_METHOD_EC, ENGINE_METHOD_CIPHERS, ENGINE_METHOD_DIGESTS, ENGINE_METHOD_PKEY_METHS, ENGINE_METHOD_PKEY_ASN1_METHS, ENGINE_METHOD_ALL, ENGINE_METHOD_NONE, DH_CHECK_P_NOT_SAFE_PRIME, DH_CHECK_P_NOT_PRIME, DH_UNABLE_TO_CHECK_GENERATOR, DH_NOT_SUITABLE_GENERATOR, RSA_PKCS1_PADDING, RSA_NO_PADDING, RSA_PKCS1_OAEP_PADDING, RSA_X931_PADDING, RSA_PKCS1_PSS_PADDING, RSA_PSS_SALTLEN_DIGEST, RSA_PSS_SALTLEN_MAX_SIGN, RSA_PSS_SALTLEN_AUTO, defaultCoreCipherList, TLS1_VERSION, TLS1_1_VERSION, TLS1_2_VERSION, TLS1_3_VERSION, POINT_CONVERSION_COMPRESSED, POINT_CONVERSION_UNCOMPRESSED, POINT_CONVERSION_HYBRID } from "./internal/crypto/constants.mjs";
export const { RTLD_LAZY, RTLD_NOW, RTLD_GLOBAL, RTLD_LOCAL, RTLD_DEEPBIND } = dlopen;
export const { E2BIG, EACCES, EADDRINUSE, EADDRNOTAVAIL, EAFNOSUPPORT, EAGAIN, EALREADY, EBADF, EBADMSG, EBUSY, ECANCELED, ECHILD, ECONNABORTED, ECONNREFUSED, ECONNRESET, EDEADLK, EDESTADDRREQ, EDOM, EDQUOT, EEXIST, EFAULT, EFBIG, EHOSTUNREACH, EIDRM, EILSEQ, EINPROGRESS, EINTR, EINVAL, EIO, EISCONN, EISDIR, ELOOP, EMFILE, EMLINK, EMSGSIZE, EMULTIHOP, ENAMETOOLONG, ENETDOWN, ENETRESET, ENETUNREACH, ENFILE, ENOBUFS, ENODATA, ENODEV, ENOENT, ENOEXEC, ENOLCK, ENOLINK, ENOMEM, ENOMSG, ENOPROTOOPT, ENOSPC, ENOSR, ENOSTR, ENOSYS, ENOTCONN, ENOTDIR, ENOTEMPTY, ENOTSOCK, ENOTSUP, ENOTTY, ENXIO, EOPNOTSUPP, EOVERFLOW, EPERM, EPIPE, EPROTO, EPROTONOSUPPORT, EPROTOTYPE, ERANGE, EROFS, ESPIPE, ESRCH, ESTALE, ETIME, ETIMEDOUT, ETXTBSY, EWOULDBLOCK, EXDEV } = errno;
export const { PRIORITY_LOW, PRIORITY_BELOW_NORMAL, PRIORITY_NORMAL, PRIORITY_ABOVE_NORMAL, PRIORITY_HIGH, PRIORITY_HIGHEST } = priority;
export const { SIGHUP, SIGINT, SIGQUIT, SIGILL, SIGTRAP, SIGABRT, SIGIOT, SIGBUS, SIGFPE, SIGKILL, SIGUSR1, SIGSEGV, SIGUSR2, SIGPIPE, SIGALRM, SIGTERM, SIGCHLD, SIGSTKFLT, SIGCONT, SIGSTOP, SIGTSTP, SIGTTIN, SIGTTOU, SIGURG, SIGXCPU, SIGXFSZ, SIGVTALRM, SIGPROF, SIGWINCH, SIGIO, SIGPOLL, SIGPWR, SIGSYS } = signals;
export default {
	OPENSSL_VERSION_NUMBER,
	SSL_OP_ALL,
	SSL_OP_ALLOW_NO_DHE_KEX,
	SSL_OP_ALLOW_UNSAFE_LEGACY_RENEGOTIATION,
	SSL_OP_CIPHER_SERVER_PREFERENCE,
	SSL_OP_CISCO_ANYCONNECT,
	SSL_OP_COOKIE_EXCHANGE,
	SSL_OP_CRYPTOPRO_TLSEXT_BUG,
	SSL_OP_DONT_INSERT_EMPTY_FRAGMENTS,
	SSL_OP_LEGACY_SERVER_CONNECT,
	SSL_OP_NO_COMPRESSION,
	SSL_OP_NO_ENCRYPT_THEN_MAC,
	SSL_OP_NO_QUERY_MTU,
	SSL_OP_NO_RENEGOTIATION,
	SSL_OP_NO_SESSION_RESUMPTION_ON_RENEGOTIATION,
	SSL_OP_NO_SSLv2,
	SSL_OP_NO_SSLv3,
	SSL_OP_NO_TICKET,
	SSL_OP_NO_TLSv1,
	SSL_OP_NO_TLSv1_1,
	SSL_OP_NO_TLSv1_2,
	SSL_OP_NO_TLSv1_3,
	SSL_OP_PRIORITIZE_CHACHA,
	SSL_OP_TLS_ROLLBACK_BUG,
	ENGINE_METHOD_RSA,
	ENGINE_METHOD_DSA,
	ENGINE_METHOD_DH,
	ENGINE_METHOD_RAND,
	ENGINE_METHOD_EC,
	ENGINE_METHOD_CIPHERS,
	ENGINE_METHOD_DIGESTS,
	ENGINE_METHOD_PKEY_METHS,
	ENGINE_METHOD_PKEY_ASN1_METHS,
	ENGINE_METHOD_ALL,
	ENGINE_METHOD_NONE,
	DH_CHECK_P_NOT_SAFE_PRIME,
	DH_CHECK_P_NOT_PRIME,
	DH_UNABLE_TO_CHECK_GENERATOR,
	DH_NOT_SUITABLE_GENERATOR,
	RSA_PKCS1_PADDING,
	RSA_NO_PADDING,
	RSA_PKCS1_OAEP_PADDING,
	RSA_X931_PADDING,
	RSA_PKCS1_PSS_PADDING,
	RSA_PSS_SALTLEN_DIGEST,
	RSA_PSS_SALTLEN_MAX_SIGN,
	RSA_PSS_SALTLEN_AUTO,
	defaultCoreCipherList,
	TLS1_VERSION,
	TLS1_1_VERSION,
	TLS1_2_VERSION,
	TLS1_3_VERSION,
	POINT_CONVERSION_COMPRESSED,
	POINT_CONVERSION_UNCOMPRESSED,
	POINT_CONVERSION_HYBRID,
	UV_FS_SYMLINK_DIR,
	UV_FS_SYMLINK_JUNCTION,
	O_RDONLY,
	O_WRONLY,
	O_RDWR,
	UV_DIRENT_UNKNOWN,
	UV_DIRENT_FILE,
	UV_DIRENT_DIR,
	UV_DIRENT_LINK,
	UV_DIRENT_FIFO,
	UV_DIRENT_SOCKET,
	UV_DIRENT_CHAR,
	UV_DIRENT_BLOCK,
	EXTENSIONLESS_FORMAT_JAVASCRIPT,
	EXTENSIONLESS_FORMAT_WASM,
	S_IFMT,
	S_IFREG,
	S_IFDIR,
	S_IFCHR,
	S_IFBLK,
	S_IFIFO,
	S_IFLNK,
	S_IFSOCK,
	O_CREAT,
	O_EXCL,
	UV_FS_O_FILEMAP,
	O_NOCTTY,
	O_TRUNC,
	O_APPEND,
	O_DIRECTORY,
	O_NOATIME,
	O_NOFOLLOW,
	O_SYNC,
	O_DSYNC,
	O_DIRECT,
	O_NONBLOCK,
	S_IRWXU,
	S_IRUSR,
	S_IWUSR,
	S_IXUSR,
	S_IRWXG,
	S_IRGRP,
	S_IWGRP,
	S_IXGRP,
	S_IRWXO,
	S_IROTH,
	S_IWOTH,
	S_IXOTH,
	F_OK,
	R_OK,
	W_OK,
	X_OK,
	UV_FS_COPYFILE_EXCL,
	COPYFILE_EXCL,
	UV_FS_COPYFILE_FICLONE,
	COPYFILE_FICLONE,
	UV_FS_COPYFILE_FICLONE_FORCE,
	COPYFILE_FICLONE_FORCE,
	E2BIG,
	EACCES,
	EADDRINUSE,
	EADDRNOTAVAIL,
	EAFNOSUPPORT,
	EAGAIN,
	EALREADY,
	EBADF,
	EBADMSG,
	EBUSY,
	ECANCELED,
	ECHILD,
	ECONNABORTED,
	ECONNREFUSED,
	ECONNRESET,
	EDEADLK,
	EDESTADDRREQ,
	EDOM,
	EDQUOT,
	EEXIST,
	EFAULT,
	EFBIG,
	EHOSTUNREACH,
	EIDRM,
	EILSEQ,
	EINPROGRESS,
	EINTR,
	EINVAL,
	EIO,
	EISCONN,
	EISDIR,
	ELOOP,
	EMFILE,
	EMLINK,
	EMSGSIZE,
	EMULTIHOP,
	ENAMETOOLONG,
	ENETDOWN,
	ENETRESET,
	ENETUNREACH,
	ENFILE,
	ENOBUFS,
	ENODATA,
	ENODEV,
	ENOENT,
	ENOEXEC,
	ENOLCK,
	ENOLINK,
	ENOMEM,
	ENOMSG,
	ENOPROTOOPT,
	ENOSPC,
	ENOSR,
	ENOSTR,
	ENOSYS,
	ENOTCONN,
	ENOTDIR,
	ENOTEMPTY,
	ENOTSOCK,
	ENOTSUP,
	ENOTTY,
	ENXIO,
	EOPNOTSUPP,
	EOVERFLOW,
	EPERM,
	EPIPE,
	EPROTO,
	EPROTONOSUPPORT,
	EPROTOTYPE,
	ERANGE,
	EROFS,
	ESPIPE,
	ESRCH,
	ESTALE,
	ETIME,
	ETIMEDOUT,
	ETXTBSY,
	EWOULDBLOCK,
	EXDEV,
	RTLD_LAZY,
	RTLD_NOW,
	RTLD_GLOBAL,
	RTLD_LOCAL,
	RTLD_DEEPBIND,
	PRIORITY_LOW,
	PRIORITY_BELOW_NORMAL,
	PRIORITY_NORMAL,
	PRIORITY_ABOVE_NORMAL,
	PRIORITY_HIGH,
	PRIORITY_HIGHEST,
	SIGHUP,
	SIGINT,
	SIGQUIT,
	SIGILL,
	SIGTRAP,
	SIGABRT,
	SIGIOT,
	SIGBUS,
	SIGFPE,
	SIGKILL,
	SIGUSR1,
	SIGSEGV,
	SIGUSR2,
	SIGPIPE,
	SIGALRM,
	SIGTERM,
	SIGCHLD,
	SIGSTKFLT,
	SIGCONT,
	SIGSTOP,
	SIGTSTP,
	SIGTTIN,
	SIGTTOU,
	SIGURG,
	SIGXCPU,
	SIGXFSZ,
	SIGVTALRM,
	SIGPROF,
	SIGWINCH,
	SIGIO,
	SIGPOLL,
	SIGPWR,
	SIGSYS
};

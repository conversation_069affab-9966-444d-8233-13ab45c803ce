export declare const NODE_PERFORMANCE_GC_MAJOR = 4;
export declare const NODE_PERFORMANCE_GC_MINOR = 1;
export declare const NODE_PERFORMANCE_GC_INCREMENTAL = 8;
export declare const NODE_PERFORMANCE_GC_WEAKCB = 16;
export declare const NODE_PERFORMANCE_GC_FLAGS_NO = 0;
export declare const NODE_PERFORMANCE_GC_FLAGS_CONSTRUCT_RETAINED = 2;
export declare const NODE_PERFORMANCE_GC_FLAGS_FORCED = 4;
export declare const NODE_PERFORMANCE_GC_FLAGS_SYNCHRONOUS_PHANTOM_PROCESSING = 8;
export declare const NODE_PERFORMANCE_GC_FLAGS_ALL_AVAILABLE_GARBAGE = 16;
export declare const NODE_PERFORMANCE_GC_FLAGS_ALL_EXTERNAL_MEMORY = 32;
export declare const NODE_PERFORMANCE_GC_FLAGS_SCHEDULE_IDLE = 64;
export declare const NODE_PERFORMANCE_ENTRY_TYPE_GC = 0;
export declare const NODE_PERFORMANCE_ENTRY_TYPE_HTTP = 1;
export declare const NODE_PERFORMANCE_ENTRY_TYPE_HTTP2 = 2;
export declare const NODE_PERFORMANCE_ENTRY_TYPE_NET = 3;
export declare const NODE_PERFORMANCE_ENTRY_TYPE_DNS = 4;
export declare const NODE_PERFORMANCE_MILESTONE_TIME_ORIGIN_TIMESTAMP = 0;
export declare const NODE_PERFORMANCE_MILESTONE_TIME_ORIGIN = 1;
export declare const NODE_PERFORMANCE_MILESTONE_ENVIRONMENT = 2;
export declare const NODE_PERFORMANCE_MILESTONE_NODE_START = 3;
export declare const NODE_PERFORMANCE_MILESTONE_V8_START = 4;
export declare const NODE_PERFORMANCE_MILESTONE_LOOP_START = 5;
export declare const NODE_PERFORMANCE_MILESTONE_LOOP_EXIT = 6;
export declare const NODE_PERFORMANCE_MILESTONE_BOOTSTRAP_COMPLETE = 7;

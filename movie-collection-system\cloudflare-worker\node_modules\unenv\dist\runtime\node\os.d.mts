import type nodeOs from "node:os";
export declare const constants: typeof nodeOs.constants;
export declare const availableParallelism: typeof nodeOs.availableParallelism;
export declare const arch: typeof nodeOs.arch;
export declare const machine: typeof nodeOs.machine;
export declare const endianness: typeof nodeOs.endianness;
export declare const cpus: typeof nodeOs.cpus;
export declare const getPriority: typeof nodeOs.getPriority;
export declare const setPriority: typeof nodeOs.setPriority;
export declare const homedir: typeof nodeOs.homedir;
export declare const tmpdir: typeof nodeOs.tmpdir;
export declare const devNull: typeof nodeOs.devNull;
export declare const freemem: typeof nodeOs.freemem;
export declare const totalmem: typeof nodeOs.totalmem;
export declare const loadavg: typeof nodeOs.loadavg;
export declare const uptime: typeof nodeOs.uptime;
export declare const hostname: typeof nodeOs.hostname;
export declare const networkInterfaces: typeof nodeOs.networkInterfaces;
export declare const platform: typeof nodeOs.platform;
export declare const type: typeof nodeOs.type;
export declare const release: typeof nodeOs.release;
export declare const version: typeof nodeOs.version;
export declare const userInfo: typeof nodeOs.userInfo;
export declare const EOL: typeof nodeOs.EOL;
declare const _default: {};
export default _default;

// 添加更多电影数据到Cloudflare D1数据库
import fs from 'fs';
import path from 'path';

// 读取SQL文件
const sqlFile = path.join(process.cwd(), 'migrations', '0003_add_more_movies.sql');
const sqlContent = fs.readFileSync(sqlFile, 'utf8');

// 执行数据库迁移
async function addMoreMovies() {
  try {
    console.log('开始添加更多电影数据...');
    
    // 这里需要根据您的Cloudflare D1配置来执行SQL
    // 您可以使用wrangler d1 execute命令或者通过API调用
    
    console.log('SQL内容预览:');
    console.log(sqlContent.substring(0, 500) + '...');
    
    console.log('\n请使用以下命令执行迁移:');
    console.log('npx wrangler d1 execute nantingyouyu --file=migrations/0003_add_more_movies.sql');
    
  } catch (error) {
    console.error('添加电影数据时出错:', error);
  }
}

addMoreMovies();

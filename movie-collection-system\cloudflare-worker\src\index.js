/**
 * Cloudflare Workers API for Movie Collection System
 */

import { Router } from 'itty-router'
import { corsHeaders, handleCORS } from './utils/cors'
import { movieRoutes } from './routes/movies'

const router = Router()

// CORS预检请求
router.options('*', handleCORS)

// 测试路由
router.get('/api/test', async (request, env) => {
  try {
    const { results } = await env.DB.prepare('SELECT COUNT(*) as count FROM movies').all()
    return new Response(JSON.stringify({
      success: true,
      count: results[0].count
    }), {
      headers: {
        'Content-Type': 'application/json',
        ...corsHeaders
      }
    })
  } catch (error) {
    return new Response(JSON.stringify({
      success: false,
      error: error.message
    }), {
      status: 500,
      headers: {
        'Content-Type': 'application/json',
        ...corsHeaders
      }
    })
  }
})

// 热门电影路由
router.get('/api/movies/popular', async (request, env) => {
  try {
    const url = new URL(request.url)
    const page = parseInt(url.searchParams.get('page') || '0')
    const size = parseInt(url.searchParams.get('size') || '10')

    const { results } = await env.DB.prepare(
      'SELECT * FROM movies ORDER BY collection_count DESC, average_rating DESC LIMIT ? OFFSET ?'
    ).bind(size, page * size).all()

    return new Response(JSON.stringify({
      success: true,
      movies: results,
      currentPage: page,
      totalPages: 1,
      totalElements: results.length,
      size: size
    }), {
      headers: {
        'Content-Type': 'application/json',
        ...corsHeaders
      }
    })
  } catch (error) {
    console.error('获取热门电影失败:', error)
    return new Response(JSON.stringify({
      success: false,
      message: '获取热门电影失败',
      error: error.message
    }), {
      status: 500,
      headers: {
        'Content-Type': 'application/json',
        ...corsHeaders
      }
    })
  }
})

// 高评分电影路由
router.get('/api/movies/top-rated', async (request, env) => {
  try {
    const url = new URL(request.url)
    const page = parseInt(url.searchParams.get('page') || '0')
    const size = parseInt(url.searchParams.get('size') || '10')
    const minRating = parseFloat(url.searchParams.get('minRating') || '8.0')

    const { results } = await env.DB.prepare(
      'SELECT * FROM movies WHERE average_rating >= ? ORDER BY average_rating DESC LIMIT ? OFFSET ?'
    ).bind(minRating, size, page * size).all()

    return new Response(JSON.stringify({
      success: true,
      movies: results,
      currentPage: page,
      totalPages: 1,
      totalElements: results.length,
      size: size
    }), {
      headers: {
        'Content-Type': 'application/json',
        ...corsHeaders
      }
    })
  } catch (error) {
    console.error('获取高评分电影失败:', error)
    return new Response(JSON.stringify({
      success: false,
      message: '获取高评分电影失败',
      error: error.message
    }), {
      status: 500,
      headers: {
        'Content-Type': 'application/json',
        ...corsHeaders
      }
    })
  }
})

// 最新电影路由
router.get('/api/movies/latest', async (request, env) => {
  try {
    const url = new URL(request.url)
    const page = parseInt(url.searchParams.get('page') || '0')
    const size = parseInt(url.searchParams.get('size') || '10')

    const { results } = await env.DB.prepare(
      'SELECT * FROM movies ORDER BY created_at DESC LIMIT ? OFFSET ?'
    ).bind(size, page * size).all()

    return new Response(JSON.stringify({
      success: true,
      movies: results,
      currentPage: page,
      totalPages: 1,
      totalElements: results.length,
      size: size
    }), {
      headers: {
        'Content-Type': 'application/json',
        ...corsHeaders
      }
    })
  } catch (error) {
    console.error('获取最新电影失败:', error)
    return new Response(JSON.stringify({
      success: false,
      message: '获取最新电影失败',
      error: error.message
    }), {
      status: 500,
      headers: {
        'Content-Type': 'application/json',
        ...corsHeaders
      }
    })
  }
})

// 电影统计信息
router.get('/api/movies/stats', async (request, env) => {
  try {
    const { results } = await env.DB.prepare('SELECT COUNT(*) as total_movies FROM movies').all()
    const totalMovies = results[0]?.total_movies || 0

    return new Response(JSON.stringify({
      success: true,
      stats: {
        totalMovies: totalMovies
      }
    }), {
      headers: {
        'Content-Type': 'application/json',
        ...corsHeaders
      }
    })
  } catch (error) {
    console.error('获取电影统计失败:', error)
    return new Response(JSON.stringify({
      success: false,
      message: '获取统计信息失败'
    }), {
      status: 500,
      headers: {
        'Content-Type': 'application/json',
        ...corsHeaders
      }
    })
  }
})

// 电影详情路由
router.get('/api/movies/:id', async (request, env) => {
  try {
    const url = new URL(request.url)
    const movieId = url.pathname.split('/').pop()

    const { results } = await env.DB.prepare(
      'SELECT * FROM movies WHERE id = ?'
    ).bind(movieId).all()

    if (results.length === 0) {
      return new Response(JSON.stringify({
        success: false,
        message: '电影不存在'
      }), {
        status: 404,
        headers: {
          'Content-Type': 'application/json',
          ...corsHeaders
        }
      })
    }

    const movie = results[0]

    return new Response(JSON.stringify({
      success: true,
      movie: movie
    }), {
      headers: {
        'Content-Type': 'application/json',
        ...corsHeaders
      }
    })
  } catch (error) {
    console.error('获取电影详情失败:', error)
    return new Response(JSON.stringify({
      success: false,
      message: '获取电影详情失败',
      error: error.message
    }), {
      status: 500,
      headers: {
        'Content-Type': 'application/json',
        ...corsHeaders
      }
    })
  }
})

// 基础电影列表路由
router.get('/api/movies', async (request, env) => {
  try {
    const url = new URL(request.url)
    const page = parseInt(url.searchParams.get('page') || '0')
    const size = parseInt(url.searchParams.get('size') || '10')
    const search = url.searchParams.get('search') || ''

    let query = 'SELECT * FROM movies'
    let params = []

    if (search) {
      query += ' WHERE title LIKE ?'
      params.push(`%${search}%`)
    }

    query += ' ORDER BY average_rating DESC LIMIT ? OFFSET ?'
    params.push(size, page * size)

    const { results } = await env.DB.prepare(query).bind(...params).all()

    return new Response(JSON.stringify({
      success: true,
      movies: results,
      currentPage: page,
      totalPages: Math.ceil(results.length / size),
      totalElements: results.length,
      size: size
    }), {
      headers: {
        'Content-Type': 'application/json',
        ...corsHeaders
      }
    })
  } catch (error) {
    console.error('获取电影列表失败:', error)
    return new Response(JSON.stringify({
      success: false,
      message: '获取电影列表失败',
      error: error.message
    }), {
      status: 500,
      headers: {
        'Content-Type': 'application/json',
        ...corsHeaders
      }
    })
  }
})

// 获取电影评分
router.get('/api/movies/:id/ratings', async (request, env) => {
  try {
    const url = new URL(request.url)
    const movieId = url.pathname.split('/')[3] // /api/movies/:id/ratings

    const { results } = await env.DB.prepare(
      'SELECT * FROM ratings WHERE movie_id = ? ORDER BY created_at DESC'
    ).bind(movieId).all()

    return new Response(JSON.stringify({
      success: true,
      ratings: results
    }), {
      headers: {
        'Content-Type': 'application/json',
        ...corsHeaders
      }
    })
  } catch (error) {
    console.error('获取电影评分失败:', error)
    return new Response(JSON.stringify({
      success: false,
      message: '获取电影评分失败',
      error: error.message
    }), {
      status: 500,
      headers: {
        'Content-Type': 'application/json',
        ...corsHeaders
      }
    })
  }
})

// 添加电影评分
router.post('/api/movies/:id/ratings', async (request, env) => {
  try {
    const url = new URL(request.url)
    const movieId = url.pathname.split('/')[3]
    const body = await request.json()

    const { user_id, score, comment } = body

    // 检查是否已经评分过
    const { results: existing } = await env.DB.prepare(
      'SELECT * FROM ratings WHERE user_id = ? AND movie_id = ?'
    ).bind(user_id, movieId).all()

    if (existing.length > 0) {
      // 更新评分
      await env.DB.prepare(
        'UPDATE ratings SET score = ?, comment = ?, updated_at = CURRENT_TIMESTAMP WHERE user_id = ? AND movie_id = ?'
      ).bind(score, comment, user_id, movieId).run()
    } else {
      // 新增评分
      await env.DB.prepare(
        'INSERT INTO ratings (user_id, movie_id, score, comment) VALUES (?, ?, ?, ?)'
      ).bind(user_id, movieId, score, comment).run()
    }

    return new Response(JSON.stringify({
      success: true,
      message: '评分成功'
    }), {
      headers: {
        'Content-Type': 'application/json',
        ...corsHeaders
      }
    })
  } catch (error) {
    console.error('添加评分失败:', error)
    return new Response(JSON.stringify({
      success: false,
      message: '添加评分失败',
      error: error.message
    }), {
      status: 500,
      headers: {
        'Content-Type': 'application/json',
        ...corsHeaders
      }
    })
  }
})

// 健康检查
router.get('/api/health', () => {
  return new Response(JSON.stringify({
    status: 'ok',
    timestamp: new Date().toISOString(),
    version: '1.0.0'
  }), {
    headers: {
      'Content-Type': 'application/json',
      ...corsHeaders
    }
  })
})

// 404处理
router.all('*', () => {
  return new Response(JSON.stringify({
    success: false,
    message: 'API endpoint not found'
  }), {
    status: 404,
    headers: {
      'Content-Type': 'application/json',
      ...corsHeaders
    }
  })
})

export default {
  async fetch(request, env, ctx) {
    try {
      return await router.handle(request, env, ctx)
    } catch (error) {
      console.error('Worker error:', error)
      return new Response(JSON.stringify({
        success: false,
        message: 'Internal server error'
      }), {
        status: 500,
        headers: {
          'Content-Type': 'application/json',
          ...corsHeaders
        }
      })
    }
  }
}

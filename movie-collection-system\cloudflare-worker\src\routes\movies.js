/**
 * 电影相关路由
 */

import { Router } from 'itty-router'
import { corsHeaders } from '../utils/cors'

const router = Router({ base: '/api/movies' })

// 获取电影列表
router.get('/', async (request, env) => {
  try {
    const url = new URL(request.url)
    const page = parseInt(url.searchParams.get('page') || '0')
    const size = parseInt(url.searchParams.get('size') || '10')
    const search = url.searchParams.get('search') || ''

    let query = 'SELECT * FROM movies'
    let params = []

    if (search) {
      query += ' WHERE title LIKE ?'
      params.push(`%${search}%`)
    }

    query += ' ORDER BY average_rating DESC LIMIT ? OFFSET ?'
    params.push(size, page * size)

    const { results } = await env.DB.prepare(query).bind(...params).all()

    return new Response(JSON.stringify({
      success: true,
      movies: results,
      currentPage: page,
      totalPages: Math.ceil(results.length / size),
      totalElements: results.length,
      size: size
    }), {
      headers: {
        'Content-Type': 'application/json',
        ...corsHeaders
      }
    })

  } catch (error) {
    console.error('获取电影列表失败:', error)
    return new Response(JSON.stringify({
      success: false,
      message: '获取电影列表失败',
      error: error.message
    }), {
      status: 500,
      headers: {
        'Content-Type': 'application/json',
        ...corsHeaders
      }
    })
  }
})

// 获取热门电影
router.get('/popular', async (request, env) => {
  try {
    const url = new URL(request.url)
    const page = parseInt(url.searchParams.get('page') || '0')
    const size = parseInt(url.searchParams.get('size') || '10')

    const { results } = await env.DB.prepare(
      'SELECT * FROM movies ORDER BY collection_count DESC, average_rating DESC LIMIT ? OFFSET ?'
    ).bind(size, page * size).all()

    return new Response(JSON.stringify({
      success: true,
      movies: results,
      currentPage: page,
      totalPages: 1,
      totalElements: results.length,
      size: size
    }), {
      headers: {
        'Content-Type': 'application/json',
        ...corsHeaders
      }
    })

  } catch (error) {
    console.error('获取热门电影失败:', error)
    return new Response(JSON.stringify({
      success: false,
      message: '获取热门电影失败',
      error: error.message
    }), {
      status: 500,
      headers: {
        'Content-Type': 'application/json',
        ...corsHeaders
      }
    })
  }
})

// 获取高评分电影
router.get('/top-rated', async (request, env) => {
  try {
    const url = new URL(request.url)
    const page = parseInt(url.searchParams.get('page') || '0')
    const size = parseInt(url.searchParams.get('size') || '10')
    const minRating = parseFloat(url.searchParams.get('minRating') || '8.0')

    const { results } = await env.DB.prepare(
      'SELECT * FROM movies WHERE average_rating >= ? ORDER BY average_rating DESC LIMIT ? OFFSET ?'
    ).bind(minRating, size, page * size).all()

    return new Response(JSON.stringify({
      success: true,
      movies: results,
      currentPage: page,
      totalPages: 1,
      totalElements: results.length,
      size: size
    }), {
      headers: {
        'Content-Type': 'application/json',
        ...corsHeaders
      }
    })

  } catch (error) {
    console.error('获取高评分电影失败:', error)
    return new Response(JSON.stringify({
      success: false,
      message: '获取高评分电影失败',
      error: error.message
    }), {
      status: 500,
      headers: {
        'Content-Type': 'application/json',
        ...corsHeaders
      }
    })
  }
})

// 获取最新电影
router.get('/latest', async (request, env) => {
  try {
    const url = new URL(request.url)
    const page = parseInt(url.searchParams.get('page') || '0')
    const size = parseInt(url.searchParams.get('size') || '10')

    const { results } = await env.DB.prepare(
      'SELECT * FROM movies ORDER BY created_at DESC LIMIT ? OFFSET ?'
    ).bind(size, page * size).all()

    return new Response(JSON.stringify({
      success: true,
      movies: results,
      currentPage: page,
      totalPages: 1,
      totalElements: results.length,
      size: size
    }), {
      headers: {
        'Content-Type': 'application/json',
        ...corsHeaders
      }
    })

  } catch (error) {
    console.error('获取最新电影失败:', error)
    return new Response(JSON.stringify({
      success: false,
      message: '获取最新电影失败',
      error: error.message
    }), {
      status: 500,
      headers: {
        'Content-Type': 'application/json',
        ...corsHeaders
      }
    })
  }
})

// 获取单个电影
router.get('/:id', async (request, env) => {
  try {
    const { id } = request.params
    
    const { results } = await env.DB.prepare(
      'SELECT * FROM movies WHERE id = ?'
    ).bind(id).all()

    if (results.length === 0) {
      return new Response(JSON.stringify({
        success: false,
        message: '电影不存在'
      }), {
        status: 404,
        headers: {
          'Content-Type': 'application/json',
          ...corsHeaders
        }
      })
    }

    return new Response(JSON.stringify({
      success: true,
      movie: results[0]
    }), {
      headers: {
        'Content-Type': 'application/json',
        ...corsHeaders
      }
    })

  } catch (error) {
    console.error('获取电影详情失败:', error)
    return new Response(JSON.stringify({
      success: false,
      message: '获取电影详情失败'
    }), {
      status: 500,
      headers: {
        'Content-Type': 'application/json',
        ...corsHeaders
      }
    })
  }
})

export { router as movieRoutes }

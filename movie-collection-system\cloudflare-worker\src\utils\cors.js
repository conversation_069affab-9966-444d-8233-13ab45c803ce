/**
 * CORS工具函数
 */

export const corsHeaders = {
  'Access-Control-Allow-Origin': '*', // 允许所有域名，生产环境可以限制为特定域名
  'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
  'Access-Control-Allow-Headers': 'Content-Type, Authorization',
  'Access-Control-Max-Age': '86400',
}

export function handleCORS() {
  return new Response(null, {
    status: 200,
    headers: corsHeaders
  })
}

export function addCORSHeaders(response) {
  Object.entries(corsHeaders).forEach(([key, value]) => {
    response.headers.set(key, value)
  })
  return response
}

name = "movie-collection-api"
main = "src/index.js"
compatibility_date = "2023-12-01"

# 顶级数据库配置（用于迁移）
[[d1_databases]]
binding = "DB"
database_name = "movie-collection-db"
database_id = "9277478c-edb8-424c-8a5e-2454126808eb"

# 环境变量
[env.production.vars]
ENVIRONMENT = "production"
CORS_ORIGIN = "https://nantingyouyu.dpdns.org"

# D1 数据库绑定
[[env.production.d1_databases]]
binding = "DB"
database_name = "movie-collection-db"
database_id = "9277478c-edb8-424c-8a5e-2454126808eb"

# KV存储绑定（用于缓存）
[[env.production.kv_namespaces]]
binding = "CACHE"
id = "97c6dbdbb19f428198075abfc1c35348"

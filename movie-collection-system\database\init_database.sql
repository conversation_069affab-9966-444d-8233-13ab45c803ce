-- 电影收藏管理系统数据库初始化脚本
-- 在您的 nantingyouyu 数据库中运行此脚本

USE nantingyouyu;

-- 删除已存在的表（如果需要重新创建）
-- DROP TABLE IF EXISTS watch_history;
-- DROP TABLE IF EXISTS movie_genres;
-- DROP TABLE IF EXISTS comments;
-- DROP TABLE IF EXISTS ratings;
-- DROP TABLE IF EXISTS collections;
-- DROP TABLE IF EXISTS movies;
-- DROP TABLE IF EXISTS genres;
-- DROP TABLE IF EXISTS users;

-- 1. 用户表
CREATE TABLE IF NOT EXISTS users (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) NOT NULL UNIQUE COMMENT '用户名',
    email VARCHAR(100) NOT NULL UNIQUE COMMENT '邮箱',
    password VARCHAR(255) NOT NULL COMMENT '密码(加密)',
    nickname VARCHAR(50) COMMENT '昵称',
    avatar VARCHAR(255) COMMENT '头像URL',
    bio TEXT COMMENT '个人简介',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否激活',
    INDEX idx_username (username),
    INDEX idx_email (email)
) COMMENT '用户信息表';

-- 2. 电影类型表
CREATE TABLE IF NOT EXISTS genres (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(50) NOT NULL UNIQUE COMMENT '类型名称',
    name_en VARCHAR(50) COMMENT '英文名称',
    description TEXT COMMENT '类型描述',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间'
) COMMENT '电影类型表';

-- 3. 电影表
CREATE TABLE IF NOT EXISTS movies (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(255) NOT NULL COMMENT '电影标题',
    original_title VARCHAR(255) COMMENT '原始标题',
    overview TEXT COMMENT '电影简介',
    poster_path VARCHAR(255) COMMENT '海报图片路径',
    backdrop_path VARCHAR(255) COMMENT '背景图片路径',
    release_date DATE COMMENT '上映日期',
    runtime INT COMMENT '时长(分钟)',
    genres VARCHAR(255) COMMENT '类型(JSON格式)',
    director VARCHAR(100) COMMENT '导演',
    cast TEXT COMMENT '演员(JSON格式)',
    country VARCHAR(100) COMMENT '制片国家',
    language VARCHAR(50) COMMENT '语言',
    imdb_id VARCHAR(20) COMMENT 'IMDB ID',
    tmdb_id INT COMMENT 'TMDB ID',
    average_rating DECIMAL(3,1) DEFAULT 0.0 COMMENT '平均评分',
    rating_count INT DEFAULT 0 COMMENT '评分人数',
    collection_count INT DEFAULT 0 COMMENT '收藏人数',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_title (title),
    INDEX idx_release_date (release_date),
    INDEX idx_average_rating (average_rating),
    INDEX idx_tmdb_id (tmdb_id)
) COMMENT '电影信息表';

-- 4. 收藏表
CREATE TABLE IF NOT EXISTS collections (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT NOT NULL COMMENT '用户ID',
    movie_id BIGINT NOT NULL COMMENT '电影ID',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '收藏时间',
    notes TEXT COMMENT '收藏备注',
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (movie_id) REFERENCES movies(id) ON DELETE CASCADE,
    UNIQUE KEY uk_user_movie (user_id, movie_id),
    INDEX idx_user_id (user_id),
    INDEX idx_movie_id (movie_id),
    INDEX idx_created_at (created_at)
) COMMENT '用户收藏表';

-- 5. 评分表
CREATE TABLE IF NOT EXISTS ratings (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT NOT NULL COMMENT '用户ID',
    movie_id BIGINT NOT NULL COMMENT '电影ID',
    rating DECIMAL(2,1) NOT NULL COMMENT '评分(1.0-10.0)',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '评分时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (movie_id) REFERENCES movies(id) ON DELETE CASCADE,
    UNIQUE KEY uk_user_movie (user_id, movie_id),
    INDEX idx_user_id (user_id),
    INDEX idx_movie_id (movie_id),
    INDEX idx_rating (rating),
    CHECK (rating >= 1.0 AND rating <= 10.0)
) COMMENT '用户评分表';

-- 6. 评论表
CREATE TABLE IF NOT EXISTS comments (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT NOT NULL COMMENT '用户ID',
    movie_id BIGINT NOT NULL COMMENT '电影ID',
    content TEXT NOT NULL COMMENT '评论内容',
    parent_id BIGINT COMMENT '父评论ID(用于回复)',
    like_count INT DEFAULT 0 COMMENT '点赞数',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    is_deleted BOOLEAN DEFAULT FALSE COMMENT '是否删除',
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (movie_id) REFERENCES movies(id) ON DELETE CASCADE,
    FOREIGN KEY (parent_id) REFERENCES comments(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_movie_id (movie_id),
    INDEX idx_parent_id (parent_id),
    INDEX idx_created_at (created_at)
) COMMENT '用户评论表';

-- 7. 电影类型关联表
CREATE TABLE IF NOT EXISTS movie_genres (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    movie_id BIGINT NOT NULL COMMENT '电影ID',
    genre_id INT NOT NULL COMMENT '类型ID',
    FOREIGN KEY (movie_id) REFERENCES movies(id) ON DELETE CASCADE,
    FOREIGN KEY (genre_id) REFERENCES genres(id) ON DELETE CASCADE,
    UNIQUE KEY uk_movie_genre (movie_id, genre_id),
    INDEX idx_movie_id (movie_id),
    INDEX idx_genre_id (genre_id)
) COMMENT '电影类型关联表';

-- 8. 用户观看历史表
CREATE TABLE IF NOT EXISTS watch_history (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT NOT NULL COMMENT '用户ID',
    movie_id BIGINT NOT NULL COMMENT '电影ID',
    watched_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '观看时间',
    watch_count INT DEFAULT 1 COMMENT '观看次数',
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (movie_id) REFERENCES movies(id) ON DELETE CASCADE,
    UNIQUE KEY uk_user_movie (user_id, movie_id),
    INDEX idx_user_id (user_id),
    INDEX idx_movie_id (movie_id),
    INDEX idx_watched_at (watched_at)
) COMMENT '用户观看历史表';

-- 插入电影类型数据
INSERT IGNORE INTO genres (name, name_en, description) VALUES
('动作', 'Action', '以动作场面为主的电影类型'),
('冒险', 'Adventure', '以冒险故事为主题的电影'),
('动画', 'Animation', '动画制作的电影'),
('喜剧', 'Comedy', '以幽默搞笑为主的电影'),
('犯罪', 'Crime', '以犯罪故事为主题的电影'),
('纪录片', 'Documentary', '记录真实事件的电影'),
('剧情', 'Drama', '以剧情发展为主的电影'),
('家庭', 'Family', '适合全家观看的电影'),
('奇幻', 'Fantasy', '包含奇幻元素的电影'),
('历史', 'History', '以历史事件为背景的电影'),
('恐怖', 'Horror', '以恐怖元素为主的电影'),
('音乐', 'Music', '以音乐为主题的电影'),
('悬疑', 'Mystery', '以悬疑推理为主的电影'),
('爱情', 'Romance', '以爱情故事为主的电影'),
('科幻', 'Science Fiction', '包含科幻元素的电影'),
('电视电影', 'TV Movie', '为电视制作的电影'),
('惊悚', 'Thriller', '以惊悚情节为主的电影'),
('战争', 'War', '以战争为背景的电影'),
('西部', 'Western', '以美国西部为背景的电影');

SELECT '数据库表创建完成！' AS message;

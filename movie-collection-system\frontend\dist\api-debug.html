<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API调试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .result {
            margin-top: 10px;
            padding: 10px;
            border-radius: 4px;
            white-space: pre-wrap;
            font-family: monospace;
        }
        .success { background-color: #d4edda; color: #155724; }
        .error { background-color: #f8d7da; color: #721c24; }
        .loading { background-color: #fff3cd; color: #856404; }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background-color: #0056b3; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎬 电影收藏系统 API 调试</h1>
        
        <div class="test-section">
            <h3>1. API健康检查</h3>
            <button onclick="testHealth()">测试健康检查</button>
            <div id="health-result" class="result"></div>
        </div>

        <div class="test-section">
            <h3>2. 获取电影列表</h3>
            <button onclick="testMovies()">获取电影数据</button>
            <div id="movies-result" class="result"></div>
        </div>

        <div class="test-section">
            <h3>3. CORS测试</h3>
            <button onclick="testCORS()">测试跨域请求</button>
            <div id="cors-result" class="result"></div>
        </div>

        <div class="test-section">
            <h3>4. 环境信息</h3>
            <div id="env-info" class="result">
                当前域名: <span id="current-domain"></span><br>
                API地址: https://movie-collection-api.nantingyouyu.workers.dev/api<br>
                时间: <span id="current-time"></span>
            </div>
        </div>
    </div>

    <script>
        const API_BASE = 'https://movie-collection-api.nantingyouyu.workers.dev/api';
        
        // 显示环境信息
        document.getElementById('current-domain').textContent = window.location.hostname;
        document.getElementById('current-time').textContent = new Date().toLocaleString();

        function showResult(elementId, type, message) {
            const element = document.getElementById(elementId);
            element.className = `result ${type}`;
            element.textContent = message;
        }

        async function testHealth() {
            showResult('health-result', 'loading', '正在测试健康检查...');
            try {
                const response = await fetch(`${API_BASE}/health`);
                const data = await response.json();
                showResult('health-result', 'success', 
                    `✅ 健康检查成功\n状态: ${data.status}\n时间: ${data.timestamp}\n版本: ${data.version}`);
            } catch (error) {
                showResult('health-result', 'error', `❌ 健康检查失败: ${error.message}`);
            }
        }

        async function testMovies() {
            showResult('movies-result', 'loading', '正在获取电影数据...');
            try {
                const response = await fetch(`${API_BASE}/movies`);
                const data = await response.json();
                
                if (data.success) {
                    showResult('movies-result', 'success', 
                        `✅ 电影数据获取成功\n电影数量: ${data.movies.length}\n电影列表:\n${data.movies.map(m => `- ${m.title} (${m.average_rating}分)`).join('\n')}`);
                } else {
                    showResult('movies-result', 'error', `❌ API返回错误: ${data.message}`);
                }
            } catch (error) {
                showResult('movies-result', 'error', `❌ 请求失败: ${error.message}`);
            }
        }

        async function testCORS() {
            showResult('cors-result', 'loading', '正在测试CORS...');
            try {
                const response = await fetch(`${API_BASE}/health`, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });
                
                const corsHeaders = {
                    'Access-Control-Allow-Origin': response.headers.get('Access-Control-Allow-Origin'),
                    'Access-Control-Allow-Methods': response.headers.get('Access-Control-Allow-Methods'),
                    'Access-Control-Allow-Headers': response.headers.get('Access-Control-Allow-Headers')
                };
                
                showResult('cors-result', 'success', 
                    `✅ CORS配置正常\nAllow-Origin: ${corsHeaders['Access-Control-Allow-Origin']}\nAllow-Methods: ${corsHeaders['Access-Control-Allow-Methods']}\nAllow-Headers: ${corsHeaders['Access-Control-Allow-Headers']}`);
            } catch (error) {
                showResult('cors-result', 'error', `❌ CORS测试失败: ${error.message}`);
            }
        }

        // 页面加载时自动运行测试
        window.onload = function() {
            setTimeout(testHealth, 500);
            setTimeout(testMovies, 1000);
            setTimeout(testCORS, 1500);
        };
    </script>
</body>
</html>

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .test-button {
            background: #409eff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background: #337ecc;
        }
        .result {
            margin-top: 10px;
            padding: 10px;
            border-radius: 4px;
            white-space: pre-wrap;
            font-family: monospace;
        }
        .success {
            background: #f0f9ff;
            border: 1px solid #67c23a;
            color: #67c23a;
        }
        .error {
            background: #fef0f0;
            border: 1px solid #f56c6c;
            color: #f56c6c;
        }
        .loading {
            background: #fdf6ec;
            border: 1px solid #e6a23c;
            color: #e6a23c;
        }
    </style>
</head>
<body>
    <h1>电影收藏系统 API 测试</h1>
    
    <div class="test-section">
        <h3>健康检查</h3>
        <button class="test-button" onclick="testHealth()">测试健康检查</button>
        <div id="health-result" class="result"></div>
    </div>
    
    <div class="test-section">
        <h3>热门电影</h3>
        <button class="test-button" onclick="testPopularMovies()">获取热门电影</button>
        <div id="popular-result" class="result"></div>
    </div>
    
    <div class="test-section">
        <h3>所有电影</h3>
        <button class="test-button" onclick="testAllMovies()">获取所有电影</button>
        <div id="all-result" class="result"></div>
    </div>
    
    <div class="test-section">
        <h3>数据库测试</h3>
        <button class="test-button" onclick="testDatabase()">测试数据库连接</button>
        <div id="db-result" class="result"></div>
    </div>

    <div class="test-section">
        <h3>海报更新</h3>
        <button class="test-button" onclick="updatePosters()">更新电影海报</button>
        <div id="poster-result" class="result"></div>
    </div>

    <script>
        const API_BASE = 'http://localhost:8081/api';
        
        function showLoading(elementId) {
            const element = document.getElementById(elementId);
            element.className = 'result loading';
            element.textContent = '正在请求...';
        }
        
        function showSuccess(elementId, data) {
            const element = document.getElementById(elementId);
            element.className = 'result success';
            element.textContent = '✅ 请求成功\n' + JSON.stringify(data, null, 2);
        }
        
        function showError(elementId, error) {
            const element = document.getElementById(elementId);
            element.className = 'result error';
            element.textContent = '❌ 请求失败\n' + error.message;
        }
        
        async function testHealth() {
            showLoading('health-result');
            try {
                const response = await fetch(`${API_BASE}/test/health`);
                const data = await response.json();
                showSuccess('health-result', data);
            } catch (error) {
                showError('health-result', error);
            }
        }
        
        async function testPopularMovies() {
            showLoading('popular-result');
            try {
                const response = await fetch(`${API_BASE}/movies/popular?page=0&size=5`);
                const data = await response.json();
                showSuccess('popular-result', data);
            } catch (error) {
                showError('popular-result', error);
            }
        }
        
        async function testAllMovies() {
            showLoading('all-result');
            try {
                const response = await fetch(`${API_BASE}/movies?page=0&size=5`);
                const data = await response.json();
                showSuccess('all-result', data);
            } catch (error) {
                showError('all-result', error);
            }
        }
        
        async function testDatabase() {
            showLoading('db-result');
            try {
                const response = await fetch(`${API_BASE}/test/db-connection`);
                const data = await response.json();
                showSuccess('db-result', data);
            } catch (error) {
                showError('db-result', error);
            }
        }

        async function updatePosters() {
            showLoading('poster-result');
            try {
                const response = await fetch(`${API_BASE}/test/update-posters`, {
                    method: 'POST'
                });
                const data = await response.json();
                showSuccess('poster-result', data);
            } catch (error) {
                showError('poster-result', error);
            }
        }
    </script>
</body>
</html>

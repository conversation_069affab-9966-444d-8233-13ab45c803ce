<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Vue数据调试</title>
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <script src="https://unpkg.com/axios/dist/axios.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        .debug-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .movie-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        .movie-card {
            border: 1px solid #eee;
            border-radius: 8px;
            overflow: hidden;
        }
        .movie-poster {
            position: relative;
            aspect-ratio: 2/3;
        }
        .movie-poster img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        .movie-info {
            padding: 10px;
            text-align: center;
        }
        .movie-title {
            font-weight: bold;
            margin-bottom: 5px;
        }
        .movie-year {
            color: #666;
            font-size: 14px;
        }
        .movie-rating {
            background: rgba(0, 0, 0, 0.7);
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            position: absolute;
            top: 8px;
            right: 8px;
        }
        .debug-info {
            background: #f5f5f5;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            white-space: pre-wrap;
            margin-top: 10px;
        }
        .test-button {
            background: #409eff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background: #337ecc;
        }
    </style>
</head>
<body>
    <div id="app">
        <h1>Vue数据调试页面</h1>
        
        <div class="debug-section">
            <h3>API数据测试</h3>
            <button class="test-button" @click="loadMovies">加载热门电影</button>
            <button class="test-button" @click="clearData">清空数据</button>
            
            <div class="debug-info" v-if="rawData">
                原始API数据：
                {{ JSON.stringify(rawData, null, 2) }}
            </div>
            
            <div class="debug-info" v-if="formattedMovies.length > 0">
                格式化后的数据：
                {{ JSON.stringify(formattedMovies, null, 2) }}
            </div>
        </div>
        
        <div class="debug-section" v-if="formattedMovies.length > 0">
            <h3>电影展示</h3>
            <div class="movie-grid">
                <div class="movie-card" v-for="movie in formattedMovies" :key="movie.id">
                    <div class="movie-poster">
                        <img :src="movie.poster" :alt="movie.title" 
                             @load="onImageLoad(movie.title)"
                             @error="onImageError(movie.title, $event)">
                        <div class="movie-rating">{{ movie.rating }}</div>
                    </div>
                    <div class="movie-info">
                        <div class="movie-title">{{ movie.title }}</div>
                        <div class="movie-year">{{ movie.year }}</div>
                        <div style="font-size: 12px; color: #999; margin-top: 5px;">
                            海报路径: {{ movie.poster }}
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="debug-section">
            <h3>图片加载日志</h3>
            <div class="debug-info">{{ imageLog }}</div>
        </div>
    </div>

    <script>
        const { createApp } = Vue;
        
        createApp({
            data() {
                return {
                    rawData: null,
                    movies: [],
                    imageLog: '等待加载图片...\n'
                }
            },
            computed: {
                formattedMovies() {
                    return this.movies.map(movie => ({
                        id: movie.id,
                        title: movie.title,
                        year: movie.releaseDate ? new Date(movie.releaseDate).getFullYear() : '未知',
                        rating: movie.averageRating || 0,
                        poster: movie.posterPath || '/placeholder.jpg'
                    }))
                }
            },
            methods: {
                async loadMovies() {
                    try {
                        this.imageLog = '开始加载电影数据...\n';
                        
                        const response = await axios.get('http://localhost:8081/api/movies/popular?page=0&size=6');
                        
                        this.rawData = response.data;
                        this.movies = response.data.movies || [];
                        
                        this.imageLog += `API请求成功，获取到 ${this.movies.length} 部电影\n`;
                        this.movies.forEach(movie => {
                            this.imageLog += `${movie.title}: ${movie.posterPath || '无海报'}\n`;
                        });
                        
                    } catch (error) {
                        this.imageLog += `API请求失败: ${error.message}\n`;
                        console.error('加载电影失败:', error);
                    }
                },
                
                clearData() {
                    this.rawData = null;
                    this.movies = [];
                    this.imageLog = '数据已清空\n';
                },
                
                onImageLoad(title) {
                    this.imageLog += `✅ ${title} 图片加载成功\n`;
                    console.log(`${title} 图片加载成功`);
                },
                
                onImageError(title, event) {
                    this.imageLog += `❌ ${title} 图片加载失败: ${event.target.src}\n`;
                    console.error(`${title} 图片加载失败:`, event.target.src);
                    // 设置备用图片
                    event.target.src = 'https://via.placeholder.com/300x450/cccccc/666666?text=加载失败';
                }
            }
        }).mount('#app');
    </script>
</body>
</html>

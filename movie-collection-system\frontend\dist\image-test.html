<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>图片加载测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .image-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        .image-card {
            border: 1px solid #eee;
            border-radius: 8px;
            overflow: hidden;
        }
        .image-card img {
            width: 100%;
            height: 300px;
            object-fit: cover;
        }
        .image-info {
            padding: 10px;
            text-align: center;
        }
        .test-button {
            background: #409eff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background: #337ecc;
        }
        .result {
            margin-top: 10px;
            padding: 10px;
            border-radius: 4px;
            white-space: pre-wrap;
            font-family: monospace;
        }
        .success {
            background: #f0f9ff;
            border: 1px solid #67c23a;
            color: #67c23a;
        }
        .error {
            background: #fef0f0;
            border: 1px solid #f56c6c;
            color: #f56c6c;
        }
    </style>
</head>
<body>
    <h1>电影海报图片加载测试</h1>
    
    <div class="test-section">
        <h3>API数据测试</h3>
        <button class="test-button" onclick="loadMovieData()">加载电影数据</button>
        <div id="api-result" class="result"></div>
    </div>
    
    <div class="test-section">
        <h3>直接图片测试</h3>
        <div class="image-grid">
            <div class="image-card">
                <img src="https://img1.doubanio.com/view/photo/s_ratio_poster/public/p480747492.jpg" 
                     alt="肖申克的救赎" 
                     onload="console.log('肖申克的救赎 图片加载成功')"
                     onerror="console.log('肖申克的救赎 图片加载失败')">
                <div class="image-info">肖申克的救赎</div>
            </div>
            <div class="image-card">
                <img src="https://img1.doubanio.com/view/photo/s_ratio_poster/public/p2561716440.jpg" 
                     alt="黑暗骑士" 
                     onload="console.log('黑暗骑士 图片加载成功')"
                     onerror="console.log('黑暗骑士 图片加载失败')">
                <div class="image-info">黑暗骑士</div>
            </div>
            <div class="image-card">
                <img src="https://img1.doubanio.com/view/photo/s_ratio_poster/public/p510876377.jpg" 
                     alt="阿甘正传" 
                     onload="console.log('阿甘正传 图片加载成功')"
                     onerror="console.log('阿甘正传 图片加载失败')">
                <div class="image-info">阿甘正传</div>
            </div>
        </div>
    </div>
    
    <div class="test-section">
        <h3>动态加载测试</h3>
        <button class="test-button" onclick="loadDynamicImages()">动态加载图片</button>
        <div id="dynamic-images" class="image-grid"></div>
    </div>

    <script>
        const API_BASE = 'http://localhost:8081/api';
        
        async function loadMovieData() {
            const resultDiv = document.getElementById('api-result');
            resultDiv.className = 'result';
            resultDiv.textContent = '正在加载电影数据...';
            
            try {
                const response = await fetch(`${API_BASE}/movies/popular?page=0&size=6`);
                const data = await response.json();
                
                if (data.success) {
                    resultDiv.className = 'result success';
                    resultDiv.textContent = '✅ API数据加载成功\n' + 
                        `共 ${data.movies.length} 部电影\n` +
                        data.movies.map(movie => 
                            `${movie.title}: ${movie.posterPath || '无海报'}`
                        ).join('\n');
                    
                    // 显示动态图片
                    loadDynamicImages(data.movies);
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = '❌ API请求失败: ' + data.message;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = '❌ 请求失败: ' + error.message;
            }
        }
        
        function loadDynamicImages(movies) {
            const container = document.getElementById('dynamic-images');
            container.innerHTML = '';
            
            if (!movies) {
                // 使用默认测试数据
                movies = [
                    { title: '肖申克的救赎', posterPath: 'https://img1.doubanio.com/view/photo/s_ratio_poster/public/p480747492.jpg' },
                    { title: '黑暗骑士', posterPath: 'https://img1.doubanio.com/view/photo/s_ratio_poster/public/p2561716440.jpg' },
                    { title: '阿甘正传', posterPath: 'https://img1.doubanio.com/view/photo/s_ratio_poster/public/p510876377.jpg' }
                ];
            }
            
            movies.forEach(movie => {
                if (movie.posterPath) {
                    const card = document.createElement('div');
                    card.className = 'image-card';
                    
                    const img = document.createElement('img');
                    img.src = movie.posterPath;
                    img.alt = movie.title;
                    img.onload = () => console.log(`${movie.title} 动态图片加载成功`);
                    img.onerror = () => {
                        console.log(`${movie.title} 动态图片加载失败`);
                        img.src = 'https://via.placeholder.com/300x450/cccccc/666666?text=加载失败';
                    };
                    
                    const info = document.createElement('div');
                    info.className = 'image-info';
                    info.textContent = movie.title;
                    
                    card.appendChild(img);
                    card.appendChild(info);
                    container.appendChild(card);
                }
            });
        }
    </script>
</body>
</html>

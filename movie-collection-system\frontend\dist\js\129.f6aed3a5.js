"use strict";(self["webpackChunkmovie_collection_frontend"]=self["webpackChunkmovie_collection_frontend"]||[]).push([[129],{129:function(e,t,i){i.r(t),i.d(t,{default:function(){return ze}});var a=i(641),s=i(33);const o={class:"movie-detail-page"},l={key:0,class:"container"},n={class:"back-button"},r={class:"movie-hero"},c={class:"movie-main-info"},d={class:"movie-poster"},g=["src","alt"],m={class:"movie-details"},u={class:"movie-title"},h={key:0,class:"movie-original-title"},v={class:"movie-meta"},k={class:"release-year"},p={key:0,class:"runtime"},L={class:"country"},C={class:"language"},R={class:"movie-rating"},y={class:"rating-score"},f={class:"score"},b={class:"rating-count"},_={class:"movie-actions"},w={class:"movie-content"},F={class:"content-main"},D={class:"section overview-section"},E={class:"overview-text"},I={key:0,class:"section cast-section"},M={class:"cast-info"},X={key:0,class:"director-info"},$={key:1,class:"cast-info"},S={class:"section rating-section"},U={class:"content-sidebar"},V={class:"info-card"},Q={class:"info-list"},W={key:0,class:"info-item"},x={key:1,class:"info-item"},P={key:2,class:"info-item"},z={key:3,class:"info-item"},T={key:4,class:"info-item"},O=["href"],A={class:"stats-card"},B={class:"stats-list"},N={class:"stat-item"},Y={class:"stat-number"},j={class:"stat-item"},K={class:"stat-number"},q={key:0,class:"recommendations-card"},G={class:"recommendations-list"},H=["onClick"],J=["src","alt"],Z={class:"rec-info"},ee={class:"rec-rating"},te={key:1,class:"empty-state"},ie={class:"rating-dialog"},ae={class:"movie-info"},se=["src","alt"],oe={class:"rating-form"},le={class:"score-input"},ne={class:"comment-input"};function re(e,t,i,re,ce,de){const ge=(0,a.g2)("ArrowLeft"),me=(0,a.g2)("el-icon"),ue=(0,a.g2)("el-button"),he=(0,a.g2)("Star"),ve=(0,a.g2)("StarFilled"),ke=(0,a.g2)("ChatDotRound"),pe=(0,a.g2)("Share"),Le=(0,a.g2)("MovieRating"),Ce=(0,a.g2)("VideoCamera"),Re=(0,a.g2)("el-rate"),ye=(0,a.g2)("el-input"),fe=(0,a.g2)("el-dialog"),be=(0,a.gN)("loading");return(0,a.bo)(((0,a.uX)(),(0,a.CE)("div",o,[ce.movie?((0,a.uX)(),(0,a.CE)("div",l,[(0,a.Lk)("div",n,[(0,a.bF)(ue,{onClick:t[0]||(t[0]=t=>e.$router.go(-1)),type:"info",plain:""},{default:(0,a.k6)(()=>[(0,a.bF)(me,null,{default:(0,a.k6)(()=>[(0,a.bF)(ge)]),_:1}),t[7]||(t[7]=(0,a.eW)(" 返回 "))]),_:1,__:[7]})]),(0,a.Lk)("div",r,[(0,a.Lk)("div",{class:"movie-backdrop",style:(0,s.Tr)({backgroundImage:`url(${ce.movie.backdropPath||ce.movie.posterPath})`})},t[8]||(t[8]=[(0,a.Lk)("div",{class:"backdrop-overlay"},null,-1)]),4),(0,a.Lk)("div",c,[(0,a.Lk)("div",d,[(0,a.Lk)("img",{src:ce.movie.posterPath||"/placeholder.jpg",alt:ce.movie.title},null,8,g)]),(0,a.Lk)("div",m,[(0,a.Lk)("h1",u,(0,s.v_)(ce.movie.title),1),ce.movie.originalTitle&&ce.movie.originalTitle!==ce.movie.title?((0,a.uX)(),(0,a.CE)("p",h,(0,s.v_)(ce.movie.originalTitle),1)):(0,a.Q3)("",!0),(0,a.Lk)("div",v,[(0,a.Lk)("span",k,(0,s.v_)(de.releaseYear),1),ce.movie.runtime?((0,a.uX)(),(0,a.CE)("span",p,(0,s.v_)(ce.movie.runtime)+"分钟",1)):(0,a.Q3)("",!0),(0,a.Lk)("span",L,(0,s.v_)(ce.movie.country),1),(0,a.Lk)("span",C,(0,s.v_)(ce.movie.language),1)]),(0,a.Lk)("div",R,[(0,a.Lk)("div",y,[(0,a.bF)(me,{class:"star-icon"},{default:(0,a.k6)(()=>[(0,a.bF)(he)]),_:1}),(0,a.Lk)("span",f,(0,s.v_)(ce.movie.averageRating||0),1),t[9]||(t[9]=(0,a.Lk)("span",{class:"max-score"},"/10",-1))]),(0,a.Lk)("div",b,(0,s.v_)(ce.movie.ratingCount||0)+"人评分 ",1)]),(0,a.Lk)("div",_,[e.isLoggedIn?((0,a.uX)(),(0,a.Wv)(ue,{key:0,type:"primary",size:"large",onClick:de.toggleCollection,loading:ce.collectionLoading},{default:(0,a.k6)(()=>[(0,a.bF)(me,null,{default:(0,a.k6)(()=>[ce.isCollected?((0,a.uX)(),(0,a.Wv)(ve,{key:1})):((0,a.uX)(),(0,a.Wv)(he,{key:0}))]),_:1}),(0,a.eW)(" "+(0,s.v_)(ce.isCollected?"已收藏":"收藏"),1)]),_:1},8,["onClick","loading"])):(0,a.Q3)("",!0),e.isLoggedIn?((0,a.uX)(),(0,a.Wv)(ue,{key:1,type:"success",size:"large",onClick:t[1]||(t[1]=e=>ce.showRatingDialog=!0)},{default:(0,a.k6)(()=>[(0,a.bF)(me,null,{default:(0,a.k6)(()=>[(0,a.bF)(ke)]),_:1}),(0,a.eW)(" "+(0,s.v_)(ce.userRating?"修改评分":"评分"),1)]),_:1})):(0,a.Q3)("",!0),(0,a.bF)(ue,{size:"large",onClick:de.shareMovie},{default:(0,a.k6)(()=>[(0,a.bF)(me,null,{default:(0,a.k6)(()=>[(0,a.bF)(pe)]),_:1}),t[10]||(t[10]=(0,a.eW)(" 分享 "))]),_:1,__:[10]},8,["onClick"])])])])]),(0,a.Lk)("div",w,[(0,a.Lk)("div",F,[(0,a.Lk)("div",D,[t[11]||(t[11]=(0,a.Lk)("h3",null,"剧情简介",-1)),(0,a.Lk)("p",E,(0,s.v_)(ce.movie.overview||"暂无剧情简介"),1)]),ce.movie.director||ce.movie.cast?((0,a.uX)(),(0,a.CE)("div",I,[t[14]||(t[14]=(0,a.Lk)("h3",null,"演职员表",-1)),(0,a.Lk)("div",M,[ce.movie.director?((0,a.uX)(),(0,a.CE)("div",X,[t[12]||(t[12]=(0,a.Lk)("label",null,"导演：",-1)),(0,a.Lk)("span",null,(0,s.v_)(ce.movie.director),1)])):(0,a.Q3)("",!0),ce.movie.cast?((0,a.uX)(),(0,a.CE)("div",$,[t[13]||(t[13]=(0,a.Lk)("label",null,"主演：",-1)),(0,a.Lk)("span",null,(0,s.v_)(ce.movie.cast),1)])):(0,a.Q3)("",!0)])])):(0,a.Q3)("",!0),(0,a.Lk)("div",S,[(0,a.bF)(Le,{"movie-id":de.movieId,"show-other-ratings":!0,onRatingUpdated:de.handleRatingUpdated,onRatingDeleted:de.handleRatingDeleted},null,8,["movie-id","onRatingUpdated","onRatingDeleted"])])]),(0,a.Lk)("div",U,[(0,a.Lk)("div",V,[t[20]||(t[20]=(0,a.Lk)("h4",null,"电影信息",-1)),(0,a.Lk)("div",Q,[ce.movie.releaseDate?((0,a.uX)(),(0,a.CE)("div",W,[t[15]||(t[15]=(0,a.Lk)("label",null,"上映日期：",-1)),(0,a.Lk)("span",null,(0,s.v_)(de.formatDate(ce.movie.releaseDate)),1)])):(0,a.Q3)("",!0),ce.movie.runtime?((0,a.uX)(),(0,a.CE)("div",x,[t[16]||(t[16]=(0,a.Lk)("label",null,"片长：",-1)),(0,a.Lk)("span",null,(0,s.v_)(ce.movie.runtime)+"分钟",1)])):(0,a.Q3)("",!0),ce.movie.country?((0,a.uX)(),(0,a.CE)("div",P,[t[17]||(t[17]=(0,a.Lk)("label",null,"制片国家：",-1)),(0,a.Lk)("span",null,(0,s.v_)(ce.movie.country),1)])):(0,a.Q3)("",!0),ce.movie.language?((0,a.uX)(),(0,a.CE)("div",z,[t[18]||(t[18]=(0,a.Lk)("label",null,"语言：",-1)),(0,a.Lk)("span",null,(0,s.v_)(ce.movie.language),1)])):(0,a.Q3)("",!0),ce.movie.imdbId?((0,a.uX)(),(0,a.CE)("div",T,[t[19]||(t[19]=(0,a.Lk)("label",null,"IMDb：",-1)),(0,a.Lk)("a",{href:`https://www.imdb.com/title/${ce.movie.imdbId}`,target:"_blank"},(0,s.v_)(ce.movie.imdbId),9,O)])):(0,a.Q3)("",!0)])]),(0,a.Lk)("div",A,[t[23]||(t[23]=(0,a.Lk)("h4",null,"统计信息",-1)),(0,a.Lk)("div",B,[(0,a.Lk)("div",N,[(0,a.Lk)("span",Y,(0,s.v_)(ce.movie.ratingCount||0),1),t[21]||(t[21]=(0,a.Lk)("span",{class:"stat-label"},"评分人数",-1))]),(0,a.Lk)("div",j,[(0,a.Lk)("span",K,(0,s.v_)(ce.movie.collectionCount||0),1),t[22]||(t[22]=(0,a.Lk)("span",{class:"stat-label"},"收藏人数",-1))])])]),ce.recommendations.length>0?((0,a.uX)(),(0,a.CE)("div",q,[t[24]||(t[24]=(0,a.Lk)("h4",null,"相关推荐",-1)),(0,a.Lk)("div",G,[((0,a.uX)(!0),(0,a.CE)(a.FK,null,(0,a.pI)(ce.recommendations,e=>((0,a.uX)(),(0,a.CE)("div",{class:"recommendation-item",key:e.id,onClick:t=>de.viewMovie(e.id)},[(0,a.Lk)("img",{src:e.posterPath||"/placeholder.jpg",alt:e.title},null,8,J),(0,a.Lk)("div",Z,[(0,a.Lk)("h5",null,(0,s.v_)(e.title),1),(0,a.Lk)("p",null,(0,s.v_)(e.releaseDate?new Date(e.releaseDate).getFullYear():"未知"),1),(0,a.Lk)("div",ee,[(0,a.bF)(me,null,{default:(0,a.k6)(()=>[(0,a.bF)(he)]),_:1}),(0,a.eW)(" "+(0,s.v_)(e.averageRating||0),1)])])],8,H))),128))])])):(0,a.Q3)("",!0)])])])):ce.loading?(0,a.Q3)("",!0):((0,a.uX)(),(0,a.CE)("div",te,[(0,a.bF)(me,{class:"empty-icon"},{default:(0,a.k6)(()=>[(0,a.bF)(Ce)]),_:1}),t[26]||(t[26]=(0,a.Lk)("h3",null,"电影不存在",-1)),t[27]||(t[27]=(0,a.Lk)("p",null,"抱歉，找不到您要查看的电影",-1)),(0,a.bF)(ue,{type:"primary",onClick:t[2]||(t[2]=t=>e.$router.push("/movies"))},{default:(0,a.k6)(()=>t[25]||(t[25]=[(0,a.eW)(" 浏览其他电影 ")])),_:1,__:[25]})])),(0,a.bF)(fe,{modelValue:ce.showRatingDialog,"onUpdate:modelValue":t[6]||(t[6]=e=>ce.showRatingDialog=e),title:"为电影评分",width:"500px"},{footer:(0,a.k6)(()=>[(0,a.bF)(ue,{onClick:t[5]||(t[5]=e=>ce.showRatingDialog=!1)},{default:(0,a.k6)(()=>t[30]||(t[30]=[(0,a.eW)("取消")])),_:1,__:[30]}),(0,a.bF)(ue,{type:"primary",onClick:de.submitRating,loading:ce.ratingLoading,disabled:0===ce.dialogRating.score},{default:(0,a.k6)(()=>t[31]||(t[31]=[(0,a.eW)(" 提交评分 ")])),_:1,__:[31]},8,["onClick","loading","disabled"])]),default:(0,a.k6)(()=>[(0,a.Lk)("div",ie,[(0,a.Lk)("div",ae,[(0,a.Lk)("img",{src:ce.movie?.posterPath||"/placeholder.jpg",alt:ce.movie?.title},null,8,se),(0,a.Lk)("h4",null,(0,s.v_)(ce.movie?.title),1)]),(0,a.Lk)("div",oe,[(0,a.Lk)("div",le,[t[28]||(t[28]=(0,a.Lk)("label",null,"评分：",-1)),(0,a.bF)(Re,{modelValue:ce.dialogRating.score,"onUpdate:modelValue":t[3]||(t[3]=e=>ce.dialogRating.score=e),max:10,"allow-half":!0,"show-score":"","score-template":"{value}/10"},null,8,["modelValue"])]),(0,a.Lk)("div",ne,[t[29]||(t[29]=(0,a.Lk)("label",null,"评论：",-1)),(0,a.bF)(ye,{modelValue:ce.dialogRating.comment,"onUpdate:modelValue":t[4]||(t[4]=e=>ce.dialogRating.comment=e),type:"textarea",rows:4,placeholder:"分享你的观影感受...",maxlength:"500","show-word-limit":""},null,8,["modelValue"])])])])]),_:1},8,["modelValue"])])),[[be,ce.loading]])}var ce=i(548),de=i(278);const ge={class:"movie-rating"},me={key:0,class:"rating-display"},ue={class:"rating-score"},he={key:0,class:"score-text"},ve={key:0,class:"rating-comment"},ke={key:1,class:"rating-actions"},pe={key:1,class:"action-buttons"},Le={key:1,class:"rating-edit"},Ce={class:"edit-form"},Re={class:"score-input"},ye={class:"comment-input"},fe={class:"edit-actions"},be={key:2,class:"other-ratings"},_e={class:"ratings-list"},we={class:"user-info"},Fe={class:"username"},De={class:"rating-content"},Ee={class:"score"},Ie={class:"score-value"},Me={key:0,class:"comment"},Xe={class:"rating-date"},$e={key:0,class:"load-more"};function Se(e,t,i,o,l,n){const r=(0,a.g2)("el-rate"),c=(0,a.g2)("Star"),d=(0,a.g2)("el-icon"),g=(0,a.g2)("el-button"),m=(0,a.g2)("Edit"),u=(0,a.g2)("Delete"),h=(0,a.g2)("el-input"),v=(0,a.g2)("el-avatar");return(0,a.uX)(),(0,a.CE)("div",ge,[l.isEditing?((0,a.uX)(),(0,a.CE)("div",Le,[(0,a.Lk)("div",Ce,[(0,a.Lk)("div",Re,[t[6]||(t[6]=(0,a.Lk)("label",null,"评分：",-1)),(0,a.bF)(r,{modelValue:l.editScore,"onUpdate:modelValue":t[1]||(t[1]=e=>l.editScore=e),max:10,"allow-half":!0,"show-score":"","score-template":"{value}/10"},null,8,["modelValue"])]),(0,a.Lk)("div",ye,[t[7]||(t[7]=(0,a.Lk)("label",null,"评论：",-1)),(0,a.bF)(h,{modelValue:l.editComment,"onUpdate:modelValue":t[2]||(t[2]=e=>l.editComment=e),type:"textarea",rows:3,placeholder:"分享你的观影感受...",maxlength:"500","show-word-limit":""},null,8,["modelValue"])]),(0,a.Lk)("div",fe,[(0,a.bF)(g,{onClick:n.cancelEdit},{default:(0,a.k6)(()=>t[8]||(t[8]=[(0,a.eW)("取消")])),_:1,__:[8]},8,["onClick"]),(0,a.bF)(g,{type:"primary",onClick:n.handleSubmit,loading:l.submitting,disabled:0===l.editScore},{default:(0,a.k6)(()=>[(0,a.eW)((0,s.v_)(e.currentRating?"更新":"提交"),1)]),_:1},8,["onClick","loading","disabled"])])])])):((0,a.uX)(),(0,a.CE)("div",me,[(0,a.Lk)("div",ue,[(0,a.bF)(r,{modelValue:n.displayScore,"onUpdate:modelValue":t[0]||(t[0]=e=>n.displayScore=e),max:10,"allow-half":!0,disabled:"","show-score":"","score-template":"{value}"},null,8,["modelValue"]),e.currentRating?((0,a.uX)(),(0,a.CE)("span",he,(0,s.v_)(e.currentRating.score)+"/10 ",1)):(0,a.Q3)("",!0)]),e.currentRating&&e.currentRating.comment?((0,a.uX)(),(0,a.CE)("div",ve,[(0,a.Lk)("p",null,(0,s.v_)(e.currentRating.comment),1)])):(0,a.Q3)("",!0),e.isLoggedIn?((0,a.uX)(),(0,a.CE)("div",ke,[e.currentRating?((0,a.uX)(),(0,a.CE)("div",pe,[(0,a.bF)(g,{type:"text",size:"small",onClick:n.startEditing},{default:(0,a.k6)(()=>[(0,a.bF)(d,null,{default:(0,a.k6)(()=>[(0,a.bF)(m)]),_:1}),t[4]||(t[4]=(0,a.eW)(" 编辑 "))]),_:1,__:[4]},8,["onClick"]),(0,a.bF)(g,{type:"text",size:"small",onClick:n.handleDelete,loading:l.deleting},{default:(0,a.k6)(()=>[(0,a.bF)(d,null,{default:(0,a.k6)(()=>[(0,a.bF)(u)]),_:1}),t[5]||(t[5]=(0,a.eW)(" 删除 "))]),_:1,__:[5]},8,["onClick","loading"])])):((0,a.uX)(),(0,a.Wv)(g,{key:0,type:"primary",size:"small",onClick:n.startEditing},{default:(0,a.k6)(()=>[(0,a.bF)(d,null,{default:(0,a.k6)(()=>[(0,a.bF)(c)]),_:1}),t[3]||(t[3]=(0,a.eW)(" 评分 "))]),_:1,__:[3]},8,["onClick"]))])):(0,a.Q3)("",!0)])),i.showOtherRatings&&l.otherRatings.length>0?((0,a.uX)(),(0,a.CE)("div",be,[t[10]||(t[10]=(0,a.Lk)("h4",null,"其他用户评分",-1)),(0,a.Lk)("div",_e,[((0,a.uX)(!0),(0,a.CE)(a.FK,null,(0,a.pI)(l.otherRatings,e=>((0,a.uX)(),(0,a.CE)("div",{class:"rating-item",key:e.id},[(0,a.Lk)("div",we,[(0,a.bF)(v,{src:e.user.avatar,size:32},{default:(0,a.k6)(()=>[(0,a.eW)((0,s.v_)(e.user.nickname.charAt(0)),1)]),_:2},1032,["src"]),(0,a.Lk)("span",Fe,(0,s.v_)(e.user.nickname),1)]),(0,a.Lk)("div",De,[(0,a.Lk)("div",Ee,[(0,a.bF)(r,{"model-value":e.score,max:10,"allow-half":!0,disabled:"",size:"small"},null,8,["model-value"]),(0,a.Lk)("span",Ie,(0,s.v_)(e.score)+"/10",1)]),e.comment?((0,a.uX)(),(0,a.CE)("div",Me,[(0,a.Lk)("p",null,(0,s.v_)(e.comment),1)])):(0,a.Q3)("",!0),(0,a.Lk)("div",Xe,(0,s.v_)(n.formatDate(e.updatedAt)),1)])]))),128))]),l.hasMoreRatings?((0,a.uX)(),(0,a.CE)("div",$e,[(0,a.bF)(g,{type:"text",onClick:n.loadMoreRatings,loading:l.loadingMore},{default:(0,a.k6)(()=>t[9]||(t[9]=[(0,a.eW)(" 加载更多 ")])),_:1,__:[9]},8,["onClick","loading"])])):(0,a.Q3)("",!0)])):(0,a.Q3)("",!0)])}var Ue={name:"MovieRating",components:{Star:ce.Star,Edit:ce.Edit,Delete:ce.Delete},props:{movieId:{type:Number,required:!0},showOtherRatings:{type:Boolean,default:!1}},data(){return{isEditing:!1,editScore:0,editComment:"",submitting:!1,deleting:!1,otherRatings:[],loadingMore:!1,hasMoreRatings:!1,currentPage:0}},computed:{...(0,de.L8)("user",["isLoggedIn"]),...(0,de.L8)("rating",["currentRating"]),displayScore(){return this.currentRating?this.currentRating.score:0}},async mounted(){await this.loadCurrentRating(),this.showOtherRatings&&await this.loadOtherRatings()},methods:{...(0,de.i0)("rating",["fetchUserMovieRating","addOrUpdateRating","deleteRating","fetchMovieRatings"]),async loadCurrentRating(){if(this.isLoggedIn)try{await this.fetchUserMovieRating(this.movieId)}catch(e){console.error("加载用户评分失败:",e)}},async loadOtherRatings(){try{const e=await this.fetchMovieRatings({movieId:this.movieId,page:this.currentPage,size:5});e.success&&(this.otherRatings=e.data.ratings,this.hasMoreRatings=e.data.currentPage<e.data.totalPages-1)}catch(e){console.error("加载其他用户评分失败:",e)}},async loadMoreRatings(){this.loadingMore=!0;try{this.currentPage++;const e=await this.fetchMovieRatings({movieId:this.movieId,page:this.currentPage,size:5});e.success&&(this.otherRatings.push(...e.data.ratings),this.hasMoreRatings=e.data.currentPage<e.data.totalPages-1)}catch(e){console.error("加载更多评分失败:",e),this.currentPage--}finally{this.loadingMore=!1}},startEditing(){if(!this.isLoggedIn)return this.$message.warning("请先登录"),void this.$router.push("/login");this.isEditing=!0,this.editScore=this.currentRating?this.currentRating.score:0,this.editComment=this.currentRating?this.currentRating.comment:""},cancelEdit(){this.isEditing=!1,this.editScore=0,this.editComment=""},async handleSubmit(){if(0!==this.editScore){this.submitting=!0;try{const e=await this.addOrUpdateRating({movieId:this.movieId,score:this.editScore,comment:this.editComment.trim()});e.success&&(this.$message.success(e.message),this.isEditing=!1,this.$emit("rating-updated",e.rating))}catch(e){console.error("提交评分失败:",e),this.$message.error(e.message||"提交失败")}finally{this.submitting=!1}}else this.$message.warning("请选择评分")},async handleDelete(){try{await this.$confirm("确定要删除这个评分吗？","确认删除",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),this.deleting=!0;const e=await this.deleteRating(this.movieId);e.success&&(this.$message.success(e.message),this.$emit("rating-deleted"))}catch(e){"cancel"!==e&&(console.error("删除评分失败:",e),this.$message.error(e.message||"删除失败"))}finally{this.deleting=!1}},formatDate(e){return new Date(e).toLocaleDateString("zh-CN")}}},Ve=i(262);const Qe=(0,Ve.A)(Ue,[["render",Se],["__scopeId","data-v-38863e36"]]);var We=Qe,xe={name:"MovieDetail",components:{ArrowLeft:ce.ArrowLeft,Star:ce.Star,StarFilled:ce.StarFilled,ChatDotRound:ce.ChatDotRound,Share:ce.Share,VideoCamera:ce.VideoCamera,MovieRating:We},data(){return{movie:null,loading:!1,collectionLoading:!1,ratingLoading:!1,isCollected:!1,userRating:null,recommendations:[],showRatingDialog:!1,dialogRating:{score:0,comment:""}}},computed:{...(0,de.L8)("user",["isLoggedIn"]),movieId(){return parseInt(this.$route.params.id)},releaseYear(){return this.movie?.releaseDate?new Date(this.movie.releaseDate).getFullYear():"未知"}},async mounted(){await this.loadMovieDetail(),this.isLoggedIn&&(await this.checkCollectionStatus(),await this.loadUserRating()),await this.loadRecommendations()},methods:{...(0,de.i0)("movie",["fetchMovieById"]),...(0,de.i0)("collection",{addMovieToCollection:"addToCollection",removeMovieFromCollection:"removeFromCollection",checkMovieCollection:"checkCollection"}),...(0,de.i0)("rating",["addOrUpdateRating","fetchUserMovieRating"]),async loadMovieDetail(){this.loading=!0;try{const e=await this.fetchMovieById(this.movieId);e.success?this.movie=e.movie:this.$message.error("电影不存在")}catch(e){console.error("加载电影详情失败:",e),this.$message.error("加载电影详情失败")}finally{this.loading=!1}},async checkCollectionStatus(){try{const e=await this.checkMovieCollection(this.movieId);this.isCollected=e.isCollected}catch(e){console.error("检查收藏状态失败:",e)}},async loadUserRating(){try{const e=await this.fetchUserMovieRating(this.movieId);this.userRating=e.rating,this.userRating&&(this.dialogRating.score=this.userRating.score,this.dialogRating.comment=this.userRating.comment||"")}catch(e){console.error("加载用户评分失败:",e)}},async loadRecommendations(){this.recommendations=[]},async toggleCollection(){if(!this.isLoggedIn)return this.$message.warning("请先登录"),void this.$router.push("/login");this.collectionLoading=!0;try{this.isCollected?(await this.removeMovieFromCollection(this.movieId),this.isCollected=!1,this.$message.success("取消收藏成功")):(await this.addMovieToCollection(this.movieId),this.isCollected=!0,this.$message.success("收藏成功"))}catch(e){console.error("收藏操作失败:",e),this.$message.error(e.message||"操作失败")}finally{this.collectionLoading=!1}},async submitRating(){if(this.isLoggedIn)if(0!==this.dialogRating.score){this.ratingLoading=!0;try{const e=await this.addOrUpdateRating({movieId:this.movieId,score:this.dialogRating.score,comment:this.dialogRating.comment.trim()});e.success&&(this.userRating=e.rating,this.showRatingDialog=!1,this.$message.success(e.message),await this.loadMovieDetail())}catch(e){console.error("提交评分失败:",e),this.$message.error(e.message||"提交失败")}finally{this.ratingLoading=!1}}else this.$message.warning("请选择评分");else this.$message.warning("请先登录")},handleRatingUpdated(e){this.userRating=e,this.loadMovieDetail()},handleRatingDeleted(){this.userRating=null,this.loadMovieDetail()},shareMovie(){navigator.share?navigator.share({title:this.movie.title,text:`推荐一部好电影：${this.movie.title}`,url:window.location.href}):navigator.clipboard.writeText(window.location.href).then(()=>{this.$message.success("链接已复制到剪贴板")}).catch(()=>{this.$message.error("分享失败")})},viewMovie(e){this.$router.push(`/movies/${e}`)},formatDate(e){return new Date(e).toLocaleDateString("zh-CN")}},watch:{"$route.params.id":{handler(){this.loadMovieDetail(),this.isLoggedIn&&(this.checkCollectionStatus(),this.loadUserRating())},immediate:!1}}};const Pe=(0,Ve.A)(xe,[["render",re],["__scopeId","data-v-3aaf4c82"]]);var ze=Pe}}]);
//# sourceMappingURL=129.f6aed3a5.js.map
"use strict";(self["webpackChunkmovie_collection_frontend"]=self["webpackChunkmovie_collection_frontend"]||[]).push([[160],{160:function(n,o,e){e.r(o),e.d(o,{default:function(){return f}});var t=e(641);const c={class:"not-found-page"},l={class:"not-found-content"},i={class:"error-icon"},r={class:"actions"};function u(n,o,e,u,a,s){const k=(0,t.g2)("Warning"),d=(0,t.g2)("el-icon"),f=(0,t.g2)("el-button");return(0,t.uX)(),(0,t.CE)("div",c,[(0,t.Lk)("div",l,[(0,t.Lk)("div",i,[(0,t.bF)(d,null,{default:(0,t.k6)(()=>[(0,t.bF)(k)]),_:1})]),o[2]||(o[2]=(0,t.Lk)("h1",null,"404",-1)),o[3]||(o[3]=(0,t.Lk)("h2",null,"页面未找到",-1)),o[4]||(o[4]=(0,t.Lk)("p",null,"抱歉，您访问的页面不存在或已被移除。",-1)),(0,t.Lk)("div",r,[(0,t.bF)(f,{type:"primary",onClick:s.goHome},{default:(0,t.k6)(()=>o[0]||(o[0]=[(0,t.eW)("返回首页")])),_:1,__:[0]},8,["onClick"]),(0,t.bF)(f,{onClick:s.goBack},{default:(0,t.k6)(()=>o[1]||(o[1]=[(0,t.eW)("返回上页")])),_:1,__:[1]},8,["onClick"])])])])}var a=e(548),s={name:"NotFound",components:{Warning:a.Warning},methods:{goHome(){this.$router.push("/")},goBack(){this.$router.go(-1)}}},k=e(262);const d=(0,k.A)(s,[["render",u],["__scopeId","data-v-fb256fec"]]);var f=d}}]);
//# sourceMappingURL=160.bbf82b8d.js.map
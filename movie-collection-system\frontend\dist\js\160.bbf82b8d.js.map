{"version": 3, "file": "js/160.bbf82b8d.js", "mappings": "mNACOA,MAAM,kB,GACJA,MAAM,qB,GACJA,MAAM,c,GAMNA,MAAM,W,oHARf,QAaM,MAbN,EAaM,EAZJ,QAWM,MAXN,EAWM,EAVJ,QAEM,MAFN,EAEM,EADJ,QAA8B,Q,iBAArB,IAAW,EAAX,QAAW,K,qBAEtB,QAAY,UAAR,OAAG,I,aACP,QAAc,UAAV,SAAK,I,aACT,QAAyB,SAAtB,sBAAkB,KACrB,QAGM,MAHN,EAGM,EAFJ,QAA0D,GAA/CC,KAAK,UAAW,QAAO,EAAAC,Q,kBAAQ,IAAI,c,QAAJ,W,4BAC1C,QAA2C,GAA/B,QAAO,EAAAC,QAAM,C,iBAAE,IAAI,c,QAAJ,W,8CASnC,GACEC,KAAM,WACNC,WAAY,CACVC,QAAO,WAETC,QAAS,CACP,MAAAL,GACEM,KAAKC,QAAQC,KAAK,IACpB,EAEA,MAAAP,GACEK,KAAKC,QAAQE,IAAI,EACnB,I,SCzBJ,MAAMC,GAA2B,OAAgB,EAAQ,CAAC,CAAC,SAASC,GAAQ,CAAC,YAAY,qBAEzF,O", "sources": ["webpack://movie-collection-frontend/./src/views/NotFound.vue", "webpack://movie-collection-frontend/./src/views/NotFound.vue?0aab"], "sourcesContent": ["<template>\n  <div class=\"not-found-page\">\n    <div class=\"not-found-content\">\n      <div class=\"error-icon\">\n        <el-icon><Warning /></el-icon>\n      </div>\n      <h1>404</h1>\n      <h2>页面未找到</h2>\n      <p>抱歉，您访问的页面不存在或已被移除。</p>\n      <div class=\"actions\">\n        <el-button type=\"primary\" @click=\"goHome\">返回首页</el-button>\n        <el-button @click=\"goBack\">返回上页</el-button>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport { Warning } from '@element-plus/icons-vue'\n\nexport default {\n  name: 'NotFound',\n  components: {\n    Warning\n  },\n  methods: {\n    goHome() {\n      this.$router.push('/')\n    },\n    \n    goBack() {\n      this.$router.go(-1)\n    }\n  }\n}\n</script>\n\n<style scoped>\n.not-found-page {\n  min-height: 100vh;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);\n}\n\n.not-found-content {\n  text-align: center;\n  padding: 40px;\n}\n\n.error-icon {\n  font-size: 80px;\n  color: #f56c6c;\n  margin-bottom: 20px;\n}\n\nh1 {\n  font-size: 120px;\n  font-weight: bold;\n  color: #333;\n  margin: 0;\n  line-height: 1;\n}\n\nh2 {\n  font-size: 32px;\n  color: #666;\n  margin: 20px 0;\n  font-weight: 600;\n}\n\np {\n  font-size: 16px;\n  color: #888;\n  margin-bottom: 40px;\n  line-height: 1.6;\n}\n\n.actions {\n  display: flex;\n  gap: 16px;\n  justify-content: center;\n}\n\n@media (max-width: 768px) {\n  h1 {\n    font-size: 80px;\n  }\n  \n  h2 {\n    font-size: 24px;\n  }\n  \n  .actions {\n    flex-direction: column;\n    align-items: center;\n  }\n}\n</style>\n", "import { render } from \"./NotFound.vue?vue&type=template&id=fb256fec&scoped=true\"\nimport script from \"./NotFound.vue?vue&type=script&lang=js\"\nexport * from \"./NotFound.vue?vue&type=script&lang=js\"\n\nimport \"./NotFound.vue?vue&type=style&index=0&id=fb256fec&scoped=true&lang=css\"\n\nimport exportComponent from \"../../node_modules/vue-loader/dist/exportHelper.js\"\nconst __exports__ = /*#__PURE__*/exportComponent(script, [['render',render],['__scopeId',\"data-v-fb256fec\"]])\n\nexport default __exports__"], "names": ["class", "type", "goHome", "goBack", "name", "components", "Warning", "methods", "this", "$router", "push", "go", "__exports__", "render"], "sourceRoot": ""}
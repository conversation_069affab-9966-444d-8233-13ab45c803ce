"use strict";(self["webpackChunkmovie_collection_frontend"]=self["webpackChunkmovie_collection_frontend"]||[]).push([[184],{184:function(e,t,a){a.r(t),a.d(t,{default:function(){return q}});var s=a(641),l=a(33),i=a(751);const o={class:"profile-page"},r={class:"container"},n={class:"profile-header"},c={class:"user-avatar"},d={class:"user-info"},u={class:"username"},m={class:"bio"},h={class:"user-stats"},v={class:"stat-item"},f={class:"stat-number"},p={class:"stat-item"},g={class:"stat-number"},k={class:"stat-item"},b={class:"stat-number"},_={class:"movies-grid"},C=["onClick"],F={class:"movie-poster"},y=["src","alt"],L={class:"movie-overlay"},w={class:"movie-rating"},D={class:"movie-info"},E={class:"movie-title"},U={class:"movie-year"},V={key:0,class:"empty-state"},A={class:"ratings-list"},x={class:"rating-movie"},I=["src","alt"],S={class:"rating-info"},$={class:"rating-score"},X={class:"rating-date"},R={key:0,class:"empty-state"},W={class:"history-list"},P={class:"history-movie"},z=["src","alt"],H={class:"history-info"},K={key:0,class:"empty-state"},M={class:"avatar-options"},j=["onClick"];function Q(e,t,a,Q,T,N){const O=(0,s.g2)("User"),Y=(0,s.g2)("el-icon"),B=(0,s.g2)("el-avatar"),q=(0,s.g2)("el-button"),G=(0,s.g2)("Star"),J=(0,s.g2)("Delete"),Z=(0,s.g2)("el-tab-pane"),ee=(0,s.g2)("el-rate"),te=(0,s.g2)("VideoCamera"),ae=(0,s.g2)("el-tabs"),se=(0,s.g2)("el-input"),le=(0,s.g2)("el-form-item"),ie=(0,s.g2)("el-form"),oe=(0,s.g2)("el-dialog"),re=(0,s.gN)("loading");return(0,s.uX)(),(0,s.CE)("div",o,[(0,s.Lk)("div",r,[(0,s.Lk)("div",n,[(0,s.Lk)("div",c,[(0,s.bF)(B,{size:120,src:e.userInfo?.avatar},{default:(0,s.k6)(()=>[(0,s.bF)(Y,null,{default:(0,s.k6)(()=>[(0,s.bF)(O)]),_:1})]),_:1},8,["src"]),(0,s.bF)(q,{type:"text",onClick:t[0]||(t[0]=e=>T.showAvatarDialog=!0)},{default:(0,s.k6)(()=>t[12]||(t[12]=[(0,s.eW)("更换头像")])),_:1,__:[12]})]),(0,s.Lk)("div",d,[(0,s.Lk)("h2",null,(0,l.v_)(e.userInfo?.nickname||e.userInfo?.username||"用户"),1),(0,s.Lk)("p",u,"@"+(0,l.v_)(e.userInfo?.username||""),1),(0,s.Lk)("p",m,(0,l.v_)(e.userInfo?.bio||"这个人很懒，什么都没有留下..."),1),(0,s.Lk)("div",h,[(0,s.Lk)("div",v,[(0,s.Lk)("span",f,(0,l.v_)(T.userStats.collectionCount),1),t[13]||(t[13]=(0,s.Lk)("span",{class:"stat-label"},"收藏",-1))]),(0,s.Lk)("div",p,[(0,s.Lk)("span",g,(0,l.v_)(T.userStats.ratingCount),1),t[14]||(t[14]=(0,s.Lk)("span",{class:"stat-label"},"评分",-1))]),(0,s.Lk)("div",k,[(0,s.Lk)("span",b,(0,l.v_)(T.userStats.commentCount),1),t[15]||(t[15]=(0,s.Lk)("span",{class:"stat-label"},"评论",-1))])]),(0,s.bF)(q,{type:"primary",onClick:t[1]||(t[1]=e=>T.showEditDialog=!0)},{default:(0,s.k6)(()=>t[16]||(t[16]=[(0,s.eW)("编辑资料")])),_:1,__:[16]})])]),(0,s.bF)(ae,{modelValue:T.activeTab,"onUpdate:modelValue":t[5]||(t[5]=e=>T.activeTab=e),class:"profile-tabs"},{default:(0,s.k6)(()=>[(0,s.bF)(Z,{label:"我的收藏",name:"collections"},{default:(0,s.k6)(()=>[(0,s.bo)(((0,s.uX)(),(0,s.CE)("div",_,[((0,s.uX)(!0),(0,s.CE)(s.FK,null,(0,s.pI)(T.collections,e=>((0,s.uX)(),(0,s.CE)("div",{class:"movie-card",key:e.id,onClick:t=>N.viewMovie(e.id)},[(0,s.Lk)("div",F,[(0,s.Lk)("img",{src:e.posterPath||"/placeholder.jpg",alt:e.title},null,8,y),(0,s.Lk)("div",L,[(0,s.Lk)("div",w,[(0,s.bF)(Y,null,{default:(0,s.k6)(()=>[(0,s.bF)(G)]),_:1}),(0,s.eW)(" "+(0,l.v_)(e.averageRating),1)]),(0,s.bF)(q,{type:"danger",size:"small",circle:"",onClick:(0,i.D$)(t=>N.removeFromCollection(e.id),["stop"])},{default:(0,s.k6)(()=>[(0,s.bF)(Y,null,{default:(0,s.k6)(()=>[(0,s.bF)(J)]),_:1})]),_:2},1032,["onClick"])])]),(0,s.Lk)("div",D,[(0,s.Lk)("h4",E,(0,l.v_)(e.title),1),(0,s.Lk)("p",U,(0,l.v_)(e.releaseDate?new Date(e.releaseDate).getFullYear():"未知"),1)])],8,C))),128))])),[[re,T.loading]]),0===T.collections.length?((0,s.uX)(),(0,s.CE)("div",V,[(0,s.bF)(Y,{class:"empty-icon"},{default:(0,s.k6)(()=>[(0,s.bF)(G)]),_:1}),t[18]||(t[18]=(0,s.Lk)("p",null,"还没有收藏任何电影",-1)),(0,s.bF)(q,{type:"primary",onClick:t[2]||(t[2]=t=>e.$router.push("/movies"))},{default:(0,s.k6)(()=>t[17]||(t[17]=[(0,s.eW)("去发现电影")])),_:1,__:[17]})])):(0,s.Q3)("",!0)]),_:1}),(0,s.bF)(Z,{label:"我的评分",name:"ratings"},{default:(0,s.k6)(()=>[(0,s.bo)(((0,s.uX)(),(0,s.CE)("div",A,[((0,s.uX)(!0),(0,s.CE)(s.FK,null,(0,s.pI)(T.ratings,e=>((0,s.uX)(),(0,s.CE)("div",{class:"rating-item",key:e.id},[(0,s.Lk)("div",x,[(0,s.Lk)("img",{src:e.movie?.posterPath||"/placeholder.jpg",alt:e.movie?.title},null,8,I),(0,s.Lk)("div",S,[(0,s.Lk)("h4",null,(0,l.v_)(e.movie?.title),1),(0,s.Lk)("p",null,(0,l.v_)(e.movie?.releaseDate?new Date(e.movie.releaseDate).getFullYear():"未知"),1)])]),(0,s.Lk)("div",$,[(0,s.bF)(ee,{modelValue:e.score,"onUpdate:modelValue":t=>e.score=t,disabled:"","show-score":""},null,8,["modelValue","onUpdate:modelValue"]),(0,s.Lk)("p",X,(0,l.v_)(N.formatDate(e.createdAt)),1)])]))),128))])),[[re,T.loading]]),0===T.ratings.length?((0,s.uX)(),(0,s.CE)("div",R,[(0,s.bF)(Y,{class:"empty-icon"},{default:(0,s.k6)(()=>[(0,s.bF)(G)]),_:1}),t[20]||(t[20]=(0,s.Lk)("p",null,"还没有评分任何电影",-1)),(0,s.bF)(q,{type:"primary",onClick:t[3]||(t[3]=t=>e.$router.push("/movies"))},{default:(0,s.k6)(()=>t[19]||(t[19]=[(0,s.eW)("去评分电影")])),_:1,__:[19]})])):(0,s.Q3)("",!0)]),_:1}),(0,s.bF)(Z,{label:"观影历史",name:"history"},{default:(0,s.k6)(()=>[(0,s.bo)(((0,s.uX)(),(0,s.CE)("div",W,[((0,s.uX)(!0),(0,s.CE)(s.FK,null,(0,s.pI)(T.watchHistory,e=>((0,s.uX)(),(0,s.CE)("div",{class:"history-item",key:e.id},[(0,s.Lk)("div",P,[(0,s.Lk)("img",{src:e.movie?.posterPath||"/placeholder.jpg",alt:e.movie?.title},null,8,z),(0,s.Lk)("div",H,[(0,s.Lk)("h4",null,(0,l.v_)(e.movie?.title),1),(0,s.Lk)("p",null,"观看时间："+(0,l.v_)(N.formatDate(e.watchedAt)),1)])])]))),128))])),[[re,T.loading]]),0===T.watchHistory.length?((0,s.uX)(),(0,s.CE)("div",K,[(0,s.bF)(Y,{class:"empty-icon"},{default:(0,s.k6)(()=>[(0,s.bF)(te)]),_:1}),t[22]||(t[22]=(0,s.Lk)("p",null,"还没有观影记录",-1)),(0,s.bF)(q,{type:"primary",onClick:t[4]||(t[4]=t=>e.$router.push("/movies"))},{default:(0,s.k6)(()=>t[21]||(t[21]=[(0,s.eW)("去看电影")])),_:1,__:[21]})])):(0,s.Q3)("",!0)]),_:1})]),_:1},8,["modelValue"])]),(0,s.bF)(oe,{modelValue:T.showEditDialog,"onUpdate:modelValue":t[9]||(t[9]=e=>T.showEditDialog=e),title:"编辑资料",width:"500px"},{footer:(0,s.k6)(()=>[(0,s.bF)(q,{onClick:t[8]||(t[8]=e=>T.showEditDialog=!1)},{default:(0,s.k6)(()=>t[23]||(t[23]=[(0,s.eW)("取消")])),_:1,__:[23]}),(0,s.bF)(q,{type:"primary",onClick:N.updateProfile,loading:T.updating},{default:(0,s.k6)(()=>t[24]||(t[24]=[(0,s.eW)("保存")])),_:1,__:[24]},8,["onClick","loading"])]),default:(0,s.k6)(()=>[(0,s.bF)(ie,{model:T.editForm,rules:T.editRules,ref:"editFormRef"},{default:(0,s.k6)(()=>[(0,s.bF)(le,{label:"昵称",prop:"nickname"},{default:(0,s.k6)(()=>[(0,s.bF)(se,{modelValue:T.editForm.nickname,"onUpdate:modelValue":t[6]||(t[6]=e=>T.editForm.nickname=e),placeholder:"请输入昵称"},null,8,["modelValue"])]),_:1}),(0,s.bF)(le,{label:"个人简介",prop:"bio"},{default:(0,s.k6)(()=>[(0,s.bF)(se,{modelValue:T.editForm.bio,"onUpdate:modelValue":t[7]||(t[7]=e=>T.editForm.bio=e),type:"textarea",rows:4,placeholder:"介绍一下自己吧..."},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["modelValue"]),(0,s.bF)(oe,{modelValue:T.showAvatarDialog,"onUpdate:modelValue":t[11]||(t[11]=e=>T.showAvatarDialog=e),title:"更换头像",width:"400px"},{footer:(0,s.k6)(()=>[(0,s.bF)(q,{onClick:t[10]||(t[10]=e=>T.showAvatarDialog=!1)},{default:(0,s.k6)(()=>t[25]||(t[25]=[(0,s.eW)("取消")])),_:1,__:[25]}),(0,s.bF)(q,{type:"primary",onClick:N.updateAvatar,loading:T.updating},{default:(0,s.k6)(()=>t[26]||(t[26]=[(0,s.eW)("确定")])),_:1,__:[26]},8,["onClick","loading"])]),default:(0,s.k6)(()=>[(0,s.Lk)("div",M,[((0,s.uX)(!0),(0,s.CE)(s.FK,null,(0,s.pI)(T.avatarOptions,e=>((0,s.uX)(),(0,s.CE)("div",{class:(0,l.C4)(["avatar-option",{active:T.selectedAvatar===e}]),key:e,onClick:t=>N.selectAvatar(e)},[(0,s.bF)(B,{size:60,src:e},null,8,["src"])],10,j))),128))])]),_:1},8,["modelValue"])])}var T=a(548),N=a(278),O={name:"Profile",components:{User:T.User,Star:T.Star,Delete:T.Delete,VideoCamera:T.VideoCamera},data(){return{activeTab:"collections",loading:!1,updating:!1,showEditDialog:!1,showAvatarDialog:!1,selectedAvatar:"",collections:[],ratings:[],watchHistory:[],userStats:{collectionCount:0,ratingCount:0,commentCount:0},editForm:{nickname:"",bio:""},editRules:{nickname:[{max:20,message:"昵称长度不能超过20个字符",trigger:"blur"}],bio:[{max:200,message:"个人简介不能超过200个字符",trigger:"blur"}]},avatarOptions:["https://via.placeholder.com/120x120/409eff/ffffff?text=A","https://via.placeholder.com/120x120/67c23a/ffffff?text=B","https://via.placeholder.com/120x120/e6a23c/ffffff?text=C","https://via.placeholder.com/120x120/f56c6c/ffffff?text=D","https://via.placeholder.com/120x120/909399/ffffff?text=E","https://via.placeholder.com/120x120/9c27b0/ffffff?text=F"]}},computed:{...(0,N.L8)("user",["userInfo","isLoggedIn"])},async mounted(){this.isLoggedIn?(this.initEditForm(),await this.loadUserData()):this.$router.push("/login")},methods:{...(0,N.i0)("user",["updateUserInfo"]),...(0,N.i0)("collection",["fetchUserCollections","fetchCollectionStats",{removeMovieFromCollection:"removeFromCollection"}]),...(0,N.i0)("rating",["fetchUserRatings","fetchRatingStats"]),initEditForm(){this.editForm.nickname=this.userInfo?.nickname||"",this.editForm.bio=this.userInfo?.bio||""},async loadUserData(){this.loading=!0;try{await this.loadCollections(),await this.loadRatings(),await this.loadWatchHistory(),await this.loadUserStats()}catch(e){console.error("加载用户数据失败:",e),this.$message.error("加载数据失败")}finally{this.loading=!1}},async loadCollections(){try{const e=await this.fetchUserCollections({page:0,size:20});e.success&&(this.collections=e.data.movies)}catch(e){console.error("加载收藏失败:",e)}},async loadRatings(){try{const e=await this.fetchUserRatings({page:0,size:20});e.success&&(this.ratings=e.data.ratings)}catch(e){console.error("加载评分失败:",e)}},async loadWatchHistory(){this.watchHistory=[]},async loadUserStats(){try{const[e,t]=await Promise.all([this.fetchCollectionStats(),this.fetchRatingStats()]);this.userStats={collectionCount:e.success?e.stats.totalCollections:0,ratingCount:t.success?t.stats.totalRatings:0,commentCount:0}}catch(e){console.error("加载用户统计失败:",e)}},async updateProfile(){try{await this.$refs.editFormRef.validate(),this.updating=!0,await this.updateUserInfo(this.editForm),this.$message.success("资料更新成功"),this.showEditDialog=!1}catch(e){console.error("更新资料失败:",e),this.$message.error("更新失败")}finally{this.updating=!1}},selectAvatar(e){this.selectedAvatar=e},async updateAvatar(){if(this.selectedAvatar)try{this.updating=!0,await this.updateUserInfo({avatar:this.selectedAvatar}),this.$message.success("头像更新成功"),this.showAvatarDialog=!1}catch(e){console.error("更新头像失败:",e),this.$message.error("更新失败")}finally{this.updating=!1}else this.$message.warning("请选择头像")},viewMovie(e){this.$router.push(`/movies/${e}`)},async removeFromCollection(e){try{await this.removeMovieFromCollection(e),this.$message.success("已取消收藏"),await this.loadCollections(),await this.loadUserStats()}catch(t){console.error("取消收藏失败:",t),this.$message.error("操作失败")}},formatDate(e){return new Date(e).toLocaleDateString("zh-CN")}}},Y=a(262);const B=(0,Y.A)(O,[["render",Q],["__scopeId","data-v-02c5282e"]]);var q=B}}]);
//# sourceMappingURL=184.34c8fc60.js.map
{"version": 3, "file": "js/184.34c8fc60.js", "mappings": "oOACOA,MAAM,gB,GACJA,MAAM,a,GAEJA,MAAM,kB,GACJA,MAAM,e,GAONA,MAAM,a,GAENA,MAAM,Y,GACNA,MAAM,O,GACJA,MAAM,c,GACJA,MAAM,a,GACHA,MAAM,e,GAGTA,MAAM,a,GACHA,MAAM,e,GAGTA,MAAM,a,GACHA,MAAM,e,GAWXA,MAAM,e,iBAEFA,MAAM,gB,mBAEJA,MAAM,iB,GACJA,MAAM,gB,GAcVA,MAAM,c,GACLA,MAAM,e,GACPA,MAAM,c,SAIsBA,MAAM,e,GAQtCA,MAAM,gB,GAEFA,MAAM,gB,mBAEJA,MAAM,e,GAKRA,MAAM,gB,GAENA,MAAM,e,SAIkBA,MAAM,e,GAQlCA,MAAM,gB,GAEFA,MAAM,iB,mBAEJA,MAAM,gB,SAOqBA,MAAM,e,GAgC3CA,MAAM,kB,6ZAvIf,QAuJM,MAvJN,EAuJM,EAtJJ,QA6GM,MA7GN,EA6GM,EA3GJ,QA4BM,MA5BN,EA4BM,EA3BJ,QAKM,MALN,EAKM,EAJJ,QAEY,GAFAC,KAAM,IAAMC,IAAKC,EAAAA,UAAUC,Q,kBACrC,IAA2B,EAA3B,QAA2B,Q,iBAAlB,IAAQ,EAAR,QAAQ,K,yBAEnB,QAAwE,GAA7DC,KAAK,OAAQ,QAAK,eAAE,EAAAC,kBAAmB,I,kBAAM,IAAI,gB,QAAJ,W,iBAG1D,QAmBM,MAnBN,EAmBM,EAlBJ,QAA+D,mBAAxDH,EAAAA,UAAUI,UAAYJ,EAAAA,UAAUK,UAAY,MAAJ,IAC/C,QAAuD,IAAvD,EAAoB,KAAC,QAAGL,EAAAA,UAAUK,UAAY,IAAJ,IAC1C,QAA4D,IAA5D,GAA4D,QAA1CL,EAAAA,UAAUM,KAAO,oBAAJ,IAC/B,QAaM,MAbN,EAaM,EAZJ,QAGM,MAHN,EAGM,EAFJ,QAAgE,OAAhE,GAAgE,QAAnC,EAAAC,UAAUC,iBAAe,G,eACtD,QAAkC,QAA5BX,MAAM,cAAa,MAAE,OAE7B,QAGM,MAHN,EAGM,EAFJ,QAA4D,OAA5D,GAA4D,QAA/B,EAAAU,UAAUE,aAAW,G,eAClD,QAAkC,QAA5BZ,MAAM,cAAa,MAAE,OAE7B,QAGM,MAHN,EAGM,EAFJ,QAA6D,OAA7D,GAA6D,QAAhC,EAAAU,UAAUG,cAAY,G,eACnD,QAAkC,QAA5Bb,MAAM,cAAa,MAAE,SAG/B,QAAyE,GAA9DK,KAAK,UAAW,QAAK,eAAE,EAAAS,gBAAiB,I,kBAAM,IAAI,gB,QAAJ,W,mBAK7D,QA2EU,I,WA3EQ,EAAAC,U,qCAAA,EAAS,aAAEf,MAAM,gB,kBACjC,IA+Bc,EA/Bd,QA+Bc,GA/BDgB,MAAM,OAAOC,KAAK,e,kBAC7B,IAwBM,E,qBAxBN,QAwBM,MAxBN,EAwBM,G,aAvBJ,QAsBM,mBAtBkC,EAAAC,YAATC,K,WAA/B,QAsBM,OAtBDnB,MAAM,aAA2CoB,IAAKD,EAAME,GAAK,QAAK,GAAE,EAAAC,UAAUH,EAAME,K,EAC3F,QAgBM,MAhBN,EAgBM,EAfJ,QAAwE,OAAlEnB,IAAKiB,EAAMI,YAAc,mBAAqBC,IAAKL,EAAMM,O,WAC/D,QAaM,MAbN,EAaM,EAZJ,QAGM,MAHN,EAGM,EAFJ,QAA2B,Q,iBAAlB,IAAQ,EAAR,QAAQ,K,eAAU,KAC3B,QAAGN,EAAMO,eAAa,MAExB,QAOY,GANVrB,KAAK,SACLJ,KAAK,QACL0B,OAAA,GACC,SAAK,WAAO,EAAAC,qBAAqBT,EAAME,IAAE,W,kBAE1C,IAA6B,EAA7B,QAA6B,Q,iBAApB,IAAU,EAAV,QAAU,K,oCAIzB,QAGM,MAHN,EAGM,EAFJ,QAA8C,KAA9C,GAA8C,QAAnBF,EAAMM,OAAK,IACtC,QAAoG,IAApG,GAAoG,QAA3EN,EAAMU,YAAc,IAAIC,KAAKX,EAAMU,aAAaE,cAAgB,MAAL,M,qBArBtD,EAAAC,WAyBF,IAAvB,EAAAd,YAAYe,S,WAAvB,QAIM,MAJN,EAIM,EAHJ,QAA8C,GAArCjC,MAAM,cAAY,C,iBAAC,IAAQ,EAAR,QAAQ,K,qBACpC,QAAgB,SAAb,aAAS,KACZ,QAA4E,GAAjEK,KAAK,UAAW,QAAK,eAAE6B,EAAAA,QAAQC,KAAK,a,kBAAY,IAAK,gB,QAAL,Y,0CAI/D,QAqBc,GArBDnB,MAAM,OAAOC,KAAK,W,kBAC7B,IAcM,E,qBAdN,QAcM,MAdN,EAcM,G,aAbJ,QAYM,mBAZoC,EAAAmB,QAAVC,K,WAAhC,QAYM,OAZDrC,MAAM,cAAyCoB,IAAKiB,EAAOhB,I,EAC9D,QAMM,MANN,EAMM,EALJ,QAAwF,OAAlFnB,IAAKmC,EAAOlB,OAAOI,YAAc,mBAAqBC,IAAKa,EAAOlB,OAAOM,O,WAC/E,QAGM,MAHN,EAGM,EAFJ,QAAkC,mBAA3BY,EAAOlB,OAAOM,OAAK,IAC1B,QAAgG,kBAA1FY,EAAOlB,OAAOU,YAAc,IAAIC,KAAKO,EAAOlB,MAAMU,aAAaE,cAAgB,MAAL,QAGpF,QAGM,MAHN,EAGM,EAFJ,QAAsD,I,WAApCM,EAAOC,M,yBAAPD,EAAY,QAAEE,SAAA,GAAS,iB,8CACzC,QAA6D,IAA7D,GAA6D,QAAnC,EAAAC,WAAWH,EAAOI,YAAS,S,cAXtB,EAAAT,WAeP,IAAnB,EAAAI,QAAQH,S,WAAnB,QAIM,MAJN,EAIM,EAHJ,QAA8C,GAArCjC,MAAM,cAAY,C,iBAAC,IAAQ,EAAR,QAAQ,K,qBACpC,QAAgB,SAAb,aAAS,KACZ,QAA4E,GAAjEK,KAAK,UAAW,QAAK,eAAE6B,EAAAA,QAAQC,KAAK,a,kBAAY,IAAK,gB,QAAL,Y,0CAI/D,QAiBc,GAjBDnB,MAAM,OAAOC,KAAK,W,kBAC7B,IAUM,E,qBAVN,QAUM,MAVN,EAUM,G,aATJ,QAQM,mBARsC,EAAAyB,aAAXC,K,WAAjC,QAQM,OARD3C,MAAM,eAAgDoB,IAAKuB,EAAQtB,I,EACtE,QAMM,MANN,EAMM,EALJ,QAA0F,OAApFnB,IAAKyC,EAAQxB,OAAOI,YAAc,mBAAqBC,IAAKmB,EAAQxB,OAAOM,O,WACjF,QAGM,MAHN,EAGM,EAFJ,QAAmC,mBAA5BkB,EAAQxB,OAAOM,OAAK,IAC3B,QAA+C,SAA5C,SAAK,QAAG,EAAAe,WAAWG,EAAQC,YAAS,W,cANV,EAAAZ,WAWF,IAAxB,EAAAU,aAAaT,S,WAAxB,QAIM,MAJN,EAIM,EAHJ,QAAqD,GAA5CjC,MAAM,cAAY,C,iBAAC,IAAe,EAAf,QAAe,M,qBAC3C,QAAc,SAAX,WAAO,KACV,QAA2E,GAAhEK,KAAK,UAAW,QAAK,eAAE6B,EAAAA,QAAQC,KAAK,a,kBAAY,IAAI,gB,QAAJ,W,qEAOnE,QAkBY,I,WAlBQ,EAAArB,e,qCAAA,EAAc,kBAAEW,MAAM,OAAOoB,MAAM,S,CAc1CC,QAAM,QACf,IAAyD,EAAzD,QAAyD,GAA7C,QAAK,eAAE,EAAAhC,gBAAiB,I,kBAAO,IAAE,gB,QAAF,S,eAC3C,QAAmF,GAAxET,KAAK,UAAW,QAAO,EAAA0C,cAAgBf,QAAS,EAAAgB,U,kBAAU,IAAE,gB,QAAF,S,yDAfvE,IAYU,EAZV,QAYU,IAZAC,MAAO,EAAAC,SAAWC,MAAO,EAAAC,UAAWC,IAAI,e,kBAChD,IAEe,EAFf,QAEe,IAFDrC,MAAM,KAAKsC,KAAK,Y,kBAC5B,IAA4D,EAA5D,QAA4D,I,WAAzC,EAAAJ,SAAS3C,S,qCAAT,EAAA2C,SAAiB,YAAEK,YAAY,S,gCAEpD,QAOe,IAPDvC,MAAM,OAAOsC,KAAK,O,kBAC9B,IAKE,EALF,QAKE,I,WAJS,EAAAJ,SAASzC,I,qCAAT,EAAAyC,SAAY,OACrB7C,KAAK,WACJmD,KAAM,EACPD,YAAY,c,qFAWpB,QAgBY,I,WAhBQ,EAAAjD,iB,uCAAA,EAAgB,oBAAEmB,MAAM,OAAOoB,MAAM,S,CAY5CC,QAAM,QACf,IAA2D,EAA3D,QAA2D,GAA/C,QAAK,iBAAE,EAAAxC,kBAAmB,I,kBAAO,IAAE,gB,QAAF,S,eAC7C,QAAkF,GAAvED,KAAK,UAAW,QAAO,EAAAoD,aAAezB,QAAS,EAAAgB,U,kBAAU,IAAE,gB,QAAF,S,yDAbtE,IAUM,EAVN,QAUM,MAVN,EAUM,G,aATJ,QAQM,mBANa,EAAAU,cAAVtD,K,WAFT,QAQM,OAPJJ,OAAK,SAAC,gBAAe,QAIH,EAAA2D,iBAAmBvD,KAFpCgB,IAAKhB,EACL,QAAK,GAAE,EAAAwD,aAAaxD,I,EAGrB,QAAsC,GAA1BH,KAAM,GAAKC,IAAKE,G,kFAetC,GACEa,KAAM,UACN4C,WAAY,CACVC,KAAI,OACJC,KAAI,OACJC,OAAM,SACNC,YAAW,eAEb,IAAAC,GACE,MAAO,CACLnD,UAAW,cACXiB,SAAS,EACTgB,UAAU,EACVlC,gBAAgB,EAChBR,kBAAkB,EAClBqD,eAAgB,GAChBzC,YAAa,GACbkB,QAAS,GACTM,aAAc,GACdhC,UAAW,CACTC,gBAAiB,EACjBC,YAAa,EACbC,aAAc,GAEhBqC,SAAU,CACR3C,SAAU,GACVE,IAAK,IAEP2C,UAAW,CACT7C,SAAU,CACR,CAAE4D,IAAK,GAAIC,QAAS,gBAAiBC,QAAS,SAEhD5D,IAAK,CACH,CAAE0D,IAAK,IAAKC,QAAS,iBAAkBC,QAAS,UAGpDX,cAAe,CACb,2DACA,2DACA,2DACA,2DACA,2DACA,4DAGN,EACAY,SAAU,KACL,QAAW,OAAQ,CAAC,WAAY,gBAErC,aAAMC,GACCC,KAAKC,YAKVD,KAAKE,qBACCF,KAAKG,gBALTH,KAAKtC,QAAQC,KAAK,SAMtB,EACAyC,QAAS,KACJ,QAAW,OAAQ,CAAC,uBACpB,QAAW,aAAc,CAAC,uBAAwB,uBAAwB,CAAEC,0BAA2B,8BACvG,QAAW,SAAU,CAAC,mBAAoB,qBAE7C,YAAAH,GACEF,KAAKtB,SAAS3C,SAAWiE,KAAKrE,UAAUI,UAAY,GACpDiE,KAAKtB,SAASzC,IAAM+D,KAAKrE,UAAUM,KAAO,EAC5C,EAEA,kBAAMkE,GACJH,KAAKxC,SAAU,EACf,UAGQwC,KAAKM,wBACLN,KAAKO,oBACLP,KAAKQ,yBACLR,KAAKS,eACb,CAAE,MAAOC,GACPC,QAAQD,MAAM,YAAaA,GAC3BV,KAAKY,SAASF,MAAM,SACtB,CAAE,QACAV,KAAKxC,SAAU,CACjB,CACF,EAEA,qBAAM8C,GACJ,IACE,MAAMO,QAAeb,KAAKc,qBAAqB,CAC7CC,KAAM,EACNtF,KAAM,KAEJoF,EAAOG,UACThB,KAAKtD,YAAcmE,EAAOnB,KAAKuB,OAEnC,CAAE,MAAOP,GACPC,QAAQD,MAAM,UAAWA,EAC3B,CACF,EAEA,iBAAMH,GACJ,IACE,MAAMM,QAAeb,KAAKkB,iBAAiB,CACzCH,KAAM,EACNtF,KAAM,KAEJoF,EAAOG,UACThB,KAAKpC,QAAUiD,EAAOnB,KAAK9B,QAE/B,CAAE,MAAO8C,GACPC,QAAQD,MAAM,UAAWA,EAC3B,CACF,EAEA,sBAAMF,GAEJR,KAAK9B,aAAe,EACtB,EAEA,mBAAMuC,GACJ,IACE,MAAOU,EAAkBC,SAAsBC,QAAQC,IAAI,CACzDtB,KAAKuB,uBACLvB,KAAKwB,qBAGPxB,KAAK9D,UAAY,CACfC,gBAAiBgF,EAAiBH,QAAUG,EAAiBM,MAAMC,iBAAmB,EACtFtF,YAAagF,EAAaJ,QAAUI,EAAaK,MAAME,aAAe,EACtEtF,aAAc,EAElB,CAAE,MAAOqE,GACPC,QAAQD,MAAM,YAAaA,EAC7B,CACF,EAEA,mBAAMnC,GACJ,UACQyB,KAAK4B,MAAMC,YAAYC,WAC7B9B,KAAKxB,UAAW,QAEVwB,KAAK+B,eAAe/B,KAAKtB,UAC/BsB,KAAKY,SAASI,QAAQ,UACtBhB,KAAK1D,gBAAiB,CACxB,CAAE,MAAOoE,GACPC,QAAQD,MAAM,UAAWA,GACzBV,KAAKY,SAASF,MAAM,OACtB,CAAE,QACAV,KAAKxB,UAAW,CAClB,CACF,EAEA,YAAAY,CAAaxD,GACXoE,KAAKb,eAAiBvD,CACxB,EAEA,kBAAMqD,GACJ,GAAKe,KAAKb,eAKV,IACEa,KAAKxB,UAAW,QACVwB,KAAK+B,eAAe,CAAEnG,OAAQoE,KAAKb,iBACzCa,KAAKY,SAASI,QAAQ,UACtBhB,KAAKlE,kBAAmB,CAC1B,CAAE,MAAO4E,GACPC,QAAQD,MAAM,UAAWA,GACzBV,KAAKY,SAASF,MAAM,OACtB,CAAE,QACAV,KAAKxB,UAAW,CAClB,MAdEwB,KAAKY,SAASoB,QAAQ,QAe1B,EAEA,SAAAlF,CAAUD,GACRmD,KAAKtC,QAAQC,KAAK,WAAWd,IAC/B,EAEA,0BAAMO,CAAqB6E,GACzB,UACQjC,KAAKK,0BAA0B4B,GACrCjC,KAAKY,SAASI,QAAQ,eAChBhB,KAAKM,wBACLN,KAAKS,eACb,CAAE,MAAOC,GACPC,QAAQD,MAAM,UAAWA,GACzBV,KAAKY,SAASF,MAAM,OACtB,CACF,EAEA,UAAA1C,CAAWkE,GACT,OAAO,IAAI5E,KAAK4E,GAAMC,mBAAmB,QAC3C,I,SCxVJ,MAAMC,GAA2B,OAAgB,EAAQ,CAAC,CAAC,SAASC,GAAQ,CAAC,YAAY,qBAEzF,O", "sources": ["webpack://movie-collection-frontend/./src/views/Profile.vue", "webpack://movie-collection-frontend/./src/views/Profile.vue?24e8"], "sourcesContent": ["<template>\n  <div class=\"profile-page\">\n    <div class=\"container\">\n      <!-- 用户信息卡片 -->\n      <div class=\"profile-header\">\n        <div class=\"user-avatar\">\n          <el-avatar :size=\"120\" :src=\"userInfo?.avatar\">\n            <el-icon><User /></el-icon>\n          </el-avatar>\n          <el-button type=\"text\" @click=\"showAvatarDialog = true\">更换头像</el-button>\n        </div>\n\n        <div class=\"user-info\">\n          <h2>{{ userInfo?.nickname || userInfo?.username || '用户' }}</h2>\n          <p class=\"username\">@{{ userInfo?.username || '' }}</p>\n          <p class=\"bio\">{{ userInfo?.bio || '这个人很懒，什么都没有留下...' }}</p>\n          <div class=\"user-stats\">\n            <div class=\"stat-item\">\n              <span class=\"stat-number\">{{ userStats.collectionCount }}</span>\n              <span class=\"stat-label\">收藏</span>\n            </div>\n            <div class=\"stat-item\">\n              <span class=\"stat-number\">{{ userStats.ratingCount }}</span>\n              <span class=\"stat-label\">评分</span>\n            </div>\n            <div class=\"stat-item\">\n              <span class=\"stat-number\">{{ userStats.commentCount }}</span>\n              <span class=\"stat-label\">评论</span>\n            </div>\n          </div>\n          <el-button type=\"primary\" @click=\"showEditDialog = true\">编辑资料</el-button>\n        </div>\n      </div>\n\n      <!-- 标签页 -->\n      <el-tabs v-model=\"activeTab\" class=\"profile-tabs\">\n        <el-tab-pane label=\"我的收藏\" name=\"collections\">\n          <div class=\"movies-grid\" v-loading=\"loading\">\n            <div class=\"movie-card\" v-for=\"movie in collections\" :key=\"movie.id\" @click=\"viewMovie(movie.id)\">\n              <div class=\"movie-poster\">\n                <img :src=\"movie.posterPath || '/placeholder.jpg'\" :alt=\"movie.title\" />\n                <div class=\"movie-overlay\">\n                  <div class=\"movie-rating\">\n                    <el-icon><Star /></el-icon>\n                    {{ movie.averageRating }}\n                  </div>\n                  <el-button\n                    type=\"danger\"\n                    size=\"small\"\n                    circle\n                    @click.stop=\"removeFromCollection(movie.id)\"\n                  >\n                    <el-icon><Delete /></el-icon>\n                  </el-button>\n                </div>\n              </div>\n              <div class=\"movie-info\">\n                <h4 class=\"movie-title\">{{ movie.title }}</h4>\n                <p class=\"movie-year\">{{ movie.releaseDate ? new Date(movie.releaseDate).getFullYear() : '未知' }}</p>\n              </div>\n            </div>\n          </div>\n          <div v-if=\"collections.length === 0\" class=\"empty-state\">\n            <el-icon class=\"empty-icon\"><Star /></el-icon>\n            <p>还没有收藏任何电影</p>\n            <el-button type=\"primary\" @click=\"$router.push('/movies')\">去发现电影</el-button>\n          </div>\n        </el-tab-pane>\n\n        <el-tab-pane label=\"我的评分\" name=\"ratings\">\n          <div class=\"ratings-list\" v-loading=\"loading\">\n            <div class=\"rating-item\" v-for=\"rating in ratings\" :key=\"rating.id\">\n              <div class=\"rating-movie\">\n                <img :src=\"rating.movie?.posterPath || '/placeholder.jpg'\" :alt=\"rating.movie?.title\" />\n                <div class=\"rating-info\">\n                  <h4>{{ rating.movie?.title }}</h4>\n                  <p>{{ rating.movie?.releaseDate ? new Date(rating.movie.releaseDate).getFullYear() : '未知' }}</p>\n                </div>\n              </div>\n              <div class=\"rating-score\">\n                <el-rate v-model=\"rating.score\" disabled show-score />\n                <p class=\"rating-date\">{{ formatDate(rating.createdAt) }}</p>\n              </div>\n            </div>\n          </div>\n          <div v-if=\"ratings.length === 0\" class=\"empty-state\">\n            <el-icon class=\"empty-icon\"><Star /></el-icon>\n            <p>还没有评分任何电影</p>\n            <el-button type=\"primary\" @click=\"$router.push('/movies')\">去评分电影</el-button>\n          </div>\n        </el-tab-pane>\n\n        <el-tab-pane label=\"观影历史\" name=\"history\">\n          <div class=\"history-list\" v-loading=\"loading\">\n            <div class=\"history-item\" v-for=\"history in watchHistory\" :key=\"history.id\">\n              <div class=\"history-movie\">\n                <img :src=\"history.movie?.posterPath || '/placeholder.jpg'\" :alt=\"history.movie?.title\" />\n                <div class=\"history-info\">\n                  <h4>{{ history.movie?.title }}</h4>\n                  <p>观看时间：{{ formatDate(history.watchedAt) }}</p>\n                </div>\n              </div>\n            </div>\n          </div>\n          <div v-if=\"watchHistory.length === 0\" class=\"empty-state\">\n            <el-icon class=\"empty-icon\"><VideoCamera /></el-icon>\n            <p>还没有观影记录</p>\n            <el-button type=\"primary\" @click=\"$router.push('/movies')\">去看电影</el-button>\n          </div>\n        </el-tab-pane>\n      </el-tabs>\n    </div>\n\n    <!-- 编辑资料对话框 -->\n    <el-dialog v-model=\"showEditDialog\" title=\"编辑资料\" width=\"500px\">\n      <el-form :model=\"editForm\" :rules=\"editRules\" ref=\"editFormRef\">\n        <el-form-item label=\"昵称\" prop=\"nickname\">\n          <el-input v-model=\"editForm.nickname\" placeholder=\"请输入昵称\" />\n        </el-form-item>\n        <el-form-item label=\"个人简介\" prop=\"bio\">\n          <el-input\n            v-model=\"editForm.bio\"\n            type=\"textarea\"\n            :rows=\"4\"\n            placeholder=\"介绍一下自己吧...\"\n          />\n        </el-form-item>\n      </el-form>\n      <template #footer>\n        <el-button @click=\"showEditDialog = false\">取消</el-button>\n        <el-button type=\"primary\" @click=\"updateProfile\" :loading=\"updating\">保存</el-button>\n      </template>\n    </el-dialog>\n\n    <!-- 更换头像对话框 -->\n    <el-dialog v-model=\"showAvatarDialog\" title=\"更换头像\" width=\"400px\">\n      <div class=\"avatar-options\">\n        <div\n          class=\"avatar-option\"\n          v-for=\"avatar in avatarOptions\"\n          :key=\"avatar\"\n          @click=\"selectAvatar(avatar)\"\n          :class=\"{ active: selectedAvatar === avatar }\"\n        >\n          <el-avatar :size=\"60\" :src=\"avatar\" />\n        </div>\n      </div>\n      <template #footer>\n        <el-button @click=\"showAvatarDialog = false\">取消</el-button>\n        <el-button type=\"primary\" @click=\"updateAvatar\" :loading=\"updating\">确定</el-button>\n      </template>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport { User, Star, Delete, VideoCamera } from '@element-plus/icons-vue'\nimport { mapGetters, mapActions } from 'vuex'\n\nexport default {\n  name: 'Profile',\n  components: {\n    User,\n    Star,\n    Delete,\n    VideoCamera\n  },\n  data() {\n    return {\n      activeTab: 'collections',\n      loading: false,\n      updating: false,\n      showEditDialog: false,\n      showAvatarDialog: false,\n      selectedAvatar: '',\n      collections: [],\n      ratings: [],\n      watchHistory: [],\n      userStats: {\n        collectionCount: 0,\n        ratingCount: 0,\n        commentCount: 0\n      },\n      editForm: {\n        nickname: '',\n        bio: ''\n      },\n      editRules: {\n        nickname: [\n          { max: 20, message: '昵称长度不能超过20个字符', trigger: 'blur' }\n        ],\n        bio: [\n          { max: 200, message: '个人简介不能超过200个字符', trigger: 'blur' }\n        ]\n      },\n      avatarOptions: [\n        'https://via.placeholder.com/120x120/409eff/ffffff?text=A',\n        'https://via.placeholder.com/120x120/67c23a/ffffff?text=B',\n        'https://via.placeholder.com/120x120/e6a23c/ffffff?text=C',\n        'https://via.placeholder.com/120x120/f56c6c/ffffff?text=D',\n        'https://via.placeholder.com/120x120/909399/ffffff?text=E',\n        'https://via.placeholder.com/120x120/9c27b0/ffffff?text=F'\n      ]\n    }\n  },\n  computed: {\n    ...mapGetters('user', ['userInfo', 'isLoggedIn'])\n  },\n  async mounted() {\n    if (!this.isLoggedIn) {\n      this.$router.push('/login')\n      return\n    }\n\n    this.initEditForm()\n    await this.loadUserData()\n  },\n  methods: {\n    ...mapActions('user', ['updateUserInfo']),\n    ...mapActions('collection', ['fetchUserCollections', 'fetchCollectionStats', { removeMovieFromCollection: 'removeFromCollection' }]),\n    ...mapActions('rating', ['fetchUserRatings', 'fetchRatingStats']),\n\n    initEditForm() {\n      this.editForm.nickname = this.userInfo?.nickname || ''\n      this.editForm.bio = this.userInfo?.bio || ''\n    },\n\n    async loadUserData() {\n      this.loading = true\n      try {\n        // TODO: 实现加载用户收藏、评分、观影历史的API\n        // 这里先用模拟数据\n        await this.loadCollections()\n        await this.loadRatings()\n        await this.loadWatchHistory()\n        await this.loadUserStats()\n      } catch (error) {\n        console.error('加载用户数据失败:', error)\n        this.$message.error('加载数据失败')\n      } finally {\n        this.loading = false\n      }\n    },\n\n    async loadCollections() {\n      try {\n        const result = await this.fetchUserCollections({\n          page: 0,\n          size: 20\n        })\n        if (result.success) {\n          this.collections = result.data.movies\n        }\n      } catch (error) {\n        console.error('加载收藏失败:', error)\n      }\n    },\n\n    async loadRatings() {\n      try {\n        const result = await this.fetchUserRatings({\n          page: 0,\n          size: 20\n        })\n        if (result.success) {\n          this.ratings = result.data.ratings\n        }\n      } catch (error) {\n        console.error('加载评分失败:', error)\n      }\n    },\n\n    async loadWatchHistory() {\n      // TODO: 实现观影历史功能\n      this.watchHistory = []\n    },\n\n    async loadUserStats() {\n      try {\n        const [collectionResult, ratingResult] = await Promise.all([\n          this.fetchCollectionStats(),\n          this.fetchRatingStats()\n        ])\n\n        this.userStats = {\n          collectionCount: collectionResult.success ? collectionResult.stats.totalCollections : 0,\n          ratingCount: ratingResult.success ? ratingResult.stats.totalRatings : 0,\n          commentCount: 0 // TODO: 实现评论统计\n        }\n      } catch (error) {\n        console.error('加载用户统计失败:', error)\n      }\n    },\n\n    async updateProfile() {\n      try {\n        await this.$refs.editFormRef.validate()\n        this.updating = true\n\n        await this.updateUserInfo(this.editForm)\n        this.$message.success('资料更新成功')\n        this.showEditDialog = false\n      } catch (error) {\n        console.error('更新资料失败:', error)\n        this.$message.error('更新失败')\n      } finally {\n        this.updating = false\n      }\n    },\n\n    selectAvatar(avatar) {\n      this.selectedAvatar = avatar\n    },\n\n    async updateAvatar() {\n      if (!this.selectedAvatar) {\n        this.$message.warning('请选择头像')\n        return\n      }\n\n      try {\n        this.updating = true\n        await this.updateUserInfo({ avatar: this.selectedAvatar })\n        this.$message.success('头像更新成功')\n        this.showAvatarDialog = false\n      } catch (error) {\n        console.error('更新头像失败:', error)\n        this.$message.error('更新失败')\n      } finally {\n        this.updating = false\n      }\n    },\n\n    viewMovie(id) {\n      this.$router.push(`/movies/${id}`)\n    },\n\n    async removeFromCollection(movieId) {\n      try {\n        await this.removeMovieFromCollection(movieId)\n        this.$message.success('已取消收藏')\n        await this.loadCollections()\n        await this.loadUserStats()\n      } catch (error) {\n        console.error('取消收藏失败:', error)\n        this.$message.error('操作失败')\n      }\n    },\n\n    formatDate(date) {\n      return new Date(date).toLocaleDateString('zh-CN')\n    }\n  }\n}\n</script>\n\n<style scoped>\n.profile-page {\n  min-height: 100vh;\n  background-color: #f5f5f5;\n  padding: 20px 0;\n}\n\n.container {\n  max-width: 1200px;\n  margin: 0 auto;\n  padding: 0 20px;\n}\n\n.profile-header {\n  background: white;\n  border-radius: 12px;\n  padding: 40px;\n  margin-bottom: 20px;\n  display: flex;\n  gap: 40px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n}\n\n.user-avatar {\n  text-align: center;\n}\n\n.user-avatar .el-button {\n  margin-top: 10px;\n  color: #409eff;\n}\n\n.user-info h2 {\n  font-size: 28px;\n  margin-bottom: 8px;\n  color: #333;\n}\n\n.username {\n  color: #666;\n  font-size: 16px;\n  margin-bottom: 12px;\n}\n\n.bio {\n  color: #666;\n  line-height: 1.6;\n  margin-bottom: 20px;\n}\n\n.user-stats {\n  display: flex;\n  gap: 30px;\n  margin-bottom: 20px;\n}\n\n.stat-item {\n  text-align: center;\n}\n\n.stat-number {\n  display: block;\n  font-size: 24px;\n  font-weight: bold;\n  color: #409eff;\n}\n\n.stat-label {\n  font-size: 14px;\n  color: #666;\n}\n\n.profile-tabs {\n  background: white;\n  border-radius: 12px;\n  padding: 20px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n}\n\n.movies-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));\n  gap: 20px;\n  margin-top: 20px;\n}\n\n.movie-card {\n  cursor: pointer;\n  transition: transform 0.3s;\n}\n\n.movie-card:hover {\n  transform: scale(1.05);\n}\n\n.movie-poster {\n  position: relative;\n  aspect-ratio: 2/3;\n  border-radius: 8px;\n  overflow: hidden;\n  margin-bottom: 12px;\n}\n\n.movie-poster img {\n  width: 100%;\n  height: 100%;\n  object-fit: cover;\n}\n\n.movie-overlay {\n  position: absolute;\n  top: 8px;\n  right: 8px;\n  display: flex;\n  flex-direction: column;\n  gap: 8px;\n}\n\n.movie-rating {\n  background: rgba(0, 0, 0, 0.7);\n  color: white;\n  padding: 4px 8px;\n  border-radius: 4px;\n  font-size: 12px;\n  display: flex;\n  align-items: center;\n  gap: 4px;\n}\n\n.movie-info {\n  text-align: center;\n}\n\n.movie-title {\n  font-size: 14px;\n  font-weight: 600;\n  margin-bottom: 4px;\n  color: #333;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n}\n\n.movie-year {\n  font-size: 12px;\n  color: #666;\n  margin: 0;\n}\n\n.ratings-list, .history-list {\n  margin-top: 20px;\n}\n\n.rating-item, .history-item {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  padding: 16px;\n  border: 1px solid #eee;\n  border-radius: 8px;\n  margin-bottom: 12px;\n}\n\n.rating-movie, .history-movie {\n  display: flex;\n  align-items: center;\n  gap: 16px;\n}\n\n.rating-movie img, .history-movie img {\n  width: 60px;\n  height: 90px;\n  object-fit: cover;\n  border-radius: 4px;\n}\n\n.rating-info h4, .history-info h4 {\n  margin: 0 0 4px 0;\n  font-size: 16px;\n  color: #333;\n}\n\n.rating-info p, .history-info p {\n  margin: 0;\n  font-size: 14px;\n  color: #666;\n}\n\n.rating-score {\n  text-align: right;\n}\n\n.rating-date {\n  margin-top: 8px;\n  font-size: 12px;\n  color: #999;\n}\n\n.empty-state {\n  text-align: center;\n  padding: 60px 20px;\n  color: #666;\n}\n\n.empty-icon {\n  font-size: 64px;\n  color: #ddd;\n  margin-bottom: 16px;\n}\n\n.avatar-options {\n  display: grid;\n  grid-template-columns: repeat(3, 1fr);\n  gap: 16px;\n  padding: 20px 0;\n}\n\n.avatar-option {\n  text-align: center;\n  padding: 16px;\n  border: 2px solid transparent;\n  border-radius: 8px;\n  cursor: pointer;\n  transition: all 0.3s;\n}\n\n.avatar-option:hover {\n  background-color: #f5f5f5;\n}\n\n.avatar-option.active {\n  border-color: #409eff;\n  background-color: #f0f9ff;\n}\n\n/* 响应式设计 */\n@media (max-width: 768px) {\n  .profile-header {\n    flex-direction: column;\n    text-align: center;\n    gap: 20px;\n  }\n\n  .user-stats {\n    justify-content: center;\n  }\n\n  .movies-grid {\n    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));\n  }\n\n  .rating-item, .history-item {\n    flex-direction: column;\n    align-items: flex-start;\n    gap: 12px;\n  }\n}\n</style>\n", "import { render } from \"./Profile.vue?vue&type=template&id=02c5282e&scoped=true\"\nimport script from \"./Profile.vue?vue&type=script&lang=js\"\nexport * from \"./Profile.vue?vue&type=script&lang=js\"\n\nimport \"./Profile.vue?vue&type=style&index=0&id=02c5282e&scoped=true&lang=css\"\n\nimport exportComponent from \"../../node_modules/vue-loader/dist/exportHelper.js\"\nconst __exports__ = /*#__PURE__*/exportComponent(script, [['render',render],['__scopeId',\"data-v-02c5282e\"]])\n\nexport default __exports__"], "names": ["class", "size", "src", "userInfo", "avatar", "type", "showAvatarDialog", "nickname", "username", "bio", "userStats", "collectionCount", "ratingCount", "commentCount", "showEditDialog", "activeTab", "label", "name", "collections", "movie", "key", "id", "viewMovie", "posterPath", "alt", "title", "averageRating", "circle", "removeFromCollection", "releaseDate", "Date", "getFullYear", "loading", "length", "$router", "push", "ratings", "rating", "score", "disabled", "formatDate", "createdAt", "watchHistory", "history", "watchedAt", "width", "footer", "updateProfile", "updating", "model", "editForm", "rules", "editRules", "ref", "prop", "placeholder", "rows", "updateAvatar", "avatarOptions", "<PERSON><PERSON><PERSON><PERSON>", "selectAvatar", "components", "User", "Star", "Delete", "VideoCamera", "data", "max", "message", "trigger", "computed", "mounted", "this", "isLoggedIn", "initEditForm", "loadUserData", "methods", "removeMovieFromCollection", "loadCollections", "loadRatings", "loadWatchHistory", "loadUserStats", "error", "console", "$message", "result", "fetchUserCollections", "page", "success", "movies", "fetchUserRatings", "collectionResult", "ratingResult", "Promise", "all", "fetchCollectionStats", "fetchRatingStats", "stats", "totalCollections", "totalRatings", "$refs", "editFormRef", "validate", "updateUserInfo", "warning", "movieId", "date", "toLocaleDateString", "__exports__", "render"], "sourceRoot": ""}
"use strict";(self["webpackChunkmovie_collection_frontend"]=self["webpackChunkmovie_collection_frontend"]||[]).push([[272],{272:function(e,t,a){a.r(t),a.d(t,{default:function(){return E}});var l=a(641),o=a(33),s=a(751);const i={class:"collections-page"},n={class:"container"},r={class:"page-header"},c={class:"header-content"},d={class:"header-actions"},h={class:"collections-content"},u={key:0,class:"movies-grid"},g=["onClick"],p={class:"movie-poster"},v=["src","alt"],m={class:"movie-overlay"},C={class:"movie-rating"},k={class:"movie-info"},y={class:"movie-title"},b={class:"movie-year"},f={class:"movie-director"},F={key:1,class:"empty-state"},S={key:0,class:"pagination-wrapper"};function w(e,t,a,w,_,L){const z=(0,l.g2)("Search"),D=(0,l.g2)("el-icon"),P=(0,l.g2)("el-input"),E=(0,l.g2)("el-option"),$=(0,l.g2)("el-select"),I=(0,l.g2)("Star"),V=(0,l.g2)("Delete"),K=(0,l.g2)("el-button"),B=(0,l.g2)("el-pagination"),U=(0,l.gN)("loading");return(0,l.uX)(),(0,l.CE)("div",i,[(0,l.Lk)("div",n,[(0,l.Lk)("div",r,[(0,l.Lk)("div",c,[t[6]||(t[6]=(0,l.Lk)("h1",null,"我的收藏",-1)),(0,l.Lk)("p",null,"共收藏了 "+(0,o.v_)(e.totalCollections)+" 部电影",1)]),(0,l.Lk)("div",d,[(0,l.bF)(P,{modelValue:_.searchKeyword,"onUpdate:modelValue":t[0]||(t[0]=e=>_.searchKeyword=e),placeholder:"搜索收藏的电影...",class:"search-input",onKeyup:(0,s.jR)(L.handleSearch,["enter"]),clearable:""},{prefix:(0,l.k6)(()=>[(0,l.bF)(D,null,{default:(0,l.k6)(()=>[(0,l.bF)(z)]),_:1})]),_:1},8,["modelValue","onKeyup"]),(0,l.bF)($,{modelValue:_.sortBy,"onUpdate:modelValue":t[1]||(t[1]=e=>_.sortBy=e),onChange:L.loadCollections,class:"sort-select"},{default:(0,l.k6)(()=>[(0,l.bF)(E,{label:"收藏时间",value:"createdAt"}),(0,l.bF)(E,{label:"电影名称",value:"title"}),(0,l.bF)(E,{label:"上映时间",value:"releaseDate"}),(0,l.bF)(E,{label:"评分",value:"averageRating"})]),_:1},8,["modelValue","onChange"]),(0,l.bF)($,{modelValue:_.sortDir,"onUpdate:modelValue":t[2]||(t[2]=e=>_.sortDir=e),onChange:L.loadCollections,class:"sort-select"},{default:(0,l.k6)(()=>[(0,l.bF)(E,{label:"降序",value:"desc"}),(0,l.bF)(E,{label:"升序",value:"asc"})]),_:1},8,["modelValue","onChange"])])]),(0,l.bo)(((0,l.uX)(),(0,l.CE)("div",h,[e.collections.length>0?((0,l.uX)(),(0,l.CE)("div",u,[((0,l.uX)(!0),(0,l.CE)(l.FK,null,(0,l.pI)(e.collections,e=>((0,l.uX)(),(0,l.CE)("div",{class:"movie-card",key:e.id,onClick:t=>L.viewMovie(e.id)},[(0,l.Lk)("div",p,[(0,l.Lk)("img",{src:e.posterPath||"/placeholder.jpg",alt:e.title},null,8,v),(0,l.Lk)("div",m,[(0,l.Lk)("div",C,[(0,l.bF)(D,null,{default:(0,l.k6)(()=>[(0,l.bF)(I)]),_:1}),(0,l.eW)(" "+(0,o.v_)(e.averageRating),1)]),(0,l.bF)(K,{type:"danger",size:"small",circle:"",onClick:(0,s.D$)(t=>L.removeFromCollection(e.id),["stop"]),loading:_.removingIds.includes(e.id)},{default:(0,l.k6)(()=>[(0,l.bF)(D,null,{default:(0,l.k6)(()=>[(0,l.bF)(V)]),_:1})]),_:2},1032,["onClick","loading"])])]),(0,l.Lk)("div",k,[(0,l.Lk)("h4",y,(0,o.v_)(e.title),1),(0,l.Lk)("p",b,(0,o.v_)(e.releaseDate?new Date(e.releaseDate).getFullYear():"未知"),1),(0,l.Lk)("p",f,(0,o.v_)(e.director||"未知导演"),1)])],8,g))),128))])):e.loading?(0,l.Q3)("",!0):((0,l.uX)(),(0,l.CE)("div",F,[(0,l.bF)(D,{class:"empty-icon"},{default:(0,l.k6)(()=>[(0,l.bF)(I)]),_:1}),t[8]||(t[8]=(0,l.Lk)("h3",null,"还没有收藏任何电影",-1)),t[9]||(t[9]=(0,l.Lk)("p",null,"去发现一些好电影吧！",-1)),(0,l.bF)(K,{type:"primary",onClick:t[3]||(t[3]=t=>e.$router.push("/movies"))},{default:(0,l.k6)(()=>[(0,l.bF)(D,null,{default:(0,l.k6)(()=>[(0,l.bF)(z)]),_:1}),t[7]||(t[7]=(0,l.eW)(" 浏览电影 "))]),_:1,__:[7]})]))])),[[U,e.loading]]),L.totalPages>1?((0,l.uX)(),(0,l.CE)("div",S,[(0,l.bF)(B,{"current-page":_.currentPage,"onUpdate:currentPage":t[4]||(t[4]=e=>_.currentPage=e),"page-size":_.pageSize,"onUpdate:pageSize":t[5]||(t[5]=e=>_.pageSize=e),"page-sizes":[12,24,48,96],total:L.totalElements,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:L.handleSizeChange,onCurrentChange:L.handleCurrentChange},null,8,["current-page","page-size","total","onSizeChange","onCurrentChange"])])):(0,l.Q3)("",!0)])])}var _=a(548),L=a(278),z={name:"Collections",components:{Search:_.Search,Star:_.Star,Delete:_.Delete},data(){return{searchKeyword:"",sortBy:"createdAt",sortDir:"desc",currentPage:1,pageSize:24,removingIds:[]}},computed:{...(0,L.L8)("collection",["collections","loading","pagination","totalCollections"]),...(0,L.L8)("user",["isLoggedIn"]),totalPages(){return this.pagination.totalPages},totalElements(){return this.pagination.totalElements}},async mounted(){this.isLoggedIn?(await this.loadCollections(),await this.loadCollectionStats()):this.$router.push("/login")},methods:{...(0,L.i0)("collection",["fetchUserCollections","fetchCollectionStats","removeFromCollection","searchCollections"]),async loadCollections(){try{await this.fetchUserCollections({page:this.currentPage-1,size:this.pageSize,sortBy:this.sortBy,sortDir:this.sortDir})}catch(e){console.error("加载收藏列表失败:",e),this.$message.error("加载收藏列表失败")}},async loadCollectionStats(){try{await this.fetchCollectionStats()}catch(e){console.error("加载收藏统计失败:",e)}},async handleSearch(){if(this.searchKeyword.trim())try{await this.searchCollections({keyword:this.searchKeyword.trim(),page:0,size:this.pageSize}),this.currentPage=1}catch(e){console.error("搜索收藏失败:",e),this.$message.error("搜索失败")}else await this.loadCollections()},async removeFromCollection(e){try{await this.$confirm("确定要取消收藏这部电影吗？","确认操作",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),this.removingIds.push(e),await this.removeFromCollection(e),this.$message.success("取消收藏成功"),await this.loadCollections(),await this.loadCollectionStats()}catch(t){"cancel"!==t&&(console.error("取消收藏失败:",t),this.$message.error("取消收藏失败"))}finally{const t=this.removingIds.indexOf(e);t>-1&&this.removingIds.splice(t,1)}},viewMovie(e){this.$router.push(`/movies/${e}`)},handleSizeChange(e){this.pageSize=e,this.currentPage=1,this.loadCollections()},handleCurrentChange(e){this.currentPage=e,this.loadCollections()}}},D=a(262);const P=(0,D.A)(z,[["render",w],["__scopeId","data-v-954b7d9e"]]);var E=P}}]);
//# sourceMappingURL=272.c2e045c8.js.map
{"version": 3, "file": "js/272.c2e045c8.js", "mappings": "oOACOA,MAAM,oB,GACJA,MAAM,a,GAEJA,MAAM,e,GACJA,MAAM,kB,GAKNA,MAAM,kB,GA4BRA,MAAM,uB,SAC0BA,MAAM,e,iBAOhCA,MAAM,gB,mBAEJA,MAAM,iB,GACJA,MAAM,gB,GAeVA,MAAM,c,GACLA,MAAM,e,GACPA,MAAM,c,GACNA,MAAM,kB,SAMWA,MAAM,e,SAY7BA,MAAM,sB,oRApFf,QAgGM,MAhGN,EAgGM,EA/FJ,QA8FM,MA9FN,EA8FM,EA5FJ,QA+BM,MA/BN,EA+BM,EA9BJ,QAGM,MAHN,EAGM,C,aAFJ,QAAa,UAAT,QAAI,KACR,QAAsC,SAAnC,SAAK,QAAGC,EAAAA,kBAAmB,OAAI,MAGpC,QAwBM,MAxBN,EAwBM,EAvBJ,QAUW,G,WATA,EAAAC,c,qCAAA,EAAa,iBACtBC,YAAY,aACZH,MAAM,eACL,SAAK,QAAQ,EAAAI,aAAY,WAC1BC,UAAA,I,CAEWC,QAAM,QACf,IAA6B,EAA7B,QAA6B,Q,iBAApB,IAAU,EAAV,QAAU,K,0CAIvB,QAKY,G,WALQ,EAAAC,O,qCAAA,EAAM,UAAG,SAAQ,EAAAC,gBAAiBR,MAAM,e,kBAC1D,IAA4C,EAA5C,QAA4C,GAAjCS,MAAM,OAAOC,MAAM,eAC9B,QAAwC,GAA7BD,MAAM,OAAOC,MAAM,WAC9B,QAA8C,GAAnCD,MAAM,OAAOC,MAAM,iBAC9B,QAA8C,GAAnCD,MAAM,KAAKC,MAAM,oB,mCAG9B,QAGY,G,WAHQ,EAAAC,Q,qCAAA,EAAO,WAAG,SAAQ,EAAAH,gBAAiBR,MAAM,e,kBAC3D,IAAqC,EAArC,QAAqC,GAA1BS,MAAM,KAAKC,MAAM,UAC5B,QAAoC,GAAzBD,MAAM,KAAKC,MAAM,U,4DAMlC,QA4CM,MA5CN,EA4CM,CA3COE,EAAAA,YAAYC,OAAS,I,WAAhC,QA+BM,MA/BN,EA+BM,G,aA9BJ,QA6BM,mBA3BYD,EAAAA,YAATE,K,WAFT,QA6BM,OA5BJd,MAAM,aAELe,IAAKD,EAAME,GACX,QAAK,GAAE,EAAAC,UAAUH,EAAME,K,EAExB,QAiBM,MAjBN,EAiBM,EAhBJ,QAAwE,OAAlEE,IAAKJ,EAAMK,YAAc,mBAAqBC,IAAKN,EAAMO,O,WAC/D,QAcM,MAdN,EAcM,EAbJ,QAGM,MAHN,EAGM,EAFJ,QAA2B,Q,iBAAlB,IAAQ,EAAR,QAAQ,K,eAAU,KAC3B,QAAGP,EAAMQ,eAAa,MAExB,QAQY,GAPVC,KAAK,SACLC,KAAK,QACLC,OAAA,GACC,SAAK,WAAO,EAAAC,qBAAqBZ,EAAME,IAAE,UACzCW,QAAS,EAAAC,YAAYC,SAASf,EAAME,K,kBAErC,IAA6B,EAA7B,QAA6B,Q,iBAApB,IAAU,EAAV,QAAU,K,8CAIzB,QAIM,MAJN,EAIM,EAHJ,QAA8C,KAA9C,GAA8C,QAAnBF,EAAMO,OAAK,IACtC,QAAoG,IAApG,GAAoG,QAA3EP,EAAMgB,YAAc,IAAIC,KAAKjB,EAAMgB,aAAaE,cAAgB,MAAL,IACpF,QAA4D,IAA5D,GAA4D,QAA/BlB,EAAMmB,UAAY,QAAJ,M,gBAMhCN,EAAAA,S,4BAAjB,QAQM,MARN,EAQM,EAPJ,QAA8C,GAArC3B,MAAM,cAAY,C,iBAAC,IAAQ,EAAR,QAAQ,K,mBACpC,QAAkB,UAAd,aAAS,I,aACb,QAAiB,SAAd,cAAU,KACb,QAGY,GAHDuB,KAAK,UAAW,QAAK,eAAEW,EAAAA,QAAQC,KAAK,a,kBAC7C,IAA6B,EAA7B,QAA6B,Q,iBAApB,IAAU,EAAV,QAAU,K,2BAAU,a,uBAxCSR,EAAAA,WA+CN,EAAAS,WAAa,I,WAAnD,QAUM,MAVN,EAUM,EATJ,QAQE,GAPQ,eAAc,EAAAC,Y,sCAAA,EAAW,eACzB,YAAW,EAAAC,S,mCAAA,EAAQ,YAC1B,aAAY,CAAC,GAAI,GAAI,GAAI,IACzBC,MAAO,EAAAC,cACRC,OAAO,0CACN,aAAa,EAAAC,iBACb,gBAAgB,EAAAC,qB,2HAW3B,GACEC,KAAM,cACNC,WAAY,CACVC,OAAM,SACNC,KAAI,OACJC,OAAM,UAER,IAAAC,GACE,MAAO,CACL/C,cAAe,GACfK,OAAQ,YACRI,QAAS,OACT0B,YAAa,EACbC,SAAU,GACVV,YAAa,GAEjB,EACAsB,SAAU,KACL,QAAW,aAAc,CAAC,cAAe,UAAW,aAAc,yBAClE,QAAW,OAAQ,CAAC,eAEvB,UAAAd,GACE,OAAOe,KAAKC,WAAWhB,UACzB,EAEA,aAAAI,GACE,OAAOW,KAAKC,WAAWZ,aACzB,GAEF,aAAMa,GACCF,KAAKG,kBAKJH,KAAK3C,wBACL2C,KAAKI,uBALTJ,KAAKjB,QAAQC,KAAK,SAMtB,EACAqB,QAAS,KACJ,QAAW,aAAc,CAC1B,uBACA,uBACA,uBACA,sBAGF,qBAAMhD,GACJ,UACQ2C,KAAKM,qBAAqB,CAC9BC,KAAMP,KAAKd,YAAc,EACzBb,KAAM2B,KAAKb,SACX/B,OAAQ4C,KAAK5C,OACbI,QAASwC,KAAKxC,SAElB,CAAE,MAAOgD,GACPC,QAAQD,MAAM,YAAaA,GAC3BR,KAAKU,SAASF,MAAM,WACtB,CACF,EAEA,yBAAMJ,GACJ,UACQJ,KAAKW,sBACb,CAAE,MAAOH,GACPC,QAAQD,MAAM,YAAaA,EAC7B,CACF,EAEA,kBAAMvD,GACJ,GAAI+C,KAAKjD,cAAc6D,OACrB,UACQZ,KAAKa,kBAAkB,CAC3BC,QAASd,KAAKjD,cAAc6D,OAC5BL,KAAM,EACNlC,KAAM2B,KAAKb,WAEba,KAAKd,YAAc,CACrB,CAAE,MAAOsB,GACPC,QAAQD,MAAM,UAAWA,GACzBR,KAAKU,SAASF,MAAM,OACtB,YAEMR,KAAK3C,iBAEf,EAEA,0BAAMkB,CAAqBwC,GACzB,UACQf,KAAKgB,SAAS,gBAAiB,OAAQ,CAC3CC,kBAAmB,KACnBC,iBAAkB,KAClB9C,KAAM,YAGR4B,KAAKvB,YAAYO,KAAK+B,SAEhBf,KAAKzB,qBAAqBwC,GAChCf,KAAKU,SAASS,QAAQ,gBAGhBnB,KAAK3C,wBACL2C,KAAKI,qBACb,CAAE,MAAOI,GACO,WAAVA,IACFC,QAAQD,MAAM,UAAWA,GACzBR,KAAKU,SAASF,MAAM,UAExB,CAAE,QACA,MAAMY,EAAQpB,KAAKvB,YAAY4C,QAAQN,GACnCK,GAAS,GACXpB,KAAKvB,YAAY6C,OAAOF,EAAO,EAEnC,CACF,EAEA,SAAAtD,CAAUD,GACRmC,KAAKjB,QAAQC,KAAK,WAAWnB,IAC/B,EAEA,gBAAA0B,CAAiBgC,GACfvB,KAAKb,SAAWoC,EAChBvB,KAAKd,YAAc,EACnBc,KAAK3C,iBACP,EAEA,mBAAAmC,CAAoBgC,GAClBxB,KAAKd,YAAcsC,EACnBxB,KAAK3C,iBACP,I,SCjOJ,MAAMoE,GAA2B,OAAgB,EAAQ,CAAC,CAAC,SAASC,GAAQ,CAAC,YAAY,qBAEzF,O", "sources": ["webpack://movie-collection-frontend/./src/views/Collections.vue", "webpack://movie-collection-frontend/./src/views/Collections.vue?31bb"], "sourcesContent": ["<template>\n  <div class=\"collections-page\">\n    <div class=\"container\">\n      <!-- 页面头部 -->\n      <div class=\"page-header\">\n        <div class=\"header-content\">\n          <h1>我的收藏</h1>\n          <p>共收藏了 {{ totalCollections }} 部电影</p>\n        </div>\n\n        <div class=\"header-actions\">\n          <el-input\n            v-model=\"searchKeyword\"\n            placeholder=\"搜索收藏的电影...\"\n            class=\"search-input\"\n            @keyup.enter=\"handleSearch\"\n            clearable\n          >\n            <template #prefix>\n              <el-icon><Search /></el-icon>\n            </template>\n          </el-input>\n\n          <el-select v-model=\"sortBy\" @change=\"loadCollections\" class=\"sort-select\">\n            <el-option label=\"收藏时间\" value=\"createdAt\" />\n            <el-option label=\"电影名称\" value=\"title\" />\n            <el-option label=\"上映时间\" value=\"releaseDate\" />\n            <el-option label=\"评分\" value=\"averageRating\" />\n          </el-select>\n\n          <el-select v-model=\"sortDir\" @change=\"loadCollections\" class=\"sort-select\">\n            <el-option label=\"降序\" value=\"desc\" />\n            <el-option label=\"升序\" value=\"asc\" />\n          </el-select>\n        </div>\n      </div>\n\n      <!-- 收藏列表 -->\n      <div class=\"collections-content\" v-loading=\"loading\">\n        <div v-if=\"collections.length > 0\" class=\"movies-grid\">\n          <div\n            class=\"movie-card\"\n            v-for=\"movie in collections\"\n            :key=\"movie.id\"\n            @click=\"viewMovie(movie.id)\"\n          >\n            <div class=\"movie-poster\">\n              <img :src=\"movie.posterPath || '/placeholder.jpg'\" :alt=\"movie.title\" />\n              <div class=\"movie-overlay\">\n                <div class=\"movie-rating\">\n                  <el-icon><Star /></el-icon>\n                  {{ movie.averageRating }}\n                </div>\n                <el-button\n                  type=\"danger\"\n                  size=\"small\"\n                  circle\n                  @click.stop=\"removeFromCollection(movie.id)\"\n                  :loading=\"removingIds.includes(movie.id)\"\n                >\n                  <el-icon><Delete /></el-icon>\n                </el-button>\n              </div>\n            </div>\n            <div class=\"movie-info\">\n              <h4 class=\"movie-title\">{{ movie.title }}</h4>\n              <p class=\"movie-year\">{{ movie.releaseDate ? new Date(movie.releaseDate).getFullYear() : '未知' }}</p>\n              <p class=\"movie-director\">{{ movie.director || '未知导演' }}</p>\n            </div>\n          </div>\n        </div>\n\n        <!-- 空状态 -->\n        <div v-else-if=\"!loading\" class=\"empty-state\">\n          <el-icon class=\"empty-icon\"><Star /></el-icon>\n          <h3>还没有收藏任何电影</h3>\n          <p>去发现一些好电影吧！</p>\n          <el-button type=\"primary\" @click=\"$router.push('/movies')\">\n            <el-icon><Search /></el-icon>\n            浏览电影\n          </el-button>\n        </div>\n      </div>\n\n      <!-- 分页 -->\n      <div class=\"pagination-wrapper\" v-if=\"totalPages > 1\">\n        <el-pagination\n          v-model:current-page=\"currentPage\"\n          v-model:page-size=\"pageSize\"\n          :page-sizes=\"[12, 24, 48, 96]\"\n          :total=\"totalElements\"\n          layout=\"total, sizes, prev, pager, next, jumper\"\n          @size-change=\"handleSizeChange\"\n          @current-change=\"handleCurrentChange\"\n        />\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport { Search, Star, Delete } from '@element-plus/icons-vue'\nimport { mapGetters, mapActions } from 'vuex'\n\nexport default {\n  name: 'Collections',\n  components: {\n    Search,\n    Star,\n    Delete\n  },\n  data() {\n    return {\n      searchKeyword: '',\n      sortBy: 'createdAt',\n      sortDir: 'desc',\n      currentPage: 1,\n      pageSize: 24,\n      removingIds: [] // 正在移除的电影ID列表\n    }\n  },\n  computed: {\n    ...mapGetters('collection', ['collections', 'loading', 'pagination', 'totalCollections']),\n    ...mapGetters('user', ['isLoggedIn']),\n\n    totalPages() {\n      return this.pagination.totalPages\n    },\n\n    totalElements() {\n      return this.pagination.totalElements\n    }\n  },\n  async mounted() {\n    if (!this.isLoggedIn) {\n      this.$router.push('/login')\n      return\n    }\n\n    await this.loadCollections()\n    await this.loadCollectionStats()\n  },\n  methods: {\n    ...mapActions('collection', [\n      'fetchUserCollections',\n      'fetchCollectionStats',\n      'removeFromCollection',\n      'searchCollections'\n    ]),\n\n    async loadCollections() {\n      try {\n        await this.fetchUserCollections({\n          page: this.currentPage - 1,\n          size: this.pageSize,\n          sortBy: this.sortBy,\n          sortDir: this.sortDir\n        })\n      } catch (error) {\n        console.error('加载收藏列表失败:', error)\n        this.$message.error('加载收藏列表失败')\n      }\n    },\n\n    async loadCollectionStats() {\n      try {\n        await this.fetchCollectionStats()\n      } catch (error) {\n        console.error('加载收藏统计失败:', error)\n      }\n    },\n\n    async handleSearch() {\n      if (this.searchKeyword.trim()) {\n        try {\n          await this.searchCollections({\n            keyword: this.searchKeyword.trim(),\n            page: 0,\n            size: this.pageSize\n          })\n          this.currentPage = 1\n        } catch (error) {\n          console.error('搜索收藏失败:', error)\n          this.$message.error('搜索失败')\n        }\n      } else {\n        await this.loadCollections()\n      }\n    },\n\n    async removeFromCollection(movieId) {\n      try {\n        await this.$confirm('确定要取消收藏这部电影吗？', '确认操作', {\n          confirmButtonText: '确定',\n          cancelButtonText: '取消',\n          type: 'warning'\n        })\n\n        this.removingIds.push(movieId)\n\n        await this.removeFromCollection(movieId)\n        this.$message.success('取消收藏成功')\n\n        // 重新加载数据\n        await this.loadCollections()\n        await this.loadCollectionStats()\n      } catch (error) {\n        if (error !== 'cancel') {\n          console.error('取消收藏失败:', error)\n          this.$message.error('取消收藏失败')\n        }\n      } finally {\n        const index = this.removingIds.indexOf(movieId)\n        if (index > -1) {\n          this.removingIds.splice(index, 1)\n        }\n      }\n    },\n\n    viewMovie(id) {\n      this.$router.push(`/movies/${id}`)\n    },\n\n    handleSizeChange(newSize) {\n      this.pageSize = newSize\n      this.currentPage = 1\n      this.loadCollections()\n    },\n\n    handleCurrentChange(newPage) {\n      this.currentPage = newPage\n      this.loadCollections()\n    }\n  }\n}\n</script>\n\n<style scoped>\n.collections-page {\n  min-height: 100vh;\n  background-color: #f5f5f5;\n  padding: 20px 0;\n}\n\n.container {\n  max-width: 1200px;\n  margin: 0 auto;\n  padding: 0 20px;\n}\n\n.page-header {\n  background: white;\n  border-radius: 12px;\n  padding: 30px;\n  margin-bottom: 20px;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n}\n\n.header-content h1 {\n  font-size: 28px;\n  color: #333;\n  margin-bottom: 8px;\n}\n\n.header-content p {\n  color: #666;\n  font-size: 16px;\n}\n\n.header-actions {\n  display: flex;\n  gap: 12px;\n  align-items: center;\n}\n\n.search-input {\n  width: 250px;\n}\n\n.sort-select {\n  width: 120px;\n}\n\n.collections-content {\n  background: white;\n  border-radius: 12px;\n  padding: 30px;\n  min-height: 400px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n}\n\n.movies-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));\n  gap: 20px;\n}\n\n.movie-card {\n  cursor: pointer;\n  transition: transform 0.3s;\n  border-radius: 8px;\n  overflow: hidden;\n}\n\n.movie-card:hover {\n  transform: scale(1.05);\n}\n\n.movie-poster {\n  position: relative;\n  aspect-ratio: 2/3;\n  overflow: hidden;\n}\n\n.movie-poster img {\n  width: 100%;\n  height: 100%;\n  object-fit: cover;\n}\n\n.movie-overlay {\n  position: absolute;\n  top: 8px;\n  right: 8px;\n  display: flex;\n  flex-direction: column;\n  gap: 8px;\n}\n\n.movie-rating {\n  background: rgba(0, 0, 0, 0.7);\n  color: white;\n  padding: 4px 8px;\n  border-radius: 4px;\n  font-size: 12px;\n  display: flex;\n  align-items: center;\n  gap: 4px;\n}\n\n.movie-info {\n  padding: 12px;\n  text-align: center;\n}\n\n.movie-title {\n  font-size: 14px;\n  font-weight: 600;\n  margin-bottom: 4px;\n  color: #333;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n}\n\n.movie-year, .movie-director {\n  font-size: 12px;\n  color: #666;\n  margin: 2px 0;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n}\n\n.empty-state {\n  text-align: center;\n  padding: 80px 20px;\n  color: #666;\n}\n\n.empty-icon {\n  font-size: 80px;\n  color: #ddd;\n  margin-bottom: 20px;\n}\n\n.empty-state h3 {\n  font-size: 20px;\n  margin-bottom: 12px;\n  color: #333;\n}\n\n.empty-state p {\n  font-size: 16px;\n  margin-bottom: 24px;\n}\n\n.pagination-wrapper {\n  display: flex;\n  justify-content: center;\n  margin-top: 30px;\n}\n\n/* 响应式设计 */\n@media (max-width: 768px) {\n  .page-header {\n    flex-direction: column;\n    gap: 20px;\n    text-align: center;\n  }\n\n  .header-actions {\n    flex-wrap: wrap;\n    justify-content: center;\n  }\n\n  .search-input {\n    width: 200px;\n  }\n\n  .movies-grid {\n    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));\n    gap: 15px;\n  }\n\n  .collections-content {\n    padding: 20px;\n  }\n}\n</style>\n", "import { render } from \"./Collections.vue?vue&type=template&id=954b7d9e&scoped=true\"\nimport script from \"./Collections.vue?vue&type=script&lang=js\"\nexport * from \"./Collections.vue?vue&type=script&lang=js\"\n\nimport \"./Collections.vue?vue&type=style&index=0&id=954b7d9e&scoped=true&lang=css\"\n\nimport exportComponent from \"../../node_modules/vue-loader/dist/exportHelper.js\"\nconst __exports__ = /*#__PURE__*/exportComponent(script, [['render',render],['__scopeId',\"data-v-954b7d9e\"]])\n\nexport default __exports__"], "names": ["class", "totalCollections", "searchKeyword", "placeholder", "handleSearch", "clearable", "prefix", "sortBy", "loadCollections", "label", "value", "sortDir", "collections", "length", "movie", "key", "id", "viewMovie", "src", "posterPath", "alt", "title", "averageRating", "type", "size", "circle", "removeFromCollection", "loading", "removingIds", "includes", "releaseDate", "Date", "getFullYear", "director", "$router", "push", "totalPages", "currentPage", "pageSize", "total", "totalElements", "layout", "handleSizeChange", "handleCurrentChange", "name", "components", "Search", "Star", "Delete", "data", "computed", "this", "pagination", "mounted", "isLoggedIn", "loadCollectionStats", "methods", "fetchUserCollections", "page", "error", "console", "$message", "fetchCollectionStats", "trim", "searchCollections", "keyword", "movieId", "$confirm", "confirmButtonText", "cancelButtonText", "success", "index", "indexOf", "splice", "newSize", "newPage", "__exports__", "render"], "sourceRoot": ""}
"use strict";(self["webpackChunkmovie_collection_frontend"]=self["webpackChunkmovie_collection_frontend"]||[]).push([[274],{274:function(e,l,a){a.r(l),a.d(l,{default:function(){return L}});var n=a(641),o=a(751),s=a(33);const i={class:"login-page"},r={class:"login-container"},t={class:"login-form"},u={class:"form-footer"},d={class:"login-banner"},g={class:"banner-content"},c={class:"feature-list"};function m(e,l,a,m,k,p){const f=(0,n.g2)("el-input"),h=(0,n.g2)("el-form-item"),b=(0,n.g2)("el-button"),L=(0,n.g2)("el-form"),_=(0,n.g2)("router-link"),F=(0,n.g2)("Check"),v=(0,n.g2)("el-icon");return(0,n.uX)(),(0,n.CE)("div",i,[(0,n.Lk)("div",r,[(0,n.Lk)("div",t,[l[4]||(l[4]=(0,n.Lk)("div",{class:"form-header"},[(0,n.Lk)("h2",null,"登录"),(0,n.Lk)("p",null,"欢迎回到电影收藏管理系统")],-1)),(0,n.bF)(L,{ref:"loginForm",model:k.loginData,rules:k.loginRules,onSubmit:(0,o.D$)(p.handleLogin,["prevent"])},{default:(0,n.k6)(()=>[(0,n.bF)(h,{prop:"usernameOrEmail"},{default:(0,n.k6)(()=>[(0,n.bF)(f,{modelValue:k.loginData.usernameOrEmail,"onUpdate:modelValue":l[0]||(l[0]=e=>k.loginData.usernameOrEmail=e),placeholder:"请输入用户名或邮箱",size:"large","prefix-icon":"User"},null,8,["modelValue"])]),_:1}),(0,n.bF)(h,{prop:"password"},{default:(0,n.k6)(()=>[(0,n.bF)(f,{modelValue:k.loginData.password,"onUpdate:modelValue":l[1]||(l[1]=e=>k.loginData.password=e),type:"password",placeholder:"请输入密码",size:"large","prefix-icon":"Lock","show-password":"",onKeyup:(0,o.jR)(p.handleLogin,["enter"])},null,8,["modelValue","onKeyup"])]),_:1}),(0,n.bF)(h,null,{default:(0,n.k6)(()=>[(0,n.bF)(b,{type:"primary",size:"large",loading:k.loading,onClick:p.handleLogin,class:"login-button"},{default:(0,n.k6)(()=>[(0,n.eW)((0,s.v_)(k.loading?"登录中...":"登录"),1)]),_:1},8,["loading","onClick"])]),_:1})]),_:1},8,["model","rules","onSubmit"]),(0,n.Lk)("div",u,[(0,n.Lk)("p",null,[l[3]||(l[3]=(0,n.eW)(" 还没有账号？ ")),(0,n.bF)(_,{to:"/register",class:"link"},{default:(0,n.k6)(()=>l[2]||(l[2]=[(0,n.eW)("立即注册")])),_:1,__:[2]})])])]),(0,n.Lk)("div",d,[(0,n.Lk)("div",g,[l[9]||(l[9]=(0,n.Lk)("h3",null,"发现好电影",-1)),l[10]||(l[10]=(0,n.Lk)("p",null,"记录你的观影时光，分享电影感受",-1)),(0,n.Lk)("ul",c,[(0,n.Lk)("li",null,[(0,n.bF)(v,null,{default:(0,n.k6)(()=>[(0,n.bF)(F)]),_:1}),l[5]||(l[5]=(0,n.eW)(" 海量电影资源"))]),(0,n.Lk)("li",null,[(0,n.bF)(v,null,{default:(0,n.k6)(()=>[(0,n.bF)(F)]),_:1}),l[6]||(l[6]=(0,n.eW)(" 个人收藏管理"))]),(0,n.Lk)("li",null,[(0,n.bF)(v,null,{default:(0,n.k6)(()=>[(0,n.bF)(F)]),_:1}),l[7]||(l[7]=(0,n.eW)(" 评分评论系统"))]),(0,n.Lk)("li",null,[(0,n.bF)(v,null,{default:(0,n.k6)(()=>[(0,n.bF)(F)]),_:1}),l[8]||(l[8]=(0,n.eW)(" 智能推荐算法"))])])])])])])}var k=a(548),p=a(278),f={name:"Login",components:{User:k.User,Lock:k.Lock,Check:k.Check},data(){return{loading:!1,loginData:{usernameOrEmail:"",password:""},loginRules:{usernameOrEmail:[{required:!0,message:"请输入用户名或邮箱",trigger:"blur"}],password:[{required:!0,message:"请输入密码",trigger:"blur"},{min:6,message:"密码长度不能少于6位",trigger:"blur"}]}}},methods:{...(0,p.i0)("user",["login"]),async handleLogin(){try{const e=await this.$refs.loginForm.validate();if(!e)return;this.loading=!0;const l=await this.login({usernameOrEmail:this.loginData.usernameOrEmail,password:this.loginData.password});if(l.success){this.$message.success("登录成功");const e=this.$route.query.redirect||"/";this.$router.push(e)}}catch(e){this.$message.error(e.message||"登录失败")}finally{this.loading=!1}}}},h=a(262);const b=(0,h.A)(f,[["render",m],["__scopeId","data-v-918e50a6"]]);var L=b}}]);
//# sourceMappingURL=274.865368db.js.map
{"version": 3, "file": "js/274.865368db.js", "mappings": "oOACOA,MAAM,c,GACJA,MAAM,mB,GACJA,MAAM,c,GA8CJA,MAAM,e,GAQRA,MAAM,gB,GACJA,MAAM,kB,GAGLA,MAAM,gB,oNA5DlB,QAqEM,MArEN,EAqEM,EApEJ,QAmEM,MAnEN,EAmEM,EAlEJ,QAoDM,MApDN,EAoDM,C,aAnDJ,QAGM,OAHDA,MAAM,eAAa,EACtB,QAAW,UAAP,OACJ,QAAmB,SAAhB,kB,KAGL,QAsCU,GArCRC,IAAI,YACHC,MAAO,EAAAC,UACPC,MAAO,EAAAC,WACP,UAAM,QAAU,EAAAC,YAAW,c,kBAE5B,IAOe,EAPf,QAOe,GAPDC,KAAK,mBAAiB,C,iBAClC,IAKE,EALF,QAKE,G,WAJS,EAAAJ,UAAUK,gB,qCAAV,EAAAL,UAAyB,mBAClCM,YAAY,YACZC,KAAK,QACL,cAAY,Q,gCAIhB,QAUe,GAVDH,KAAK,YAAU,C,iBAC3B,IAQE,EARF,QAQE,G,WAPS,EAAAJ,UAAUQ,S,qCAAV,EAAAR,UAAkB,YAC3BS,KAAK,WACLH,YAAY,QACZC,KAAK,QACL,cAAY,OACZ,mBACC,SAAK,QAAQ,EAAAJ,YAAW,Y,0CAI7B,QAUe,Q,iBATb,IAQY,EARZ,QAQY,GAPVM,KAAK,UACLF,KAAK,QACJG,QAAS,EAAAA,QACT,QAAO,EAAAP,YACRN,MAAM,gB,kBAEN,IAA+B,E,iBAA5B,EAAAa,QAAU,SAAW,MAAd,K,8EAKhB,QAKM,MALN,EAKM,EAJJ,QAGI,U,qBAHD,cAED,QAA2D,GAA9CC,GAAG,YAAYd,MAAM,Q,kBAAO,IAAI,c,QAAJ,W,oBAK/C,QAWM,MAXN,EAWM,EAVJ,QASM,MATN,EASM,C,aARJ,QAAc,UAAV,SAAK,I,eACT,QAAsB,SAAnB,mBAAe,KAClB,QAKK,KALL,EAKK,EAJH,QAA4C,YAAxC,QAA4B,Q,iBAAnB,IAAS,EAAT,QAAS,K,2BAAU,eAChC,QAA4C,YAAxC,QAA4B,Q,iBAAnB,IAAS,EAAT,QAAS,K,2BAAU,eAChC,QAA4C,YAAxC,QAA4B,Q,iBAAnB,IAAS,EAAT,QAAS,K,2BAAU,eAChC,QAA4C,YAAxC,QAA4B,Q,iBAAnB,IAAS,EAAT,QAAS,K,2BAAU,uB,uBAY5C,GACEe,KAAM,QACNC,WAAY,CACVC,KAAI,OACJC,KAAI,OACJC,MAAK,SAEP,IAAAC,GACE,MAAO,CACLP,SAAS,EACTV,UAAW,CACTK,gBAAiB,GACjBG,SAAU,IAEZN,WAAY,CACVG,gBAAiB,CACf,CAAEa,UAAU,EAAMC,QAAS,YAAaC,QAAS,SAEnDZ,SAAU,CACR,CAAEU,UAAU,EAAMC,QAAS,QAASC,QAAS,QAC7C,CAAEC,IAAK,EAAGF,QAAS,aAAcC,QAAS,UAIlD,EACAE,QAAS,KACJ,QAAW,OAAQ,CAAC,UAEvB,iBAAMnB,GACJ,IACE,MAAMoB,QAAcC,KAAKC,MAAMC,UAAUC,WACzC,IAAKJ,EAAO,OAEZC,KAAKd,SAAU,EAEf,MAAMkB,QAAeJ,KAAKK,MAAM,CAC9BxB,gBAAiBmB,KAAKxB,UAAUK,gBAChCG,SAAUgB,KAAKxB,UAAUQ,WAG3B,GAAIoB,EAAOE,QAAS,CAClBN,KAAKO,SAASD,QAAQ,QAGtB,MAAME,EAAWR,KAAKS,OAAOC,MAAMF,UAAY,IAC/CR,KAAKW,QAAQC,KAAKJ,EACpB,CACF,CAAE,MAAOK,GACPb,KAAKO,SAASM,MAAMA,EAAMlB,SAAW,OACvC,CAAE,QACAK,KAAKd,SAAU,CACjB,CACF,I,SC1HJ,MAAM4B,GAA2B,OAAgB,EAAQ,CAAC,CAAC,SAASC,GAAQ,CAAC,YAAY,qBAEzF,O", "sources": ["webpack://movie-collection-frontend/./src/views/Login.vue", "webpack://movie-collection-frontend/./src/views/Login.vue?240b"], "sourcesContent": ["<template>\n  <div class=\"login-page\">\n    <div class=\"login-container\">\n      <div class=\"login-form\">\n        <div class=\"form-header\">\n          <h2>登录</h2>\n          <p>欢迎回到电影收藏管理系统</p>\n        </div>\n\n        <el-form\n          ref=\"loginForm\"\n          :model=\"loginData\"\n          :rules=\"loginRules\"\n          @submit.prevent=\"handleLogin\"\n        >\n          <el-form-item prop=\"usernameOrEmail\">\n            <el-input\n              v-model=\"loginData.usernameOrEmail\"\n              placeholder=\"请输入用户名或邮箱\"\n              size=\"large\"\n              prefix-icon=\"User\"\n            />\n          </el-form-item>\n\n          <el-form-item prop=\"password\">\n            <el-input\n              v-model=\"loginData.password\"\n              type=\"password\"\n              placeholder=\"请输入密码\"\n              size=\"large\"\n              prefix-icon=\"Lock\"\n              show-password\n              @keyup.enter=\"handleLogin\"\n            />\n          </el-form-item>\n\n          <el-form-item>\n            <el-button\n              type=\"primary\"\n              size=\"large\"\n              :loading=\"loading\"\n              @click=\"handleLogin\"\n              class=\"login-button\"\n            >\n              {{ loading ? '登录中...' : '登录' }}\n            </el-button>\n          </el-form-item>\n        </el-form>\n\n        <div class=\"form-footer\">\n          <p>\n            还没有账号？\n            <router-link to=\"/register\" class=\"link\">立即注册</router-link>\n          </p>\n        </div>\n      </div>\n\n      <div class=\"login-banner\">\n        <div class=\"banner-content\">\n          <h3>发现好电影</h3>\n          <p>记录你的观影时光，分享电影感受</p>\n          <ul class=\"feature-list\">\n            <li><el-icon><Check /></el-icon> 海量电影资源</li>\n            <li><el-icon><Check /></el-icon> 个人收藏管理</li>\n            <li><el-icon><Check /></el-icon> 评分评论系统</li>\n            <li><el-icon><Check /></el-icon> 智能推荐算法</li>\n          </ul>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport { User, Lock, Check } from '@element-plus/icons-vue'\nimport { mapActions } from 'vuex'\n\nexport default {\n  name: 'Login',\n  components: {\n    User,\n    Lock,\n    Check\n  },\n  data() {\n    return {\n      loading: false,\n      loginData: {\n        usernameOrEmail: '',\n        password: ''\n      },\n      loginRules: {\n        usernameOrEmail: [\n          { required: true, message: '请输入用户名或邮箱', trigger: 'blur' }\n        ],\n        password: [\n          { required: true, message: '请输入密码', trigger: 'blur' },\n          { min: 6, message: '密码长度不能少于6位', trigger: 'blur' }\n        ]\n      }\n    }\n  },\n  methods: {\n    ...mapActions('user', ['login']),\n\n    async handleLogin() {\n      try {\n        const valid = await this.$refs.loginForm.validate()\n        if (!valid) return\n\n        this.loading = true\n\n        const result = await this.login({\n          usernameOrEmail: this.loginData.usernameOrEmail,\n          password: this.loginData.password\n        })\n\n        if (result.success) {\n          this.$message.success('登录成功')\n          \n          // 获取重定向路径，默认跳转到首页\n          const redirect = this.$route.query.redirect || '/'\n          this.$router.push(redirect)\n        }\n      } catch (error) {\n        this.$message.error(error.message || '登录失败')\n      } finally {\n        this.loading = false\n      }\n    }\n  }\n}\n</script>\n\n<style scoped>\n.login-page {\n  min-height: 100vh;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  padding: 20px;\n}\n\n.login-container {\n  background: white;\n  border-radius: 12px;\n  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);\n  overflow: hidden;\n  display: grid;\n  grid-template-columns: 1fr 1fr;\n  max-width: 900px;\n  width: 100%;\n  min-height: 500px;\n}\n\n.login-form {\n  padding: 60px 40px;\n  display: flex;\n  flex-direction: column;\n  justify-content: center;\n}\n\n.form-header {\n  text-align: center;\n  margin-bottom: 40px;\n}\n\n.form-header h2 {\n  font-size: 28px;\n  font-weight: bold;\n  color: #333;\n  margin-bottom: 8px;\n}\n\n.form-header p {\n  color: #666;\n  font-size: 14px;\n}\n\n.login-button {\n  width: 100%;\n  height: 48px;\n  font-size: 16px;\n  font-weight: 600;\n}\n\n.form-footer {\n  text-align: center;\n  margin-top: 20px;\n}\n\n.form-footer p {\n  color: #666;\n  font-size: 14px;\n}\n\n.link {\n  color: #409eff;\n  text-decoration: none;\n  font-weight: 600;\n}\n\n.link:hover {\n  text-decoration: underline;\n}\n\n.login-banner {\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n  padding: 60px 40px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.banner-content {\n  text-align: center;\n}\n\n.banner-content h3 {\n  font-size: 32px;\n  font-weight: bold;\n  margin-bottom: 16px;\n}\n\n.banner-content p {\n  font-size: 16px;\n  margin-bottom: 40px;\n  opacity: 0.9;\n}\n\n.feature-list {\n  list-style: none;\n  padding: 0;\n  margin: 0;\n}\n\n.feature-list li {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  gap: 8px;\n  margin-bottom: 16px;\n  font-size: 16px;\n}\n\n.feature-list li:last-child {\n  margin-bottom: 0;\n}\n\n/* 响应式设计 */\n@media (max-width: 768px) {\n  .login-container {\n    grid-template-columns: 1fr;\n    max-width: 400px;\n  }\n\n  .login-banner {\n    display: none;\n  }\n\n  .login-form {\n    padding: 40px 30px;\n  }\n\n  .form-header h2 {\n    font-size: 24px;\n  }\n}\n\n/* 表单样式调整 */\n:deep(.el-form-item) {\n  margin-bottom: 24px;\n}\n\n:deep(.el-input__wrapper) {\n  padding: 12px 16px;\n}\n\n:deep(.el-input__inner) {\n  font-size: 16px;\n}\n</style>\n", "import { render } from \"./Login.vue?vue&type=template&id=918e50a6&scoped=true\"\nimport script from \"./Login.vue?vue&type=script&lang=js\"\nexport * from \"./Login.vue?vue&type=script&lang=js\"\n\nimport \"./Login.vue?vue&type=style&index=0&id=918e50a6&scoped=true&lang=css\"\n\nimport exportComponent from \"../../node_modules/vue-loader/dist/exportHelper.js\"\nconst __exports__ = /*#__PURE__*/exportComponent(script, [['render',render],['__scopeId',\"data-v-918e50a6\"]])\n\nexport default __exports__"], "names": ["class", "ref", "model", "loginData", "rules", "loginRules", "handleLogin", "prop", "usernameOrEmail", "placeholder", "size", "password", "type", "loading", "to", "name", "components", "User", "Lock", "Check", "data", "required", "message", "trigger", "min", "methods", "valid", "this", "$refs", "loginForm", "validate", "result", "login", "success", "$message", "redirect", "$route", "query", "$router", "push", "error", "__exports__", "render"], "sourceRoot": ""}
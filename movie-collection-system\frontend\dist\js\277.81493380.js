"use strict";(self["webpackChunkmovie_collection_frontend"]=self["webpackChunkmovie_collection_frontend"]||[]).push([[277],{277:function(e,a,l){l.r(a),l.d(a,{default:function(){return U}});var t=l(641),s=l(33),r=l(751);const i={class:"movies-page"},o={class:"container"},n={class:"page-header"},c={class:"header-top"},d={class:"header-stats"},u={class:"search-section"},h={class:"filters-section"},v={class:"filter-group"},g={class:"filter-group"},b={class:"filter-group"},p={class:"filter-group"},k={class:"filter-actions"},m={key:0,style:{"text-align":"center",padding:"40px",color:"#666"}},F={class:"movies-grid"},y=["onClick"],f={class:"movie-poster"},L=["src","alt"],C={class:"movie-overlay"},w={class:"movie-rating"},_={class:"movie-actions"},S={class:"movie-info"},M={class:"movie-title"},P={class:"movie-meta"},V={class:"movie-overview"},D={class:"pagination-wrapper"};function B(e,a,l,B,K,$){const R=(0,t.g2)("Search"),z=(0,t.g2)("el-icon"),E=(0,t.g2)("el-button"),U=(0,t.g2)("el-input"),X=(0,t.g2)("el-option"),I=(0,t.g2)("el-select"),W=(0,t.g2)("Star"),Y=(0,t.g2)("Plus"),j=(0,t.g2)("el-pagination"),A=(0,t.gN)("loading");return(0,t.uX)(),(0,t.CE)("div",i,[(0,t.Lk)("div",o,[(0,t.Lk)("div",n,[(0,t.Lk)("div",c,[a[6]||(a[6]=(0,t.Lk)("h1",null,"电影浏览",-1)),(0,t.Lk)("div",d,[(0,t.Lk)("span",null,"共找到 "+(0,s.v_)($.total)+" 部电影",1)])]),(0,t.Lk)("div",u,[(0,t.bF)(U,{modelValue:K.searchKeyword,"onUpdate:modelValue":a[0]||(a[0]=e=>K.searchKeyword=e),placeholder:"搜索电影标题、导演、演员...",class:"search-input",onKeyup:(0,r.jR)($.handleSearch,["enter"]),clearable:""},{prepend:(0,t.k6)(()=>[(0,t.bF)(z,null,{default:(0,t.k6)(()=>[(0,t.bF)(R)]),_:1})]),append:(0,t.k6)(()=>[(0,t.bF)(E,{onClick:$.handleSearch,type:"primary"},{default:(0,t.k6)(()=>a[7]||(a[7]=[(0,t.eW)("搜索")])),_:1,__:[7]},8,["onClick"])]),_:1},8,["modelValue","onKeyup"])]),(0,t.Lk)("div",h,[(0,t.Lk)("div",v,[a[8]||(a[8]=(0,t.Lk)("label",null,"类型：",-1)),(0,t.bF)(I,{modelValue:K.selectedGenre,"onUpdate:modelValue":a[1]||(a[1]=e=>K.selectedGenre=e),placeholder:"全部类型",onChange:$.handleFilter,clearable:""},{default:(0,t.k6)(()=>[(0,t.bF)(X,{label:"全部类型",value:""}),(0,t.bF)(X,{label:"动作",value:"动作"}),(0,t.bF)(X,{label:"喜剧",value:"喜剧"}),(0,t.bF)(X,{label:"剧情",value:"剧情"}),(0,t.bF)(X,{label:"科幻",value:"科幻"}),(0,t.bF)(X,{label:"爱情",value:"爱情"}),(0,t.bF)(X,{label:"悬疑",value:"悬疑"}),(0,t.bF)(X,{label:"惊悚",value:"惊悚"}),(0,t.bF)(X,{label:"犯罪",value:"犯罪"}),(0,t.bF)(X,{label:"战争",value:"战争"}),(0,t.bF)(X,{label:"历史",value:"历史"})]),_:1},8,["modelValue","onChange"])]),(0,t.Lk)("div",g,[a[9]||(a[9]=(0,t.Lk)("label",null,"年份：",-1)),(0,t.bF)(I,{modelValue:K.selectedYear,"onUpdate:modelValue":a[2]||(a[2]=e=>K.selectedYear=e),placeholder:"全部年份",onChange:$.handleFilter,clearable:""},{default:(0,t.k6)(()=>[(0,t.bF)(X,{label:"全部年份",value:""}),(0,t.bF)(X,{label:"2024",value:"2024"}),(0,t.bF)(X,{label:"2023",value:"2023"}),(0,t.bF)(X,{label:"2022",value:"2022"}),(0,t.bF)(X,{label:"2021",value:"2021"}),(0,t.bF)(X,{label:"2020",value:"2020"}),(0,t.bF)(X,{label:"2010-2019",value:"2010s"}),(0,t.bF)(X,{label:"2000-2009",value:"2000s"}),(0,t.bF)(X,{label:"1990-1999",value:"1990s"}),(0,t.bF)(X,{label:"更早",value:"older"})]),_:1},8,["modelValue","onChange"])]),(0,t.Lk)("div",b,[a[10]||(a[10]=(0,t.Lk)("label",null,"评分：",-1)),(0,t.bF)(I,{modelValue:K.selectedRating,"onUpdate:modelValue":a[3]||(a[3]=e=>K.selectedRating=e),placeholder:"全部评分",onChange:$.handleFilter,clearable:""},{default:(0,t.k6)(()=>[(0,t.bF)(X,{label:"全部评分",value:""}),(0,t.bF)(X,{label:"9分以上",value:"9+"}),(0,t.bF)(X,{label:"8分以上",value:"8+"}),(0,t.bF)(X,{label:"7分以上",value:"7+"}),(0,t.bF)(X,{label:"6分以上",value:"6+"})]),_:1},8,["modelValue","onChange"])]),(0,t.Lk)("div",p,[a[11]||(a[11]=(0,t.Lk)("label",null,"排序：",-1)),(0,t.bF)(I,{modelValue:K.sortBy,"onUpdate:modelValue":a[4]||(a[4]=e=>K.sortBy=e),placeholder:"排序方式",onChange:$.handleSort},{default:(0,t.k6)(()=>[(0,t.bF)(X,{label:"评分最高",value:"rating"}),(0,t.bF)(X,{label:"最新上映",value:"release"}),(0,t.bF)(X,{label:"最受欢迎",value:"popular"}),(0,t.bF)(X,{label:"收藏最多",value:"collection"}),(0,t.bF)(X,{label:"按标题",value:"title"})]),_:1},8,["modelValue","onChange"])]),(0,t.Lk)("div",k,[(0,t.bF)(E,{onClick:$.clearFilters,type:"info",plain:""},{default:(0,t.k6)(()=>a[12]||(a[12]=[(0,t.eW)("清空筛选")])),_:1,__:[12]},8,["onClick"]),(0,t.bF)(E,{onClick:$.handleAdvancedSearch,type:"primary",plain:""},{default:(0,t.k6)(()=>a[13]||(a[13]=[(0,t.eW)("高级搜索")])),_:1,__:[13]},8,["onClick"])])])]),e.loading||0!==e.movies.length?(0,t.Q3)("",!0):((0,t.uX)(),(0,t.CE)("div",m,[a[14]||(a[14]=(0,t.Lk)("p",null,"没有找到电影数据",-1)),(0,t.Lk)("p",null,"Movies数组长度: "+(0,s.v_)(e.movies.length),1),(0,t.Lk)("p",null,"Loading状态: "+(0,s.v_)(e.loading),1),(0,t.Lk)("p",null,"Pagination: "+(0,s.v_)(JSON.stringify(e.pagination)),1)])),(0,t.bo)(((0,t.uX)(),(0,t.CE)("div",F,[((0,t.uX)(!0),(0,t.CE)(t.FK,null,(0,t.pI)($.formattedMovies,a=>((0,t.uX)(),(0,t.CE)("div",{class:"movie-card",key:a.id,onClick:e=>$.viewMovie(a.id)},[(0,t.Lk)("div",f,[(0,t.Lk)("img",{src:a.poster||"/placeholder.jpg",alt:a.title},null,8,L),(0,t.Lk)("div",C,[(0,t.Lk)("div",w,[(0,t.bF)(z,null,{default:(0,t.k6)(()=>[(0,t.bF)(W)]),_:1}),(0,t.eW)(" "+(0,s.v_)(a.rating),1)]),(0,t.Lk)("div",_,[e.isLoggedIn?((0,t.uX)(),(0,t.Wv)(E,{key:0,type:"primary",size:"small",circle:"",onClick:(0,r.D$)(e=>$.addToCollection(a.id),["stop"])},{default:(0,t.k6)(()=>[(0,t.bF)(z,null,{default:(0,t.k6)(()=>[(0,t.bF)(Y)]),_:1})]),_:2},1032,["onClick"])):(0,t.Q3)("",!0)])])]),(0,t.Lk)("div",S,[(0,t.Lk)("h3",M,(0,s.v_)(a.title),1),(0,t.Lk)("p",P,(0,s.v_)(a.year)+" · "+(0,s.v_)(a.director),1),(0,t.Lk)("p",V,(0,s.v_)(a.overview),1)])],8,y))),128))])),[[A,e.loading]]),(0,t.Lk)("div",D,[(0,t.bF)(j,{"current-page":K.currentPage,"onUpdate:currentPage":a[5]||(a[5]=e=>K.currentPage=e),"page-size":K.pageSize,total:$.total,layout:"prev, pager, next, jumper",onCurrentChange:$.handlePageChange},null,8,["current-page","page-size","total","onCurrentChange"])])])])}var K=l(548),$=l(278),R={name:"Movies",components:{Search:K.Search,Star:K.Star,Plus:K.Plus},data(){return{searchKeyword:"",selectedGenre:"",selectedYear:"",selectedRating:"",sortBy:"averageRating",sortDir:"desc",currentPage:1,pageSize:20,showAdvancedSearch:!1}},computed:{...(0,$.L8)("user",["isLoggedIn"]),...(0,$.L8)("movie",["movies","loading","pagination"]),formattedMovies(){return this.movies.map(e=>({id:e.id,title:e.title,year:e.releaseDate?new Date(e.releaseDate).getFullYear():"未知",director:e.director||"未知",rating:e.averageRating||0,poster:e.posterPath||"/placeholder.jpg",overview:e.overview||"暂无简介"}))},total(){return this.pagination.totalElements||0}},async mounted(){console.log("Movies component mounted");try{this.$route.query.search?(this.searchKeyword=this.$route.query.search,await this.handleSearch()):await this.loadMovies()}catch(e){console.error("Mounted error:",e),this.$message.error("页面初始化失败: "+e.message)}},methods:{...(0,$.i0)("movie",["fetchMovies","searchMovies"]),async loadMovies(){try{const e={page:this.currentPage-1,size:this.pageSize,sortBy:this.sortBy,sortDir:this.sortDir};this.searchKeyword.trim()&&(e.search=this.searchKeyword.trim()),console.log("Loading movies with params:",e),await this.fetchMovies(e)}catch(e){console.error("加载电影失败:",e),this.$message.error("加载电影失败: "+e.message)}},parseRatingFilter(e){switch(e){case"9+":return 9;case"8+":return 8;case"7+":return 7;case"6+":return 6;default:return null}},async handleSearch(){if(this.currentPage=1,this.searchKeyword.trim())try{await this.searchMovies({keyword:this.searchKeyword.trim(),page:0,size:this.pageSize})}catch(e){console.error("搜索失败:",e),this.$message.error("搜索失败")}else await this.loadMovies()},async handleFilter(){this.currentPage=1,await this.loadMovies()},async handleSort(){switch(this.sortBy){case"rating":this.sortBy="averageRating",this.sortDir="desc";break;case"release":this.sortBy="releaseDate",this.sortDir="desc";break;case"popular":this.sortBy="ratingCount",this.sortDir="desc";break;case"collection":this.sortBy="collectionCount",this.sortDir="desc";break;case"title":this.sortBy="title",this.sortDir="asc";break;default:this.sortBy="averageRating",this.sortDir="desc"}this.currentPage=1,await this.loadMovies()},clearFilters(){this.searchKeyword="",this.selectedGenre="",this.selectedYear="",this.selectedRating="",this.sortBy="averageRating",this.currentPage=1,this.loadMovies()},handleAdvancedSearch(){this.$message.info("高级搜索功能开发中...")},async handlePageChange(e){this.currentPage=e,await this.loadMovies()},viewMovie(e){this.$router.push(`/movies/${e}`)},addToCollection(e){if(!this.isLoggedIn)return this.$message.warning("请先登录"),void this.$router.push("/login");this.$message.success("收藏成功")}}},z=l(262);const E=(0,z.A)(R,[["render",B],["__scopeId","data-v-34f5f1fc"]]);var U=E}}]);
//# sourceMappingURL=277.81493380.js.map
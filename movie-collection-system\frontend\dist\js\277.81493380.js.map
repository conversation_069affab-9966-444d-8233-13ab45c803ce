{"version": 3, "file": "js/277.81493380.js", "mappings": "oOACOA,MAAM,e,GACJA,MAAM,a,GAEJA,MAAM,e,GACJA,MAAM,c,GAEJA,MAAM,gB,GAKRA,MAAM,kB,GAiBNA,MAAM,mB,GACJA,MAAM,gB,GAiBNA,MAAM,gB,GAgBNA,MAAM,gB,GAWNA,MAAM,gB,GAWNA,MAAM,kB,SAQ6BC,MAAA,qD,GAQvCD,MAAM,e,iBAEFA,MAAM,gB,mBAEJA,MAAM,iB,GACJA,MAAM,gB,GAINA,MAAM,iB,GAaVA,MAAM,c,GACLA,MAAM,e,GACPA,MAAM,c,GACNA,MAAM,kB,GAMVA,MAAM,sB,kRAnIf,QA6IM,MA7IN,EA6IM,EA5IJ,QA2IM,MA3IN,EA2IM,EAzIJ,QAsFM,MAtFN,EAsFM,EArFJ,QAKM,MALN,EAKM,C,aAJJ,QAAa,UAAT,QAAI,KACR,QAEM,MAFN,EAEM,EADJ,QAAgC,YAA1B,QAAI,QAAG,EAAAE,OAAQ,OAAI,QAI7B,QAeM,MAfN,EAeM,EAdJ,QAaW,G,WAZA,EAAAC,c,qCAAA,EAAa,iBACtBC,YAAY,kBACZJ,MAAM,eACL,SAAK,QAAQ,EAAAK,aAAY,WAC1BC,UAAA,I,CAEWC,SAAO,QAChB,IAA6B,EAA7B,QAA6B,Q,iBAApB,IAAU,EAAV,QAAU,K,QAEVC,QAAM,QACf,IAA8D,EAA9D,QAA8D,GAAlD,QAAO,EAAAH,aAAcI,KAAK,W,kBAAU,IAAE,c,QAAF,S,iEAKtD,QA4DM,MA5DN,EA4DM,EA3DJ,QAeM,MAfN,EAeM,C,aAdJ,QAAkB,aAAX,OAAG,KACV,QAYY,G,WAZQ,EAAAC,c,qCAAA,EAAa,iBAAEN,YAAY,OAAQ,SAAQ,EAAAO,aAAcL,UAAA,I,kBAC3E,IAAmC,EAAnC,QAAmC,GAAxBM,MAAM,OAAOC,MAAM,MAC9B,QAAmC,GAAxBD,MAAM,KAAKC,MAAM,QAC5B,QAAmC,GAAxBD,MAAM,KAAKC,MAAM,QAC5B,QAAmC,GAAxBD,MAAM,KAAKC,MAAM,QAC5B,QAAmC,GAAxBD,MAAM,KAAKC,MAAM,QAC5B,QAAmC,GAAxBD,MAAM,KAAKC,MAAM,QAC5B,QAAmC,GAAxBD,MAAM,KAAKC,MAAM,QAC5B,QAAmC,GAAxBD,MAAM,KAAKC,MAAM,QAC5B,QAAmC,GAAxBD,MAAM,KAAKC,MAAM,QAC5B,QAAmC,GAAxBD,MAAM,KAAKC,MAAM,QAC5B,QAAmC,GAAxBD,MAAM,KAAKC,MAAM,S,qCAIhC,QAcM,MAdN,EAcM,C,aAbJ,QAAkB,aAAX,OAAG,KACV,QAWY,G,WAXQ,EAAAC,a,qCAAA,EAAY,gBAAEV,YAAY,OAAQ,SAAQ,EAAAO,aAAcL,UAAA,I,kBAC1E,IAAmC,EAAnC,QAAmC,GAAxBM,MAAM,OAAOC,MAAM,MAC9B,QAAuC,GAA5BD,MAAM,OAAOC,MAAM,UAC9B,QAAuC,GAA5BD,MAAM,OAAOC,MAAM,UAC9B,QAAuC,GAA5BD,MAAM,OAAOC,MAAM,UAC9B,QAAuC,GAA5BD,MAAM,OAAOC,MAAM,UAC9B,QAAuC,GAA5BD,MAAM,OAAOC,MAAM,UAC9B,QAA6C,GAAlCD,MAAM,YAAYC,MAAM,WACnC,QAA6C,GAAlCD,MAAM,YAAYC,MAAM,WACnC,QAA6C,GAAlCD,MAAM,YAAYC,MAAM,WACnC,QAAsC,GAA3BD,MAAM,KAAKC,MAAM,Y,qCAIhC,QASM,MATN,EASM,C,eARJ,QAAkB,aAAX,OAAG,KACV,QAMY,G,WANQ,EAAAE,e,qCAAA,EAAc,kBAAEX,YAAY,OAAQ,SAAQ,EAAAO,aAAcL,UAAA,I,kBAC5E,IAAmC,EAAnC,QAAmC,GAAxBM,MAAM,OAAOC,MAAM,MAC9B,QAAqC,GAA1BD,MAAM,OAAOC,MAAM,QAC9B,QAAqC,GAA1BD,MAAM,OAAOC,MAAM,QAC9B,QAAqC,GAA1BD,MAAM,OAAOC,MAAM,QAC9B,QAAqC,GAA1BD,MAAM,OAAOC,MAAM,S,qCAIlC,QASM,MATN,EASM,C,eARJ,QAAkB,aAAX,OAAG,KACV,QAMY,G,WANQ,EAAAG,O,qCAAA,EAAM,UAAEZ,YAAY,OAAQ,SAAQ,EAAAa,Y,kBACtD,IAAyC,EAAzC,QAAyC,GAA9BL,MAAM,OAAOC,MAAM,YAC9B,QAA0C,GAA/BD,MAAM,OAAOC,MAAM,aAC9B,QAA0C,GAA/BD,MAAM,OAAOC,MAAM,aAC9B,QAA6C,GAAlCD,MAAM,OAAOC,MAAM,gBAC9B,QAAuC,GAA5BD,MAAM,MAAMC,MAAM,Y,qCAIjC,QAGM,MAHN,EAGM,EAFJ,QAAmE,GAAvD,QAAO,EAAAK,aAAcT,KAAK,OAAOU,MAAA,I,kBAAM,IAAI,gB,QAAJ,W,6BACnD,QAA8E,GAAlE,QAAO,EAAAC,qBAAsBX,KAAK,UAAUU,MAAA,I,kBAAM,IAAI,gB,QAAJ,W,kCAMxDE,EAAAA,SAA6B,IAAlBC,EAAAA,OAAOC,Q,4BAA9B,QAKM,MALN,EAKM,C,eAJJ,QAAe,SAAZ,YAAQ,KACX,QAAsC,SAAnC,gBAAY,QAAGD,EAAAA,OAAOC,QAAM,IAC/B,QAA+B,SAA5B,eAAW,QAAGF,EAAAA,SAAO,IACxB,QAAmD,SAAhD,gBAAY,QAAGG,KAAKC,UAAUC,EAAAA,aAAU,O,qBAI7C,QA4BM,MA5BN,EA4BM,G,aA3BJ,QA0BM,mBA1BkC,EAAAC,gBAATC,K,WAA/B,QA0BM,OA1BD5B,MAAM,aAA+C6B,IAAKD,EAAME,GAAK,QAAK,GAAE,EAAAC,UAAUH,EAAME,K,EAC/F,QAmBM,MAnBN,EAmBM,EAlBJ,QAAoE,OAA9DE,IAAKJ,EAAMK,QAAU,mBAAqBC,IAAKN,EAAMO,O,WAC3D,QAgBM,MAhBN,EAgBM,EAfJ,QAGM,MAHN,EAGM,EAFJ,QAA2B,Q,iBAAlB,IAAQ,EAAR,QAAQ,K,eAAU,KAC3B,QAAGP,EAAMQ,QAAM,MAEjB,QAUM,MAVN,EAUM,CAJIC,EAAU,a,WALlB,QAQY,G,MAPV5B,KAAK,UACL6B,KAAK,QACLC,OAAA,GACC,SAAK,WAAO,EAAAC,gBAAgBZ,EAAME,IAAE,W,kBAGrC,IAA2B,EAA3B,QAA2B,Q,iBAAlB,IAAQ,EAAR,QAAQ,K,uDAKzB,QAIM,MAJN,EAIM,EAHJ,QAA8C,KAA9C,GAA8C,QAAnBF,EAAMO,OAAK,IACtC,QAAiE,IAAjE,GAAiE,QAAxCP,EAAMa,MAAO,OAAG,QAAGb,EAAMc,UAAQ,IAC1D,QAAkD,IAAlD,GAAkD,QAArBd,EAAMe,UAAQ,M,oBAzBbtB,EAAAA,YA+BpC,QAQM,MARN,EAQM,EAPJ,QAME,GALQ,eAAc,EAAAuB,Y,sCAAA,EAAW,eAChC,YAAW,EAAAC,SACX3C,MAAO,EAAAA,MACR4C,OAAO,4BACN,gBAAgB,EAAAC,kB,2FAW3B,GACEC,KAAM,SACNC,WAAY,CACVC,OAAM,SACNC,KAAI,OACJC,KAAI,QAEN,IAAAC,GACE,MAAO,CACLlD,cAAe,GACfO,cAAe,GACfI,aAAc,GACdC,eAAgB,GAChBC,OAAQ,gBACRsC,QAAS,OACTV,YAAa,EACbC,SAAU,GACVU,oBAAoB,EAExB,EACAC,SAAU,KACL,QAAW,OAAQ,CAAC,mBACpB,QAAW,QAAS,CAAC,SAAU,UAAW,eAG7C,eAAA7B,GACE,OAAO8B,KAAKnC,OAAOoC,IAAI9B,IAAS,CAC9BE,GAAIF,EAAME,GACVK,MAAOP,EAAMO,MACbM,KAAMb,EAAM+B,YAAc,IAAIC,KAAKhC,EAAM+B,aAAaE,cAAgB,KACtEnB,SAAUd,EAAMc,UAAY,KAC5BN,OAAQR,EAAMkC,eAAiB,EAC/B7B,OAAQL,EAAMmC,YAAc,mBAC5BpB,SAAUf,EAAMe,UAAY,SAEhC,EAEA,KAAAzC,GACE,OAAOuD,KAAK/B,WAAWsC,eAAiB,CAC1C,GAEF,aAAMC,GACJC,QAAQC,IAAI,4BACZ,IAEMV,KAAKW,OAAOC,MAAMC,QACpBb,KAAKtD,cAAgBsD,KAAKW,OAAOC,MAAMC,aACjCb,KAAKpD,sBAELoD,KAAKc,YAEf,CAAE,MAAOC,GACPN,QAAQM,MAAM,iBAAkBA,GAChCf,KAAKgB,SAASD,MAAM,YAAcA,EAAME,QAC1C,CACF,EACAC,QAAS,KACJ,QAAW,QAAS,CAAC,cAAe,iBAEvC,gBAAMJ,GACJ,IACE,MAAMK,EAAS,CACbC,KAAMpB,KAAKb,YAAc,EACzBN,KAAMmB,KAAKZ,SACX7B,OAAQyC,KAAKzC,OACbsC,QAASG,KAAKH,SAIZG,KAAKtD,cAAc2E,SACrBF,EAAON,OAASb,KAAKtD,cAAc2E,QAiBrCZ,QAAQC,IAAI,8BAA+BS,SACrCnB,KAAKsB,YAAYH,EACzB,CAAE,MAAOJ,GACPN,QAAQM,MAAM,UAAWA,GACzBf,KAAKgB,SAASD,MAAM,WAAaA,EAAME,QACzC,CACF,EAEA,iBAAAM,CAAkBC,GAChB,OAAQA,GACN,IAAK,KAAM,OAAO,EAClB,IAAK,KAAM,OAAO,EAClB,IAAK,KAAM,OAAO,EAClB,IAAK,KAAM,OAAO,EAClB,QAAS,OAAO,KAEpB,EAEA,kBAAM5E,GAEJ,GADAoD,KAAKb,YAAc,EACfa,KAAKtD,cAAc2E,OACrB,UACQrB,KAAKyB,aAAa,CACtBC,QAAS1B,KAAKtD,cAAc2E,OAC5BD,KAAM,EACNvC,KAAMmB,KAAKZ,UAEf,CAAE,MAAO2B,GACPN,QAAQM,MAAM,QAASA,GACvBf,KAAKgB,SAASD,MAAM,OACtB,YAEMf,KAAKc,YAEf,EAEA,kBAAM5D,GACJ8C,KAAKb,YAAc,QACba,KAAKc,YACb,EAEA,gBAAMtD,GAEJ,OAAQwC,KAAKzC,QACX,IAAK,SACHyC,KAAKzC,OAAS,gBACdyC,KAAKH,QAAU,OACf,MACF,IAAK,UACHG,KAAKzC,OAAS,cACdyC,KAAKH,QAAU,OACf,MACF,IAAK,UACHG,KAAKzC,OAAS,cACdyC,KAAKH,QAAU,OACf,MACF,IAAK,aACHG,KAAKzC,OAAS,kBACdyC,KAAKH,QAAU,OACf,MACF,IAAK,QACHG,KAAKzC,OAAS,QACdyC,KAAKH,QAAU,MACf,MACF,QACEG,KAAKzC,OAAS,gBACdyC,KAAKH,QAAU,OAGnBG,KAAKb,YAAc,QACba,KAAKc,YACb,EAEA,YAAArD,GACEuC,KAAKtD,cAAgB,GACrBsD,KAAK/C,cAAgB,GACrB+C,KAAK3C,aAAe,GACpB2C,KAAK1C,eAAiB,GACtB0C,KAAKzC,OAAS,gBACdyC,KAAKb,YAAc,EACnBa,KAAKc,YACP,EAEA,oBAAAnD,GAEEqC,KAAKgB,SAASW,KAAK,eACrB,EAEA,sBAAMrC,CAAiB8B,GACrBpB,KAAKb,YAAciC,QACbpB,KAAKc,YACb,EAEA,SAAAxC,CAAUD,GACR2B,KAAK4B,QAAQC,KAAK,WAAWxD,IAC/B,EAEA,eAAAU,CAAgBV,GACd,IAAK2B,KAAKpB,WAGR,OAFAoB,KAAKgB,SAASc,QAAQ,aACtB9B,KAAK4B,QAAQC,KAAK,UAKpB7B,KAAKgB,SAASe,QAAQ,OACxB,I,SC/UJ,MAAMC,GAA2B,OAAgB,EAAQ,CAAC,CAAC,SAASC,GAAQ,CAAC,YAAY,qBAEzF,O", "sources": ["webpack://movie-collection-frontend/./src/views/Movies.vue", "webpack://movie-collection-frontend/./src/views/Movies.vue?c03d"], "sourcesContent": ["<template>\n  <div class=\"movies-page\">\n    <div class=\"container\">\n      <!-- 页面标题和搜索 -->\n      <div class=\"page-header\">\n        <div class=\"header-top\">\n          <h1>电影浏览</h1>\n          <div class=\"header-stats\">\n            <span>共找到 {{ total }} 部电影</span>\n          </div>\n        </div>\n\n        <div class=\"search-section\">\n          <el-input\n            v-model=\"searchKeyword\"\n            placeholder=\"搜索电影标题、导演、演员...\"\n            class=\"search-input\"\n            @keyup.enter=\"handleSearch\"\n            clearable\n          >\n            <template #prepend>\n              <el-icon><Search /></el-icon>\n            </template>\n            <template #append>\n              <el-button @click=\"handleSearch\" type=\"primary\">搜索</el-button>\n            </template>\n          </el-input>\n        </div>\n\n        <div class=\"filters-section\">\n          <div class=\"filter-group\">\n            <label>类型：</label>\n            <el-select v-model=\"selectedGenre\" placeholder=\"全部类型\" @change=\"handleFilter\" clearable>\n              <el-option label=\"全部类型\" value=\"\" />\n              <el-option label=\"动作\" value=\"动作\" />\n              <el-option label=\"喜剧\" value=\"喜剧\" />\n              <el-option label=\"剧情\" value=\"剧情\" />\n              <el-option label=\"科幻\" value=\"科幻\" />\n              <el-option label=\"爱情\" value=\"爱情\" />\n              <el-option label=\"悬疑\" value=\"悬疑\" />\n              <el-option label=\"惊悚\" value=\"惊悚\" />\n              <el-option label=\"犯罪\" value=\"犯罪\" />\n              <el-option label=\"战争\" value=\"战争\" />\n              <el-option label=\"历史\" value=\"历史\" />\n            </el-select>\n          </div>\n\n          <div class=\"filter-group\">\n            <label>年份：</label>\n            <el-select v-model=\"selectedYear\" placeholder=\"全部年份\" @change=\"handleFilter\" clearable>\n              <el-option label=\"全部年份\" value=\"\" />\n              <el-option label=\"2024\" value=\"2024\" />\n              <el-option label=\"2023\" value=\"2023\" />\n              <el-option label=\"2022\" value=\"2022\" />\n              <el-option label=\"2021\" value=\"2021\" />\n              <el-option label=\"2020\" value=\"2020\" />\n              <el-option label=\"2010-2019\" value=\"2010s\" />\n              <el-option label=\"2000-2009\" value=\"2000s\" />\n              <el-option label=\"1990-1999\" value=\"1990s\" />\n              <el-option label=\"更早\" value=\"older\" />\n            </el-select>\n          </div>\n\n          <div class=\"filter-group\">\n            <label>评分：</label>\n            <el-select v-model=\"selectedRating\" placeholder=\"全部评分\" @change=\"handleFilter\" clearable>\n              <el-option label=\"全部评分\" value=\"\" />\n              <el-option label=\"9分以上\" value=\"9+\" />\n              <el-option label=\"8分以上\" value=\"8+\" />\n              <el-option label=\"7分以上\" value=\"7+\" />\n              <el-option label=\"6分以上\" value=\"6+\" />\n            </el-select>\n          </div>\n\n          <div class=\"filter-group\">\n            <label>排序：</label>\n            <el-select v-model=\"sortBy\" placeholder=\"排序方式\" @change=\"handleSort\">\n              <el-option label=\"评分最高\" value=\"rating\" />\n              <el-option label=\"最新上映\" value=\"release\" />\n              <el-option label=\"最受欢迎\" value=\"popular\" />\n              <el-option label=\"收藏最多\" value=\"collection\" />\n              <el-option label=\"按标题\" value=\"title\" />\n            </el-select>\n          </div>\n\n          <div class=\"filter-actions\">\n            <el-button @click=\"clearFilters\" type=\"info\" plain>清空筛选</el-button>\n            <el-button @click=\"handleAdvancedSearch\" type=\"primary\" plain>高级搜索</el-button>\n          </div>\n        </div>\n      </div>\n\n      <!-- 调试信息 -->\n      <div v-if=\"!loading && movies.length === 0\" style=\"text-align: center; padding: 40px; color: #666;\">\n        <p>没有找到电影数据</p>\n        <p>Movies数组长度: {{ movies.length }}</p>\n        <p>Loading状态: {{ loading }}</p>\n        <p>Pagination: {{ JSON.stringify(pagination) }}</p>\n      </div>\n\n      <!-- 电影网格 -->\n      <div class=\"movies-grid\" v-loading=\"loading\">\n        <div class=\"movie-card\" v-for=\"movie in formattedMovies\" :key=\"movie.id\" @click=\"viewMovie(movie.id)\">\n          <div class=\"movie-poster\">\n            <img :src=\"movie.poster || '/placeholder.jpg'\" :alt=\"movie.title\" />\n            <div class=\"movie-overlay\">\n              <div class=\"movie-rating\">\n                <el-icon><Star /></el-icon>\n                {{ movie.rating }}\n              </div>\n              <div class=\"movie-actions\">\n                <el-button\n                  type=\"primary\"\n                  size=\"small\"\n                  circle\n                  @click.stop=\"addToCollection(movie.id)\"\n                  v-if=\"isLoggedIn\"\n                >\n                  <el-icon><Plus /></el-icon>\n                </el-button>\n              </div>\n            </div>\n          </div>\n          <div class=\"movie-info\">\n            <h3 class=\"movie-title\">{{ movie.title }}</h3>\n            <p class=\"movie-meta\">{{ movie.year }} · {{ movie.director }}</p>\n            <p class=\"movie-overview\">{{ movie.overview }}</p>\n          </div>\n        </div>\n      </div>\n\n      <!-- 分页 -->\n      <div class=\"pagination-wrapper\">\n        <el-pagination\n          v-model:current-page=\"currentPage\"\n          :page-size=\"pageSize\"\n          :total=\"total\"\n          layout=\"prev, pager, next, jumper\"\n          @current-change=\"handlePageChange\"\n        />\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport { Search, Star, Plus } from '@element-plus/icons-vue'\nimport { mapGetters, mapActions } from 'vuex'\n\nexport default {\n  name: 'Movies',\n  components: {\n    Search,\n    Star,\n    Plus\n  },\n  data() {\n    return {\n      searchKeyword: '',\n      selectedGenre: '',\n      selectedYear: '',\n      selectedRating: '',\n      sortBy: 'averageRating',\n      sortDir: 'desc',\n      currentPage: 1,\n      pageSize: 20,\n      showAdvancedSearch: false\n    }\n  },\n  computed: {\n    ...mapGetters('user', ['isLoggedIn']),\n    ...mapGetters('movie', ['movies', 'loading', 'pagination']),\n\n    // 格式化电影数据用于显示\n    formattedMovies() {\n      return this.movies.map(movie => ({\n        id: movie.id,\n        title: movie.title,\n        year: movie.releaseDate ? new Date(movie.releaseDate).getFullYear() : '未知',\n        director: movie.director || '未知',\n        rating: movie.averageRating || 0,\n        poster: movie.posterPath || '/placeholder.jpg',\n        overview: movie.overview || '暂无简介'\n      }))\n    },\n\n    total() {\n      return this.pagination.totalElements || 0\n    }\n  },\n  async mounted() {\n    console.log('Movies component mounted')\n    try {\n      // 处理URL查询参数\n      if (this.$route.query.search) {\n        this.searchKeyword = this.$route.query.search\n        await this.handleSearch()\n      } else {\n        await this.loadMovies()\n      }\n    } catch (error) {\n      console.error('Mounted error:', error)\n      this.$message.error('页面初始化失败: ' + error.message)\n    }\n  },\n  methods: {\n    ...mapActions('movie', ['fetchMovies', 'searchMovies']),\n\n    async loadMovies() {\n      try {\n        const params = {\n          page: this.currentPage - 1, // API使用0基索引\n          size: this.pageSize,\n          sortBy: this.sortBy,\n          sortDir: this.sortDir\n        }\n\n        // 添加搜索关键词\n        if (this.searchKeyword.trim()) {\n          params.search = this.searchKeyword.trim()\n        }\n\n        // TODO: 后续实现筛选功能\n        // 暂时注释掉不支持的参数\n        // if (this.selectedGenre) {\n        //   params.genre = this.selectedGenre\n        // }\n\n        // if (this.selectedYear) {\n        //   params.year = this.selectedYear\n        // }\n\n        // if (this.selectedRating) {\n        //   params.minRating = this.parseRatingFilter(this.selectedRating)\n        // }\n\n        console.log('Loading movies with params:', params)\n        await this.fetchMovies(params)\n      } catch (error) {\n        console.error('加载电影失败:', error)\n        this.$message.error('加载电影失败: ' + error.message)\n      }\n    },\n\n    parseRatingFilter(ratingFilter) {\n      switch (ratingFilter) {\n        case '9+': return 9.0\n        case '8+': return 8.0\n        case '7+': return 7.0\n        case '6+': return 6.0\n        default: return null\n      }\n    },\n\n    async handleSearch() {\n      this.currentPage = 1\n      if (this.searchKeyword.trim()) {\n        try {\n          await this.searchMovies({\n            keyword: this.searchKeyword.trim(),\n            page: 0,\n            size: this.pageSize\n          })\n        } catch (error) {\n          console.error('搜索失败:', error)\n          this.$message.error('搜索失败')\n        }\n      } else {\n        await this.loadMovies()\n      }\n    },\n\n    async handleFilter() {\n      this.currentPage = 1\n      await this.loadMovies()\n    },\n\n    async handleSort() {\n      // 转换排序方式\n      switch (this.sortBy) {\n        case 'rating':\n          this.sortBy = 'averageRating'\n          this.sortDir = 'desc'\n          break\n        case 'release':\n          this.sortBy = 'releaseDate'\n          this.sortDir = 'desc'\n          break\n        case 'popular':\n          this.sortBy = 'ratingCount'\n          this.sortDir = 'desc'\n          break\n        case 'collection':\n          this.sortBy = 'collectionCount'\n          this.sortDir = 'desc'\n          break\n        case 'title':\n          this.sortBy = 'title'\n          this.sortDir = 'asc'\n          break\n        default:\n          this.sortBy = 'averageRating'\n          this.sortDir = 'desc'\n      }\n\n      this.currentPage = 1\n      await this.loadMovies()\n    },\n\n    clearFilters() {\n      this.searchKeyword = ''\n      this.selectedGenre = ''\n      this.selectedYear = ''\n      this.selectedRating = ''\n      this.sortBy = 'averageRating'\n      this.currentPage = 1\n      this.loadMovies()\n    },\n\n    handleAdvancedSearch() {\n      // TODO: 实现高级搜索对话框\n      this.$message.info('高级搜索功能开发中...')\n    },\n\n    async handlePageChange(page) {\n      this.currentPage = page\n      await this.loadMovies()\n    },\n\n    viewMovie(id) {\n      this.$router.push(`/movies/${id}`)\n    },\n\n    addToCollection(id) {\n      if (!this.isLoggedIn) {\n        this.$message.warning('请先登录')\n        this.$router.push('/login')\n        return\n      }\n      \n      // TODO: 实现收藏功能\n      this.$message.success('收藏成功')\n    }\n  }\n}\n</script>\n\n<style scoped>\n.movies-page {\n  min-height: 100vh;\n  padding: 20px 0;\n}\n\n.container {\n  max-width: 1200px;\n  margin: 0 auto;\n  padding: 0 20px;\n}\n\n.page-header {\n  background: white;\n  border-radius: 12px;\n  padding: 24px;\n  margin-bottom: 30px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n}\n\n.header-top {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 20px;\n}\n\n.page-header h1 {\n  font-size: 32px;\n  font-weight: bold;\n  color: #333;\n  margin: 0;\n}\n\n.header-stats {\n  color: #666;\n  font-size: 14px;\n}\n\n.search-section {\n  margin-bottom: 20px;\n}\n\n.search-input {\n  width: 100%;\n  max-width: 600px;\n}\n\n.filters-section {\n  display: flex;\n  gap: 16px;\n  align-items: center;\n  flex-wrap: wrap;\n  padding-top: 20px;\n  border-top: 1px solid #eee;\n}\n\n.filter-group {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n}\n\n.filter-group label {\n  font-size: 14px;\n  color: #666;\n  white-space: nowrap;\n}\n\n.filter-group .el-select {\n  width: 120px;\n}\n\n.filter-actions {\n  margin-left: auto;\n  display: flex;\n  gap: 8px;\n}\n\n.movies-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));\n  gap: 24px;\n  margin-bottom: 40px;\n}\n\n.movie-card {\n  background: white;\n  border-radius: 12px;\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\n  overflow: hidden;\n  cursor: pointer;\n  transition: transform 0.3s, box-shadow 0.3s;\n}\n\n.movie-card:hover {\n  transform: translateY(-4px);\n  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);\n}\n\n.movie-poster {\n  position: relative;\n  aspect-ratio: 2/3;\n  overflow: hidden;\n}\n\n.movie-poster img {\n  width: 100%;\n  height: 100%;\n  object-fit: cover;\n}\n\n.movie-overlay {\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: linear-gradient(to bottom, rgba(0,0,0,0.3) 0%, transparent 30%, transparent 70%, rgba(0,0,0,0.7) 100%);\n  display: flex;\n  flex-direction: column;\n  justify-content: space-between;\n  padding: 12px;\n  opacity: 0;\n  transition: opacity 0.3s;\n}\n\n.movie-card:hover .movie-overlay {\n  opacity: 1;\n}\n\n.movie-rating {\n  align-self: flex-end;\n  background: rgba(0, 0, 0, 0.8);\n  color: white;\n  padding: 6px 10px;\n  border-radius: 6px;\n  font-size: 14px;\n  font-weight: 600;\n  display: flex;\n  align-items: center;\n  gap: 4px;\n}\n\n.movie-actions {\n  align-self: flex-end;\n}\n\n.movie-info {\n  padding: 16px;\n}\n\n.movie-title {\n  font-size: 16px;\n  font-weight: 600;\n  color: #333;\n  margin-bottom: 8px;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n}\n\n.movie-meta {\n  font-size: 14px;\n  color: #666;\n  margin-bottom: 8px;\n}\n\n.movie-overview {\n  font-size: 13px;\n  color: #888;\n  line-height: 1.4;\n  display: -webkit-box;\n  -webkit-line-clamp: 2;\n  -webkit-box-orient: vertical;\n  overflow: hidden;\n  margin: 0;\n}\n\n.pagination-wrapper {\n  display: flex;\n  justify-content: center;\n  margin-top: 40px;\n}\n\n/* 响应式设计 */\n@media (max-width: 768px) {\n  .page-header {\n    padding: 16px;\n  }\n\n  .header-top {\n    flex-direction: column;\n    align-items: flex-start;\n    gap: 8px;\n  }\n\n  .page-header h1 {\n    font-size: 24px;\n  }\n\n  .filters-section {\n    flex-direction: column;\n    align-items: stretch;\n    gap: 12px;\n  }\n\n  .filter-group {\n    justify-content: space-between;\n  }\n\n  .filter-group .el-select {\n    width: 150px;\n  }\n\n  .filter-actions {\n    margin-left: 0;\n    justify-content: center;\n  }\n\n  .movies-grid {\n    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));\n    gap: 16px;\n  }\n}\n</style>\n", "import { render } from \"./Movies.vue?vue&type=template&id=34f5f1fc&scoped=true\"\nimport script from \"./Movies.vue?vue&type=script&lang=js\"\nexport * from \"./Movies.vue?vue&type=script&lang=js\"\n\nimport \"./Movies.vue?vue&type=style&index=0&id=34f5f1fc&scoped=true&lang=css\"\n\nimport exportComponent from \"../../node_modules/vue-loader/dist/exportHelper.js\"\nconst __exports__ = /*#__PURE__*/exportComponent(script, [['render',render],['__scopeId',\"data-v-34f5f1fc\"]])\n\nexport default __exports__"], "names": ["class", "style", "total", "searchKeyword", "placeholder", "handleSearch", "clearable", "prepend", "append", "type", "selectedG<PERSON>re", "handleFilter", "label", "value", "selected<PERSON>ear", "selectedRating", "sortBy", "handleSort", "clearFilters", "plain", "handleAdvancedSearch", "loading", "movies", "length", "JSON", "stringify", "pagination", "formattedMovies", "movie", "key", "id", "viewMovie", "src", "poster", "alt", "title", "rating", "isLoggedIn", "size", "circle", "addToCollection", "year", "director", "overview", "currentPage", "pageSize", "layout", "handlePageChange", "name", "components", "Search", "Star", "Plus", "data", "sortDir", "showAdvancedSearch", "computed", "this", "map", "releaseDate", "Date", "getFullYear", "averageRating", "posterPath", "totalElements", "mounted", "console", "log", "$route", "query", "search", "loadMovies", "error", "$message", "message", "methods", "params", "page", "trim", "fetchMovies", "parseRating<PERSON><PERSON>er", "ratingFilter", "searchMovies", "keyword", "info", "$router", "push", "warning", "success", "__exports__", "render"], "sourceRoot": ""}
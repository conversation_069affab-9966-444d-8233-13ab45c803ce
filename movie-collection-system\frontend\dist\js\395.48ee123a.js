"use strict";(self["webpackChunkmovie_collection_frontend"]=self["webpackChunkmovie_collection_frontend"]||[]).push([[395],{395:function(e,a,s){s.r(a),s.d(a,{default:function(){return z}});var i=s(641),t=s(33),l=s(751);const o={class:"home"},d={class:"hero-section"},c={class:"carousel-content"},r={class:"carousel-actions"},n={class:"features-section"},v={class:"container"},u={class:"features-grid"},k={class:"feature-card"},p={class:"feature-card"},g={class:"feature-card"},h={class:"feature-card"},m={class:"popular-section"},f={class:"container"},L={class:"section-header"},b={class:"movies-grid"},C=["onClick"],_={class:"movie-poster"},F=["src","alt"],y={class:"movie-overlay"},M={class:"movie-rating"},w={class:"movie-info"},I={class:"movie-title"},P={class:"movie-year"};function T(e,a,s,T,$,X){const D=(0,i.g2)("el-button"),W=(0,i.g2)("Star"),E=(0,i.g2)("el-icon"),z=(0,i.g2)("el-carousel-item"),R=(0,i.g2)("el-carousel"),j=(0,i.g2)("VideoCamera"),x=(0,i.g2)("ChatDotRound"),S=(0,i.g2)("TrendCharts"),V=(0,i.g2)("Plus"),K=(0,i.gN)("loading");return(0,i.uX)(),(0,i.CE)("div",o,[(0,i.Lk)("section",d,[(0,i.bF)(R,{height:"400px","indicator-position":"outside"},{default:(0,i.k6)(()=>[((0,i.uX)(!0),(0,i.CE)(i.FK,null,(0,i.pI)(X.bannerMovies,s=>((0,i.uX)(),(0,i.Wv)(z,{key:s.id},{default:(0,i.k6)(()=>[(0,i.Lk)("div",{class:"carousel-item",style:(0,t.Tr)({backgroundImage:`url(${s.backdrop})`})},[(0,i.Lk)("div",c,[(0,i.Lk)("h2",null,(0,t.v_)(s.title),1),(0,i.Lk)("p",null,(0,t.v_)(s.overview),1),(0,i.Lk)("div",r,[(0,i.bF)(D,{type:"primary",size:"large",onClick:e=>X.viewMovie(s.id)},{default:(0,i.k6)(()=>a[1]||(a[1]=[(0,i.eW)(" 查看详情 ")])),_:2,__:[1]},1032,["onClick"]),e.isLoggedIn?((0,i.uX)(),(0,i.Wv)(D,{key:0,size:"large",onClick:e=>X.addToCollection(s.id)},{default:(0,i.k6)(()=>[(0,i.bF)(E,null,{default:(0,i.k6)(()=>[(0,i.bF)(W)]),_:1}),a[2]||(a[2]=(0,i.eW)(" 收藏 "))]),_:2,__:[2]},1032,["onClick"])):(0,i.Q3)("",!0)])])],4)]),_:2},1024))),128))]),_:1})]),(0,i.Lk)("section",n,[(0,i.Lk)("div",v,[a[11]||(a[11]=(0,i.Lk)("h2",{class:"section-title"},"功能特色",-1)),(0,i.Lk)("div",u,[(0,i.Lk)("div",k,[(0,i.bF)(E,{class:"feature-icon"},{default:(0,i.k6)(()=>[(0,i.bF)(j)]),_:1}),a[3]||(a[3]=(0,i.Lk)("h3",null,"电影浏览",-1)),a[4]||(a[4]=(0,i.Lk)("p",null,"浏览海量电影资源，发现你喜欢的影片",-1))]),(0,i.Lk)("div",p,[(0,i.bF)(E,{class:"feature-icon"},{default:(0,i.k6)(()=>[(0,i.bF)(W)]),_:1}),a[5]||(a[5]=(0,i.Lk)("h3",null,"收藏管理",-1)),a[6]||(a[6]=(0,i.Lk)("p",null,"收藏喜欢的电影，建立个人影片库",-1))]),(0,i.Lk)("div",g,[(0,i.bF)(E,{class:"feature-icon"},{default:(0,i.k6)(()=>[(0,i.bF)(x)]),_:1}),a[7]||(a[7]=(0,i.Lk)("h3",null,"评分评论",-1)),a[8]||(a[8]=(0,i.Lk)("p",null,"为电影评分，分享观影感受",-1))]),(0,i.Lk)("div",h,[(0,i.bF)(E,{class:"feature-icon"},{default:(0,i.k6)(()=>[(0,i.bF)(S)]),_:1}),a[9]||(a[9]=(0,i.Lk)("h3",null,"数据统计",-1)),a[10]||(a[10]=(0,i.Lk)("p",null,"查看热门排行榜和个人观影统计",-1))])])])]),(0,i.Lk)("section",m,[(0,i.Lk)("div",f,[(0,i.Lk)("div",L,[a[13]||(a[13]=(0,i.Lk)("h2",{class:"section-title"},"热门电影",-1)),(0,i.bF)(D,{type:"text",onClick:a[0]||(a[0]=a=>e.$router.push("/movies"))},{default:(0,i.k6)(()=>a[12]||(a[12]=[(0,i.eW)("查看更多")])),_:1,__:[12]})]),(0,i.bo)(((0,i.uX)(),(0,i.CE)("div",b,[((0,i.uX)(!0),(0,i.CE)(i.FK,null,(0,i.pI)(X.formattedPopularMovies,a=>((0,i.uX)(),(0,i.CE)("div",{class:"movie-card",key:a.id,onClick:e=>X.viewMovie(a.id)},[(0,i.Lk)("div",_,[(0,i.Lk)("img",{src:a.poster||"/placeholder.jpg",alt:a.title},null,8,F),(0,i.Lk)("div",y,[(0,i.Lk)("div",M,[(0,i.bF)(E,null,{default:(0,i.k6)(()=>[(0,i.bF)(W)]),_:1}),(0,i.eW)(" "+(0,t.v_)(a.rating),1)]),e.isLoggedIn?((0,i.uX)(),(0,i.Wv)(D,{key:0,type:"primary",size:"small",circle:"",onClick:(0,l.D$)(e=>X.addToCollection(a.id),["stop"]),loading:$.collectingIds.includes(a.id)},{default:(0,i.k6)(()=>[(0,i.bF)(E,null,{default:(0,i.k6)(()=>[(0,i.bF)(V)]),_:1})]),_:2},1032,["onClick","loading"])):(0,i.Q3)("",!0)])]),(0,i.Lk)("div",w,[(0,i.Lk)("h4",I,(0,t.v_)(a.title),1),(0,i.Lk)("p",P,(0,t.v_)(a.year),1)])],8,C))),128))])),[[K,e.loading]])])]),a[14]||(a[14]=(0,i.Fv)('<section class="stats-section" data-v-7a005463><div class="container" data-v-7a005463><div class="stats-grid" data-v-7a005463><div class="stat-item" data-v-7a005463><div class="stat-number" data-v-7a005463>10,000+</div><div class="stat-label" data-v-7a005463>电影数量</div></div><div class="stat-item" data-v-7a005463><div class="stat-number" data-v-7a005463>50,000+</div><div class="stat-label" data-v-7a005463>用户收藏</div></div><div class="stat-item" data-v-7a005463><div class="stat-number" data-v-7a005463>100,000+</div><div class="stat-label" data-v-7a005463>用户评分</div></div><div class="stat-item" data-v-7a005463><div class="stat-number" data-v-7a005463>5,000+</div><div class="stat-label" data-v-7a005463>活跃用户</div></div></div></div></section>',1))])}var $=s(548),X=s(278),D={name:"Home",components:{VideoCamera:$.VideoCamera,Star:$.Star,ChatDotRound:$.ChatDotRound,TrendCharts:$.TrendCharts,Plus:$.Plus},data(){return{collectingIds:[]}},computed:{...(0,X.L8)("user",["isLoggedIn"]),...(0,X.L8)("movie",["popularMovies","loading"]),bannerMovies(){return this.popularMovies.slice(0,3).map(e=>({id:e.id,title:e.title,overview:e.overview||"暂无简介",backdrop:e.backdropPath||"/placeholder.jpg"}))},formattedPopularMovies(){return this.popularMovies.map(e=>({id:e.id,title:e.title,year:e.releaseDate?new Date(e.releaseDate).getFullYear():"未知",rating:e.averageRating||0,poster:e.posterPath||"/placeholder.jpg"}))}},async mounted(){await this.loadPopularMovies()},methods:{...(0,X.i0)("movie",["fetchPopularMovies"]),...(0,X.i0)("collection",{addMovieToCollection:"addToCollection"}),async loadPopularMovies(){try{await this.fetchPopularMovies({page:0,size:6})}catch(e){console.error("加载热门电影失败:",e),this.$message.error("加载热门电影失败")}},viewMovie(e){this.$router.push(`/movies/${e}`)},async addToCollection(e){if(!this.isLoggedIn)return this.$message.warning("请先登录"),void this.$router.push("/login");try{this.collectingIds.push(e),await this.addMovieToCollection(e),this.$message.success("收藏成功")}catch(a){console.error("收藏失败:",a),this.$message.error(a.message||"收藏失败")}finally{const a=this.collectingIds.indexOf(e);a>-1&&this.collectingIds.splice(a,1)}}}},W=s(262);const E=(0,W.A)(D,[["render",T],["__scopeId","data-v-7a005463"]]);var z=E}}]);
//# sourceMappingURL=395.48ee123a.js.map
{"version": 3, "file": "js/395.48ee123a.js", "mappings": "oOACOA,MAAM,Q,GAEAA,MAAM,gB,GAIFA,MAAM,oB,GAGJA,MAAM,oB,GAgBZA,MAAM,oB,GACRA,MAAM,a,GAEJA,MAAM,iB,GACJA,MAAM,gB,GAKNA,MAAM,gB,GAKNA,MAAM,gB,GAKNA,MAAM,gB,GAURA,MAAM,mB,GACRA,MAAM,a,GACJA,MAAM,kB,GAINA,MAAM,e,iBAEFA,MAAM,gB,mBAEJA,MAAM,iB,GACJA,MAAM,gB,GAgBVA,MAAM,c,GACLA,MAAM,e,GACPA,MAAM,c,kSAnFrB,QAiHM,MAjHN,EAiHM,EA/GJ,QAoBU,UApBV,EAoBU,EAnBR,QAkBc,GAlBDC,OAAO,QAAQ,qBAAmB,W,kBAC3B,IAA4B,G,aAA9C,QAgBmB,mBAhBc,EAAAC,aAARC,K,WAAzB,QAgBmB,GAhB6BC,IAAKD,EAAKE,I,kBACxD,IAcM,EAdN,QAcM,OAdDL,MAAM,gBAAiBM,OAAK,gCAA4BH,EAAKI,e,EAChE,QAYM,MAZN,EAYM,EAXJ,QAAyB,mBAAlBJ,EAAKK,OAAK,IACjB,QAA0B,kBAApBL,EAAKM,UAAQ,IACnB,QAQM,MARN,EAQM,EAPJ,QAEY,GAFDC,KAAK,UAAUC,KAAK,QAAS,QAAK,GAAE,EAAAC,UAAUT,EAAKE,K,kBAAK,IAEnE,c,QAFmE,a,8BAGHQ,EAAU,a,WAA1E,QAGY,G,MAHDF,KAAK,QAAS,QAAK,GAAE,EAAAG,gBAAgBX,EAAKE,K,kBACnD,IAA2B,EAA3B,QAA2B,Q,iBAAlB,IAAQ,EAAR,QAAQ,K,2BAAU,W,uFAWzC,QA0BU,UA1BV,EA0BU,EAzBR,QAwBM,MAxBN,EAwBM,C,eAvBJ,QAAmC,MAA/BL,MAAM,iBAAgB,QAAI,KAC9B,QAqBM,MArBN,EAqBM,EApBJ,QAIM,MAJN,EAIM,EAHJ,QAAuD,GAA9CA,MAAM,gBAAc,C,iBAAC,IAAe,EAAf,QAAe,K,mBAC7C,QAAa,UAAT,QAAI,I,aACR,QAAwB,SAArB,qBAAiB,OAEtB,QAIM,MAJN,EAIM,EAHJ,QAAgD,GAAvCA,MAAM,gBAAc,C,iBAAC,IAAQ,EAAR,QAAQ,K,mBACtC,QAAa,UAAT,QAAI,I,aACR,QAAsB,SAAnB,mBAAe,OAEpB,QAIM,MAJN,EAIM,EAHJ,QAAwD,GAA/CA,MAAM,gBAAc,C,iBAAC,IAAgB,EAAhB,QAAgB,K,mBAC9C,QAAa,UAAT,QAAI,I,aACR,QAAmB,SAAhB,gBAAY,OAEjB,QAIM,MAJN,EAIM,EAHJ,QAAuD,GAA9CA,MAAM,gBAAc,C,iBAAC,IAAe,EAAf,QAAe,K,mBAC7C,QAAa,UAAT,QAAI,I,eACR,QAAqB,SAAlB,kBAAc,aAOzB,QAkCU,UAlCV,EAkCU,EAjCR,QAgCM,MAhCN,EAgCM,EA/BJ,QAGM,MAHN,EAGM,C,eAFJ,QAAmC,MAA/BA,MAAM,iBAAgB,QAAI,KAC9B,QAAwE,GAA7DU,KAAK,OAAQ,QAAK,eAAEK,EAAAA,QAAQC,KAAK,a,kBAAY,IAAI,gB,QAAJ,W,sCAE1D,QA0BM,MA1BN,EA0BM,G,aAzBJ,QAwBM,mBAxBkC,EAAAC,uBAATC,K,WAA/B,QAwBM,OAxBDlB,MAAM,aAAsDI,IAAKc,EAAMb,GAAK,QAAK,GAAE,EAAAO,UAAUM,EAAMb,K,EACtG,QAkBM,MAlBN,EAkBM,EAjBJ,QAAoE,OAA9Dc,IAAKD,EAAME,QAAU,mBAAqBC,IAAKH,EAAMV,O,WAC3D,QAeM,MAfN,EAeM,EAdJ,QAGM,MAHN,EAGM,EAFJ,QAA2B,Q,iBAAlB,IAAQ,EAAR,QAAQ,K,eAAU,KAC3B,QAAGU,EAAMI,QAAM,KAQTT,EAAU,a,WANlB,QASY,G,MARVH,KAAK,UACLC,KAAK,QACLY,OAAA,GACC,SAAK,WAAO,EAAAT,gBAAgBI,EAAMb,IAAE,UACpCmB,QAAS,EAAAC,cAAcC,SAASR,EAAMb,K,kBAGvC,IAA2B,EAA3B,QAA2B,Q,iBAAlB,IAAQ,EAAR,QAAQ,K,+DAIvB,QAGM,MAHN,EAGM,EAFJ,QAA8C,KAA9C,GAA8C,QAAnBa,EAAMV,OAAK,IACtC,QAA0C,IAA1C,GAA0C,QAAjBU,EAAMS,MAAI,M,oBAvBLH,EAAAA,e,8xBA4D5C,GACEI,KAAM,OACNC,WAAY,CACVC,YAAW,cACXC,KAAI,OACJC,aAAY,eACZC,YAAW,cACXC,KAAI,QAEN,IAAAC,GACE,MAAO,CACLV,cAAe,GAEnB,EACAW,SAAU,KACL,QAAW,OAAQ,CAAC,mBACpB,QAAW,QAAS,CAAC,gBAAiB,YAGzC,YAAAlC,GACE,OAAOmC,KAAKC,cAAcC,MAAM,EAAG,GAAGC,IAAItB,IAAS,CACjDb,GAAIa,EAAMb,GACVG,MAAOU,EAAMV,MACbC,SAAUS,EAAMT,UAAY,OAC5BF,SAAUW,EAAMuB,cAAgB,qBAEpC,EAGA,sBAAAxB,GACE,OAAOoB,KAAKC,cAAcE,IAAItB,IAAS,CACrCb,GAAIa,EAAMb,GACVG,MAAOU,EAAMV,MACbmB,KAAMT,EAAMwB,YAAc,IAAIC,KAAKzB,EAAMwB,aAAaE,cAAgB,KACtEtB,OAAQJ,EAAM2B,eAAiB,EAC/BzB,OAAQF,EAAM4B,YAAc,qBAEhC,GAEF,aAAMC,SACEV,KAAKW,mBACb,EACAC,QAAS,KACJ,QAAW,QAAS,CAAC,2BACrB,QAAW,aAAc,CAAEC,qBAAsB,oBAEpD,uBAAMF,GACJ,UACQX,KAAKc,mBAAmB,CAAEC,KAAM,EAAGzC,KAAM,GACjD,CAAE,MAAO0C,GACPC,QAAQD,MAAM,YAAaA,GAC3BhB,KAAKkB,SAASF,MAAM,WACtB,CACF,EAEA,SAAAzC,CAAUP,GACRgC,KAAKtB,QAAQC,KAAK,WAAWX,IAC/B,EAEA,qBAAMS,CAAgB0C,GACpB,IAAKnB,KAAKxB,WAGR,OAFAwB,KAAKkB,SAASE,QAAQ,aACtBpB,KAAKtB,QAAQC,KAAK,UAIpB,IACEqB,KAAKZ,cAAcT,KAAKwC,SAElBnB,KAAKa,qBAAqBM,GAChCnB,KAAKkB,SAASG,QAAQ,OACxB,CAAE,MAAOL,GACPC,QAAQD,MAAM,QAASA,GACvBhB,KAAKkB,SAASF,MAAMA,EAAMM,SAAW,OACvC,CAAE,QACA,MAAMC,EAAQvB,KAAKZ,cAAcoC,QAAQL,GACrCI,GAAS,GACXvB,KAAKZ,cAAcqC,OAAOF,EAAO,EAErC,CACF,I,SClMJ,MAAMG,GAA2B,OAAgB,EAAQ,CAAC,CAAC,SAASC,GAAQ,CAAC,YAAY,qBAEzF,O", "sources": ["webpack://movie-collection-frontend/./src/views/Home.vue", "webpack://movie-collection-frontend/./src/views/Home.vue?9051"], "sourcesContent": ["<template>\n  <div class=\"home\">\n    <!-- 轮播图区域 -->\n    <section class=\"hero-section\">\n      <el-carousel height=\"400px\" indicator-position=\"outside\">\n        <el-carousel-item v-for=\"item in bannerMovies\" :key=\"item.id\">\n          <div class=\"carousel-item\" :style=\"{ backgroundImage: `url(${item.backdrop})` }\">\n            <div class=\"carousel-content\">\n              <h2>{{ item.title }}</h2>\n              <p>{{ item.overview }}</p>\n              <div class=\"carousel-actions\">\n                <el-button type=\"primary\" size=\"large\" @click=\"viewMovie(item.id)\">\n                  查看详情\n                </el-button>\n                <el-button size=\"large\" @click=\"addToCollection(item.id)\" v-if=\"isLoggedIn\">\n                  <el-icon><Star /></el-icon>\n                  收藏\n                </el-button>\n              </div>\n            </div>\n          </div>\n        </el-carousel-item>\n      </el-carousel>\n    </section>\n\n    <!-- 功能介绍 -->\n    <section class=\"features-section\">\n      <div class=\"container\">\n        <h2 class=\"section-title\">功能特色</h2>\n        <div class=\"features-grid\">\n          <div class=\"feature-card\">\n            <el-icon class=\"feature-icon\"><VideoCamera /></el-icon>\n            <h3>电影浏览</h3>\n            <p>浏览海量电影资源，发现你喜欢的影片</p>\n          </div>\n          <div class=\"feature-card\">\n            <el-icon class=\"feature-icon\"><Star /></el-icon>\n            <h3>收藏管理</h3>\n            <p>收藏喜欢的电影，建立个人影片库</p>\n          </div>\n          <div class=\"feature-card\">\n            <el-icon class=\"feature-icon\"><ChatDotRound /></el-icon>\n            <h3>评分评论</h3>\n            <p>为电影评分，分享观影感受</p>\n          </div>\n          <div class=\"feature-card\">\n            <el-icon class=\"feature-icon\"><TrendCharts /></el-icon>\n            <h3>数据统计</h3>\n            <p>查看热门排行榜和个人观影统计</p>\n          </div>\n        </div>\n      </div>\n    </section>\n\n    <!-- 热门电影 -->\n    <section class=\"popular-section\">\n      <div class=\"container\">\n        <div class=\"section-header\">\n          <h2 class=\"section-title\">热门电影</h2>\n          <el-button type=\"text\" @click=\"$router.push('/movies')\">查看更多</el-button>\n        </div>\n        <div class=\"movies-grid\" v-loading=\"loading\">\n          <div class=\"movie-card\" v-for=\"movie in formattedPopularMovies\" :key=\"movie.id\" @click=\"viewMovie(movie.id)\">\n            <div class=\"movie-poster\">\n              <img :src=\"movie.poster || '/placeholder.jpg'\" :alt=\"movie.title\" />\n              <div class=\"movie-overlay\">\n                <div class=\"movie-rating\">\n                  <el-icon><Star /></el-icon>\n                  {{ movie.rating }}\n                </div>\n                <el-button\n                  type=\"primary\"\n                  size=\"small\"\n                  circle\n                  @click.stop=\"addToCollection(movie.id)\"\n                  :loading=\"collectingIds.includes(movie.id)\"\n                  v-if=\"isLoggedIn\"\n                >\n                  <el-icon><Plus /></el-icon>\n                </el-button>\n              </div>\n            </div>\n            <div class=\"movie-info\">\n              <h4 class=\"movie-title\">{{ movie.title }}</h4>\n              <p class=\"movie-year\">{{ movie.year }}</p>\n            </div>\n          </div>\n        </div>\n      </div>\n    </section>\n\n    <!-- 统计数据 -->\n    <section class=\"stats-section\">\n      <div class=\"container\">\n        <div class=\"stats-grid\">\n          <div class=\"stat-item\">\n            <div class=\"stat-number\">10,000+</div>\n            <div class=\"stat-label\">电影数量</div>\n          </div>\n          <div class=\"stat-item\">\n            <div class=\"stat-number\">50,000+</div>\n            <div class=\"stat-label\">用户收藏</div>\n          </div>\n          <div class=\"stat-item\">\n            <div class=\"stat-number\">100,000+</div>\n            <div class=\"stat-label\">用户评分</div>\n          </div>\n          <div class=\"stat-item\">\n            <div class=\"stat-number\">5,000+</div>\n            <div class=\"stat-label\">活跃用户</div>\n          </div>\n        </div>\n      </div>\n    </section>\n  </div>\n</template>\n\n<script>\nimport { VideoCamera, Star, ChatDotRound, TrendCharts, Plus } from '@element-plus/icons-vue'\nimport { mapGetters, mapActions } from 'vuex'\n\nexport default {\n  name: 'Home',\n  components: {\n    VideoCamera,\n    Star,\n    ChatDotRound,\n    TrendCharts,\n    Plus\n  },\n  data() {\n    return {\n      collectingIds: [] // 正在收藏的电影ID列表\n    }\n  },\n  computed: {\n    ...mapGetters('user', ['isLoggedIn']),\n    ...mapGetters('movie', ['popularMovies', 'loading']),\n\n    // 轮播图电影数据\n    bannerMovies() {\n      return this.popularMovies.slice(0, 3).map(movie => ({\n        id: movie.id,\n        title: movie.title,\n        overview: movie.overview || '暂无简介',\n        backdrop: movie.backdropPath || '/placeholder.jpg'\n      }))\n    },\n\n    // 格式化热门电影数据用于显示\n    formattedPopularMovies() {\n      return this.popularMovies.map(movie => ({\n        id: movie.id,\n        title: movie.title,\n        year: movie.releaseDate ? new Date(movie.releaseDate).getFullYear() : '未知',\n        rating: movie.averageRating || 0,\n        poster: movie.posterPath || '/placeholder.jpg'\n      }))\n    }\n  },\n  async mounted() {\n    await this.loadPopularMovies()\n  },\n  methods: {\n    ...mapActions('movie', ['fetchPopularMovies']),\n    ...mapActions('collection', { addMovieToCollection: 'addToCollection' }),\n\n    async loadPopularMovies() {\n      try {\n        await this.fetchPopularMovies({ page: 0, size: 6 })\n      } catch (error) {\n        console.error('加载热门电影失败:', error)\n        this.$message.error('加载热门电影失败')\n      }\n    },\n\n    viewMovie(id) {\n      this.$router.push(`/movies/${id}`)\n    },\n\n    async addToCollection(movieId) {\n      if (!this.isLoggedIn) {\n        this.$message.warning('请先登录')\n        this.$router.push('/login')\n        return\n      }\n\n      try {\n        this.collectingIds.push(movieId)\n\n        await this.addMovieToCollection(movieId)\n        this.$message.success('收藏成功')\n      } catch (error) {\n        console.error('收藏失败:', error)\n        this.$message.error(error.message || '收藏失败')\n      } finally {\n        const index = this.collectingIds.indexOf(movieId)\n        if (index > -1) {\n          this.collectingIds.splice(index, 1)\n        }\n      }\n    }\n  }\n}\n</script>\n\n<style scoped>\n.home {\n  min-height: 100vh;\n}\n\n/* 轮播图样式 */\n.hero-section {\n  margin-bottom: 60px;\n}\n\n.carousel-item {\n  height: 400px;\n  background-size: cover;\n  background-position: center;\n  position: relative;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.carousel-item::before {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: rgba(0, 0, 0, 0.4);\n}\n\n.carousel-content {\n  position: relative;\n  z-index: 1;\n  text-align: center;\n  color: white;\n  max-width: 600px;\n  padding: 0 20px;\n}\n\n.carousel-content h2 {\n  font-size: 36px;\n  margin-bottom: 16px;\n  font-weight: bold;\n}\n\n.carousel-content p {\n  font-size: 16px;\n  margin-bottom: 24px;\n  line-height: 1.6;\n}\n\n.carousel-actions {\n  display: flex;\n  gap: 16px;\n  justify-content: center;\n}\n\n/* 通用样式 */\n.container {\n  max-width: 1200px;\n  margin: 0 auto;\n  padding: 0 20px;\n}\n\n.section-title {\n  font-size: 28px;\n  font-weight: bold;\n  text-align: center;\n  margin-bottom: 40px;\n  color: #333;\n}\n\n.section-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 40px;\n}\n\n/* 功能特色样式 */\n.features-section {\n  padding: 60px 0;\n  background-color: #f8f9fa;\n}\n\n.features-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\n  gap: 30px;\n}\n\n.feature-card {\n  text-align: center;\n  padding: 30px 20px;\n  background: white;\n  border-radius: 8px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n  transition: transform 0.3s;\n}\n\n.feature-card:hover {\n  transform: translateY(-5px);\n}\n\n.feature-icon {\n  font-size: 48px;\n  color: #409eff;\n  margin-bottom: 20px;\n}\n\n.feature-card h3 {\n  font-size: 20px;\n  margin-bottom: 12px;\n  color: #333;\n}\n\n.feature-card p {\n  color: #666;\n  line-height: 1.6;\n}\n\n/* 热门电影样式 */\n.popular-section {\n  padding: 60px 0;\n}\n\n.movies-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));\n  gap: 20px;\n}\n\n.movie-card {\n  cursor: pointer;\n  transition: transform 0.3s;\n}\n\n.movie-card:hover {\n  transform: scale(1.05);\n}\n\n.movie-poster {\n  position: relative;\n  aspect-ratio: 2/3;\n  border-radius: 8px;\n  overflow: hidden;\n  margin-bottom: 12px;\n}\n\n.movie-poster img {\n  width: 100%;\n  height: 100%;\n  object-fit: cover;\n}\n\n.movie-overlay {\n  position: absolute;\n  top: 8px;\n  right: 8px;\n  display: flex;\n  flex-direction: column;\n  gap: 8px;\n}\n\n.movie-rating {\n  background: rgba(0, 0, 0, 0.7);\n  color: white;\n  padding: 4px 8px;\n  border-radius: 4px;\n  font-size: 12px;\n  display: flex;\n  align-items: center;\n  gap: 4px;\n}\n\n.movie-info {\n  text-align: center;\n}\n\n.movie-title {\n  font-size: 14px;\n  font-weight: 600;\n  margin-bottom: 4px;\n  color: #333;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n}\n\n.movie-year {\n  font-size: 12px;\n  color: #666;\n  margin: 0;\n}\n\n/* 统计数据样式 */\n.stats-section {\n  padding: 60px 0;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n}\n\n.stats-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n  gap: 30px;\n}\n\n.stat-item {\n  text-align: center;\n}\n\n.stat-number {\n  font-size: 36px;\n  font-weight: bold;\n  margin-bottom: 8px;\n}\n\n.stat-label {\n  font-size: 16px;\n  opacity: 0.9;\n}\n\n/* 响应式设计 */\n@media (max-width: 768px) {\n  .carousel-content h2 {\n    font-size: 24px;\n  }\n  \n  .carousel-actions {\n    flex-direction: column;\n    align-items: center;\n  }\n  \n  .section-header {\n    flex-direction: column;\n    gap: 20px;\n  }\n  \n  .movies-grid {\n    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));\n  }\n}\n</style>\n", "import { render } from \"./Home.vue?vue&type=template&id=7a005463&scoped=true\"\nimport script from \"./Home.vue?vue&type=script&lang=js\"\nexport * from \"./Home.vue?vue&type=script&lang=js\"\n\nimport \"./Home.vue?vue&type=style&index=0&id=7a005463&scoped=true&lang=css\"\n\nimport exportComponent from \"../../node_modules/vue-loader/dist/exportHelper.js\"\nconst __exports__ = /*#__PURE__*/exportComponent(script, [['render',render],['__scopeId',\"data-v-7a005463\"]])\n\nexport default __exports__"], "names": ["class", "height", "bannerMovies", "item", "key", "id", "style", "backdrop", "title", "overview", "type", "size", "viewMovie", "isLoggedIn", "addToCollection", "$router", "push", "formattedPopularMovies", "movie", "src", "poster", "alt", "rating", "circle", "loading", "collectingIds", "includes", "year", "name", "components", "VideoCamera", "Star", "ChatDotRound", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Plus", "data", "computed", "this", "popularMovies", "slice", "map", "<PERSON><PERSON><PERSON>", "releaseDate", "Date", "getFullYear", "averageRating", "posterPath", "mounted", "loadPopularMovies", "methods", "addMovieToCollection", "fetchPopularMovies", "page", "error", "console", "$message", "movieId", "warning", "success", "message", "index", "indexOf", "splice", "__exports__", "render"], "sourceRoot": ""}
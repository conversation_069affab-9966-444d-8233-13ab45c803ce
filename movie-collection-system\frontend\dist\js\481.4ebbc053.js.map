{"version": 3, "file": "js/481.4ebbc053.js", "mappings": "mNACOA,MAAM,iB,0CAAX,QAKM,MALN,EAKM,cAJJ,QAGM,OAHDA,MAAM,aAAW,EACpB,QAAY,UAAR,QACJ,QAAkB,SAAf,iB,MAMT,OACEC,KAAM,Y,SCJR,MAAMC,GAA2B,OAAgB,EAAQ,CAAC,CAAC,SAASC,GAAQ,CAAC,YAAY,qBAEzF,O", "sources": ["webpack://movie-collection-frontend/./src/views/Rankings.vue", "webpack://movie-collection-frontend/./src/views/Rankings.vue?1c49"], "sourcesContent": ["<template>\n  <div class=\"rankings-page\">\n    <div class=\"container\">\n      <h1>排行榜</h1>\n      <p>此页面正在开发中...</p>\n    </div>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: 'Rankings'\n}\n</script>\n\n<style scoped>\n.rankings-page {\n  min-height: 100vh;\n  padding: 40px 0;\n}\n\n.container {\n  max-width: 1200px;\n  margin: 0 auto;\n  padding: 0 20px;\n  text-align: center;\n}\n</style>\n", "import { render } from \"./Rankings.vue?vue&type=template&id=7685383d&scoped=true\"\nimport script from \"./Rankings.vue?vue&type=script&lang=js\"\nexport * from \"./Rankings.vue?vue&type=script&lang=js\"\n\nimport \"./Rankings.vue?vue&type=style&index=0&id=7685383d&scoped=true&lang=css\"\n\nimport exportComponent from \"../../node_modules/vue-loader/dist/exportHelper.js\"\nconst __exports__ = /*#__PURE__*/exportComponent(script, [['render',render],['__scopeId',\"data-v-7685383d\"]])\n\nexport default __exports__"], "names": ["class", "name", "__exports__", "render"], "sourceRoot": ""}
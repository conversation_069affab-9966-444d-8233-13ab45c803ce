"use strict";(self["webpackChunkmovie_collection_frontend"]=self["webpackChunkmovie_collection_frontend"]||[]).push([[976],{976:function(e,r,a){a.r(r),a.d(r,{default:function(){return D}});var s=a(641),l=a(751),t=a(33);const i={class:"register-page"},n={class:"register-container"},o={class:"register-form"},d={class:"form-footer"},u={class:"register-banner"},g={class:"banner-content"},m={class:"benefits"},c={class:"benefit-item"},p={class:"benefit-item"},k={class:"benefit-item"};function f(e,r,a,f,b,h){const L=(0,s.g2)("el-input"),v=(0,s.g2)("el-form-item"),w=(0,s.g2)("el-button"),D=(0,s.g2)("el-form"),F=(0,s.g2)("router-link"),_=(0,s.g2)("Star"),V=(0,s.g2)("el-icon"),C=(0,s.g2)("ChatDotRound"),R=(0,s.g2)("TrendCharts");return(0,s.uX)(),(0,s.CE)("div",i,[(0,s.Lk)("div",n,[(0,s.Lk)("div",o,[r[7]||(r[7]=(0,s.Lk)("div",{class:"form-header"},[(0,s.Lk)("h2",null,"注册"),(0,s.Lk)("p",null,"加入电影收藏管理系统")],-1)),(0,s.bF)(D,{ref:"registerForm",model:b.registerData,rules:b.registerRules,onSubmit:(0,l.D$)(h.handleRegister,["prevent"])},{default:(0,s.k6)(()=>[(0,s.bF)(v,{prop:"username"},{default:(0,s.k6)(()=>[(0,s.bF)(L,{modelValue:b.registerData.username,"onUpdate:modelValue":r[0]||(r[0]=e=>b.registerData.username=e),placeholder:"请输入用户名",size:"large","prefix-icon":"User"},null,8,["modelValue"])]),_:1}),(0,s.bF)(v,{prop:"email"},{default:(0,s.k6)(()=>[(0,s.bF)(L,{modelValue:b.registerData.email,"onUpdate:modelValue":r[1]||(r[1]=e=>b.registerData.email=e),placeholder:"请输入邮箱",size:"large","prefix-icon":"Message"},null,8,["modelValue"])]),_:1}),(0,s.bF)(v,{prop:"nickname"},{default:(0,s.k6)(()=>[(0,s.bF)(L,{modelValue:b.registerData.nickname,"onUpdate:modelValue":r[2]||(r[2]=e=>b.registerData.nickname=e),placeholder:"请输入昵称（可选）",size:"large","prefix-icon":"Avatar"},null,8,["modelValue"])]),_:1}),(0,s.bF)(v,{prop:"password"},{default:(0,s.k6)(()=>[(0,s.bF)(L,{modelValue:b.registerData.password,"onUpdate:modelValue":r[3]||(r[3]=e=>b.registerData.password=e),type:"password",placeholder:"请输入密码",size:"large","prefix-icon":"Lock","show-password":""},null,8,["modelValue"])]),_:1}),(0,s.bF)(v,{prop:"confirmPassword"},{default:(0,s.k6)(()=>[(0,s.bF)(L,{modelValue:b.registerData.confirmPassword,"onUpdate:modelValue":r[4]||(r[4]=e=>b.registerData.confirmPassword=e),type:"password",placeholder:"请确认密码",size:"large","prefix-icon":"Lock","show-password":"",onKeyup:(0,l.jR)(h.handleRegister,["enter"])},null,8,["modelValue","onKeyup"])]),_:1}),(0,s.bF)(v,null,{default:(0,s.k6)(()=>[(0,s.bF)(w,{type:"primary",size:"large",loading:b.loading,onClick:h.handleRegister,class:"register-button"},{default:(0,s.k6)(()=>[(0,s.eW)((0,t.v_)(b.loading?"注册中...":"注册"),1)]),_:1},8,["loading","onClick"])]),_:1})]),_:1},8,["model","rules","onSubmit"]),(0,s.Lk)("div",d,[(0,s.Lk)("p",null,[r[6]||(r[6]=(0,s.eW)(" 已有账号？ ")),(0,s.bF)(F,{to:"/login",class:"link"},{default:(0,s.k6)(()=>r[5]||(r[5]=[(0,s.eW)("立即登录")])),_:1,__:[5]})])])]),(0,s.Lk)("div",u,[(0,s.Lk)("div",g,[r[11]||(r[11]=(0,s.Lk)("h3",null,"开始你的观影之旅",-1)),r[12]||(r[12]=(0,s.Lk)("p",null,"记录每一部精彩电影，分享观影感受",-1)),(0,s.Lk)("div",m,[(0,s.Lk)("div",c,[(0,s.bF)(V,{class:"benefit-icon"},{default:(0,s.k6)(()=>[(0,s.bF)(_)]),_:1}),r[8]||(r[8]=(0,s.Lk)("div",null,[(0,s.Lk)("h4",null,"个人收藏"),(0,s.Lk)("p",null,"建立专属电影库")],-1))]),(0,s.Lk)("div",p,[(0,s.bF)(V,{class:"benefit-icon"},{default:(0,s.k6)(()=>[(0,s.bF)(C)]),_:1}),r[9]||(r[9]=(0,s.Lk)("div",null,[(0,s.Lk)("h4",null,"评分评论"),(0,s.Lk)("p",null,"分享观影感受")],-1))]),(0,s.Lk)("div",k,[(0,s.bF)(V,{class:"benefit-icon"},{default:(0,s.k6)(()=>[(0,s.bF)(R)]),_:1}),r[10]||(r[10]=(0,s.Lk)("div",null,[(0,s.Lk)("h4",null,"智能推荐"),(0,s.Lk)("p",null,"发现更多好电影")],-1))])])])])])])}var b=a(548),h=a(278),L={name:"Register",components:{User:b.User,Message:b.Message,Avatar:b.Avatar,Lock:b.Lock,Star:b.Star,ChatDotRound:b.ChatDotRound,TrendCharts:b.TrendCharts},data(){const e=(e,r,a)=>{r!==this.registerData.password?a(new Error("两次输入的密码不一致")):a()};return{loading:!1,registerData:{username:"",email:"",nickname:"",password:"",confirmPassword:""},registerRules:{username:[{required:!0,message:"请输入用户名",trigger:"blur"},{min:3,max:20,message:"用户名长度在3到20个字符",trigger:"blur"},{pattern:/^[a-zA-Z0-9_]+$/,message:"用户名只能包含字母、数字和下划线",trigger:"blur"}],email:[{required:!0,message:"请输入邮箱",trigger:"blur"},{type:"email",message:"请输入正确的邮箱格式",trigger:"blur"}],nickname:[{max:20,message:"昵称长度不能超过20个字符",trigger:"blur"}],password:[{required:!0,message:"请输入密码",trigger:"blur"},{min:6,max:20,message:"密码长度在6到20个字符",trigger:"blur"}],confirmPassword:[{required:!0,message:"请确认密码",trigger:"blur"},{validator:e,trigger:"blur"}]}}},methods:{...(0,h.i0)("user",["register"]),async handleRegister(){try{const e=await this.$refs.registerForm.validate();if(!e)return;this.loading=!0;const r=await this.register({username:this.registerData.username,email:this.registerData.email,password:this.registerData.password,nickname:this.registerData.nickname||this.registerData.username});r.success&&(this.$message.success("注册成功，请登录"),this.$router.push("/login"))}catch(e){this.$message.error(e.message||"注册失败")}finally{this.loading=!1}}}},v=a(262);const w=(0,v.A)(L,[["render",f],["__scopeId","data-v-eb709022"]]);var D=w}}]);
//# sourceMappingURL=976.29294908.js.map
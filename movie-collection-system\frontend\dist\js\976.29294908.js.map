{"version": 3, "file": "js/976.29294908.js", "mappings": "oOACOA,MAAM,iB,GACJA,MAAM,sB,GACJA,MAAM,iB,GA2EJA,MAAM,e,GAQRA,MAAM,mB,GACJA,MAAM,kB,GAGJA,MAAM,Y,GACJA,MAAM,gB,GAONA,MAAM,gB,GAONA,MAAM,gB,wQAxGrB,QAmHM,MAnHN,EAmHM,EAlHJ,QAiHM,MAjHN,EAiHM,EAhHJ,QAiFM,MAjFN,EAiFM,C,aAhFJ,QAGM,OAHDA,MAAM,eAAa,EACtB,QAAW,UAAP,OACJ,QAAiB,SAAd,gB,KAGL,QAmEU,GAlERC,IAAI,eACHC,MAAO,EAAAC,aACPC,MAAO,EAAAC,cACP,UAAM,QAAU,EAAAC,eAAc,c,kBAE/B,IAOe,EAPf,QAOe,GAPDC,KAAK,YAAU,C,iBAC3B,IAKE,EALF,QAKE,G,WAJS,EAAAJ,aAAaK,S,qCAAb,EAAAL,aAAqB,YAC9BM,YAAY,SACZC,KAAK,QACL,cAAY,Q,gCAIhB,QAOe,GAPDH,KAAK,SAAO,C,iBACxB,IAKE,EALF,QAKE,G,WAJS,EAAAJ,aAAaQ,M,qCAAb,EAAAR,aAAkB,SAC3BM,YAAY,QACZC,KAAK,QACL,cAAY,W,gCAIhB,QAOe,GAPDH,KAAK,YAAU,C,iBAC3B,IAKE,EALF,QAKE,G,WAJS,EAAAJ,aAAaS,S,qCAAb,EAAAT,aAAqB,YAC9BM,YAAY,YACZC,KAAK,QACL,cAAY,U,gCAIhB,QASe,GATDH,KAAK,YAAU,C,iBAC3B,IAOE,EAPF,QAOE,G,WANS,EAAAJ,aAAaU,S,qCAAb,EAAAV,aAAqB,YAC9BW,KAAK,WACLL,YAAY,QACZC,KAAK,QACL,cAAY,OACZ,oB,gCAIJ,QAUe,GAVDH,KAAK,mBAAiB,C,iBAClC,IAQE,EARF,QAQE,G,WAPS,EAAAJ,aAAaY,gB,qCAAb,EAAAZ,aAA4B,mBACrCW,KAAK,WACLL,YAAY,QACZC,KAAK,QACL,cAAY,OACZ,mBACC,SAAK,QAAQ,EAAAJ,eAAc,Y,0CAIhC,QAUe,Q,iBATb,IAQY,EARZ,QAQY,GAPVQ,KAAK,UACLJ,KAAK,QACJM,QAAS,EAAAA,QACT,QAAO,EAAAV,eACRN,MAAM,mB,kBAEN,IAA+B,E,iBAA5B,EAAAgB,QAAU,SAAW,MAAd,K,8EAKhB,QAKM,MALN,EAKM,EAJJ,QAGI,U,qBAHD,aAED,QAAwD,GAA3CC,GAAG,SAASjB,MAAM,Q,kBAAO,IAAI,c,QAAJ,W,oBAK5C,QA4BM,MA5BN,EA4BM,EA3BJ,QA0BM,MA1BN,EA0BM,C,eAzBJ,QAAiB,UAAb,YAAQ,I,eACZ,QAAuB,SAApB,oBAAgB,KACnB,QAsBM,MAtBN,EAsBM,EArBJ,QAMM,MANN,EAMM,EALJ,QAAgD,GAAvCA,MAAM,gBAAc,C,iBAAC,IAAQ,EAAR,QAAQ,K,mBACtC,QAGM,aAFJ,QAAa,UAAT,SACJ,QAAc,SAAX,a,OAGP,QAMM,MANN,EAMM,EALJ,QAAwD,GAA/CA,MAAM,gBAAc,C,iBAAC,IAAgB,EAAhB,QAAgB,K,mBAC9C,QAGM,aAFJ,QAAa,UAAT,SACJ,QAAa,SAAV,Y,OAGP,QAMM,MANN,EAMM,EALJ,QAAuD,GAA9CA,MAAM,gBAAc,C,iBAAC,IAAe,EAAf,QAAe,K,qBAC7C,QAGM,aAFJ,QAAa,UAAT,SACJ,QAAc,SAAX,a,sCAcnB,GACEkB,KAAM,WACNC,WAAY,CACVC,KAAI,OACJC,QAAO,UACPC,OAAM,SACNC,KAAI,OACJC,KAAI,OACJC,aAAY,eACZC,YAAW,eAEb,IAAAC,GAEE,MAAMC,EAA0B,CAACC,EAAMC,EAAOC,KACxCD,IAAUE,KAAK7B,aAAaU,SAC9BkB,EAAS,IAAIE,MAAM,eAEnBF,KAIJ,MAAO,CACLf,SAAS,EACTb,aAAc,CACZK,SAAU,GACVG,MAAO,GACPC,SAAU,GACVC,SAAU,GACVE,gBAAiB,IAEnBV,cAAe,CACbG,SAAU,CACR,CAAE0B,UAAU,EAAMC,QAAS,SAAUC,QAAS,QAC9C,CAAEC,IAAK,EAAGC,IAAK,GAAIH,QAAS,gBAAiBC,QAAS,QACtD,CAAEG,QAAS,kBAAmBJ,QAAS,mBAAoBC,QAAS,SAEtEzB,MAAO,CACL,CAAEuB,UAAU,EAAMC,QAAS,QAASC,QAAS,QAC7C,CAAEtB,KAAM,QAASqB,QAAS,aAAcC,QAAS,SAEnDxB,SAAU,CACR,CAAE0B,IAAK,GAAIH,QAAS,gBAAiBC,QAAS,SAEhDvB,SAAU,CACR,CAAEqB,UAAU,EAAMC,QAAS,QAASC,QAAS,QAC7C,CAAEC,IAAK,EAAGC,IAAK,GAAIH,QAAS,eAAgBC,QAAS,SAEvDrB,gBAAiB,CACf,CAAEmB,UAAU,EAAMC,QAAS,QAASC,QAAS,QAC7C,CAAEI,UAAWZ,EAAyBQ,QAAS,UAIvD,EACAK,QAAS,KACJ,QAAW,OAAQ,CAAC,aAEvB,oBAAMnC,GACJ,IACE,MAAMoC,QAAcV,KAAKW,MAAMC,aAAaC,WAC5C,IAAKH,EAAO,OAEZV,KAAKhB,SAAU,EAEf,MAAM8B,QAAed,KAAKe,SAAS,CACjCvC,SAAUwB,KAAK7B,aAAaK,SAC5BG,MAAOqB,KAAK7B,aAAaQ,MACzBE,SAAUmB,KAAK7B,aAAaU,SAC5BD,SAAUoB,KAAK7B,aAAaS,UAAYoB,KAAK7B,aAAaK,WAGxDsC,EAAOE,UACThB,KAAKiB,SAASD,QAAQ,YACtBhB,KAAKkB,QAAQC,KAAK,UAEtB,CAAE,MAAOC,GACPpB,KAAKiB,SAASG,MAAMA,EAAMjB,SAAW,OACvC,CAAE,QACAH,KAAKhB,SAAU,CACjB,CACF,I,SCpMJ,MAAMqC,GAA2B,OAAgB,EAAQ,CAAC,CAAC,SAASC,GAAQ,CAAC,YAAY,qBAEzF,O", "sources": ["webpack://movie-collection-frontend/./src/views/Register.vue", "webpack://movie-collection-frontend/./src/views/Register.vue?157d"], "sourcesContent": ["<template>\n  <div class=\"register-page\">\n    <div class=\"register-container\">\n      <div class=\"register-form\">\n        <div class=\"form-header\">\n          <h2>注册</h2>\n          <p>加入电影收藏管理系统</p>\n        </div>\n\n        <el-form\n          ref=\"registerForm\"\n          :model=\"registerData\"\n          :rules=\"registerRules\"\n          @submit.prevent=\"handleRegister\"\n        >\n          <el-form-item prop=\"username\">\n            <el-input\n              v-model=\"registerData.username\"\n              placeholder=\"请输入用户名\"\n              size=\"large\"\n              prefix-icon=\"User\"\n            />\n          </el-form-item>\n\n          <el-form-item prop=\"email\">\n            <el-input\n              v-model=\"registerData.email\"\n              placeholder=\"请输入邮箱\"\n              size=\"large\"\n              prefix-icon=\"Message\"\n            />\n          </el-form-item>\n\n          <el-form-item prop=\"nickname\">\n            <el-input\n              v-model=\"registerData.nickname\"\n              placeholder=\"请输入昵称（可选）\"\n              size=\"large\"\n              prefix-icon=\"Avatar\"\n            />\n          </el-form-item>\n\n          <el-form-item prop=\"password\">\n            <el-input\n              v-model=\"registerData.password\"\n              type=\"password\"\n              placeholder=\"请输入密码\"\n              size=\"large\"\n              prefix-icon=\"Lock\"\n              show-password\n            />\n          </el-form-item>\n\n          <el-form-item prop=\"confirmPassword\">\n            <el-input\n              v-model=\"registerData.confirmPassword\"\n              type=\"password\"\n              placeholder=\"请确认密码\"\n              size=\"large\"\n              prefix-icon=\"Lock\"\n              show-password\n              @keyup.enter=\"handleRegister\"\n            />\n          </el-form-item>\n\n          <el-form-item>\n            <el-button\n              type=\"primary\"\n              size=\"large\"\n              :loading=\"loading\"\n              @click=\"handleRegister\"\n              class=\"register-button\"\n            >\n              {{ loading ? '注册中...' : '注册' }}\n            </el-button>\n          </el-form-item>\n        </el-form>\n\n        <div class=\"form-footer\">\n          <p>\n            已有账号？\n            <router-link to=\"/login\" class=\"link\">立即登录</router-link>\n          </p>\n        </div>\n      </div>\n\n      <div class=\"register-banner\">\n        <div class=\"banner-content\">\n          <h3>开始你的观影之旅</h3>\n          <p>记录每一部精彩电影，分享观影感受</p>\n          <div class=\"benefits\">\n            <div class=\"benefit-item\">\n              <el-icon class=\"benefit-icon\"><Star /></el-icon>\n              <div>\n                <h4>个人收藏</h4>\n                <p>建立专属电影库</p>\n              </div>\n            </div>\n            <div class=\"benefit-item\">\n              <el-icon class=\"benefit-icon\"><ChatDotRound /></el-icon>\n              <div>\n                <h4>评分评论</h4>\n                <p>分享观影感受</p>\n              </div>\n            </div>\n            <div class=\"benefit-item\">\n              <el-icon class=\"benefit-icon\"><TrendCharts /></el-icon>\n              <div>\n                <h4>智能推荐</h4>\n                <p>发现更多好电影</p>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport { User, Message, Avatar, Lock, Star, ChatDotRound, TrendCharts } from '@element-plus/icons-vue'\nimport { mapActions } from 'vuex'\n\nexport default {\n  name: 'Register',\n  components: {\n    User,\n    Message,\n    Avatar,\n    Lock,\n    Star,\n    ChatDotRound,\n    TrendCharts\n  },\n  data() {\n    // 自定义验证规则\n    const validateConfirmPassword = (rule, value, callback) => {\n      if (value !== this.registerData.password) {\n        callback(new Error('两次输入的密码不一致'))\n      } else {\n        callback()\n      }\n    }\n\n    return {\n      loading: false,\n      registerData: {\n        username: '',\n        email: '',\n        nickname: '',\n        password: '',\n        confirmPassword: ''\n      },\n      registerRules: {\n        username: [\n          { required: true, message: '请输入用户名', trigger: 'blur' },\n          { min: 3, max: 20, message: '用户名长度在3到20个字符', trigger: 'blur' },\n          { pattern: /^[a-zA-Z0-9_]+$/, message: '用户名只能包含字母、数字和下划线', trigger: 'blur' }\n        ],\n        email: [\n          { required: true, message: '请输入邮箱', trigger: 'blur' },\n          { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }\n        ],\n        nickname: [\n          { max: 20, message: '昵称长度不能超过20个字符', trigger: 'blur' }\n        ],\n        password: [\n          { required: true, message: '请输入密码', trigger: 'blur' },\n          { min: 6, max: 20, message: '密码长度在6到20个字符', trigger: 'blur' }\n        ],\n        confirmPassword: [\n          { required: true, message: '请确认密码', trigger: 'blur' },\n          { validator: validateConfirmPassword, trigger: 'blur' }\n        ]\n      }\n    }\n  },\n  methods: {\n    ...mapActions('user', ['register']),\n\n    async handleRegister() {\n      try {\n        const valid = await this.$refs.registerForm.validate()\n        if (!valid) return\n\n        this.loading = true\n\n        const result = await this.register({\n          username: this.registerData.username,\n          email: this.registerData.email,\n          password: this.registerData.password,\n          nickname: this.registerData.nickname || this.registerData.username\n        })\n\n        if (result.success) {\n          this.$message.success('注册成功，请登录')\n          this.$router.push('/login')\n        }\n      } catch (error) {\n        this.$message.error(error.message || '注册失败')\n      } finally {\n        this.loading = false\n      }\n    }\n  }\n}\n</script>\n\n<style scoped>\n.register-page {\n  min-height: 100vh;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  padding: 20px;\n}\n\n.register-container {\n  background: white;\n  border-radius: 12px;\n  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);\n  overflow: hidden;\n  display: grid;\n  grid-template-columns: 1fr 1fr;\n  max-width: 1000px;\n  width: 100%;\n  min-height: 600px;\n}\n\n.register-form {\n  padding: 40px;\n  display: flex;\n  flex-direction: column;\n  justify-content: center;\n}\n\n.form-header {\n  text-align: center;\n  margin-bottom: 30px;\n}\n\n.form-header h2 {\n  font-size: 28px;\n  font-weight: bold;\n  color: #333;\n  margin-bottom: 8px;\n}\n\n.form-header p {\n  color: #666;\n  font-size: 14px;\n}\n\n.register-button {\n  width: 100%;\n  height: 48px;\n  font-size: 16px;\n  font-weight: 600;\n}\n\n.form-footer {\n  text-align: center;\n  margin-top: 20px;\n}\n\n.form-footer p {\n  color: #666;\n  font-size: 14px;\n}\n\n.link {\n  color: #409eff;\n  text-decoration: none;\n  font-weight: 600;\n}\n\n.link:hover {\n  text-decoration: underline;\n}\n\n.register-banner {\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n  padding: 40px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.banner-content {\n  text-align: center;\n}\n\n.banner-content h3 {\n  font-size: 28px;\n  font-weight: bold;\n  margin-bottom: 16px;\n}\n\n.banner-content p {\n  font-size: 16px;\n  margin-bottom: 40px;\n  opacity: 0.9;\n}\n\n.benefits {\n  display: flex;\n  flex-direction: column;\n  gap: 24px;\n}\n\n.benefit-item {\n  display: flex;\n  align-items: center;\n  gap: 16px;\n  text-align: left;\n}\n\n.benefit-icon {\n  font-size: 32px;\n  color: rgba(255, 255, 255, 0.9);\n}\n\n.benefit-item h4 {\n  font-size: 16px;\n  font-weight: 600;\n  margin-bottom: 4px;\n}\n\n.benefit-item p {\n  font-size: 14px;\n  opacity: 0.8;\n  margin: 0;\n}\n\n/* 响应式设计 */\n@media (max-width: 768px) {\n  .register-container {\n    grid-template-columns: 1fr;\n    max-width: 400px;\n  }\n\n  .register-banner {\n    display: none;\n  }\n\n  .register-form {\n    padding: 30px;\n  }\n\n  .form-header h2 {\n    font-size: 24px;\n  }\n}\n\n/* 表单样式调整 */\n:deep(.el-form-item) {\n  margin-bottom: 20px;\n}\n\n:deep(.el-input__wrapper) {\n  padding: 12px 16px;\n}\n\n:deep(.el-input__inner) {\n  font-size: 16px;\n}\n</style>\n", "import { render } from \"./Register.vue?vue&type=template&id=eb709022&scoped=true\"\nimport script from \"./Register.vue?vue&type=script&lang=js\"\nexport * from \"./Register.vue?vue&type=script&lang=js\"\n\nimport \"./Register.vue?vue&type=style&index=0&id=eb709022&scoped=true&lang=css\"\n\nimport exportComponent from \"../../node_modules/vue-loader/dist/exportHelper.js\"\nconst __exports__ = /*#__PURE__*/exportComponent(script, [['render',render],['__scopeId',\"data-v-eb709022\"]])\n\nexport default __exports__"], "names": ["class", "ref", "model", "registerData", "rules", "registerRules", "handleRegister", "prop", "username", "placeholder", "size", "email", "nickname", "password", "type", "confirmPassword", "loading", "to", "name", "components", "User", "Message", "Avatar", "Lock", "Star", "ChatDotRound", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "data", "validateConfirmPassword", "rule", "value", "callback", "this", "Error", "required", "message", "trigger", "min", "max", "pattern", "validator", "methods", "valid", "$refs", "registerForm", "validate", "result", "register", "success", "$message", "$router", "push", "error", "__exports__", "render"], "sourceRoot": ""}
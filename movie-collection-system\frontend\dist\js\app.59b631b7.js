(function(){"use strict";var e={120:function(e,t,a){var s=a(751),r=a(641);const o={id:"app"};function n(e,t,a,s,n,i){const c=(0,r.g2)("nav-bar"),l=(0,r.g2)("el-header"),u=(0,r.g2)("router-view"),d=(0,r.g2)("el-main"),g=(0,r.g2)("footer-bar"),m=(0,r.g2)("el-footer"),h=(0,r.g2)("el-container");return(0,r.uX)(),(0,r.CE)("div",o,[(0,r.bF)(h,null,{default:(0,r.k6)(()=>[(0,r.bF)(l,null,{default:(0,r.k6)(()=>[(0,r.bF)(c)]),_:1}),(0,r.bF)(d,null,{default:(0,r.k6)(()=>[(0,r.bF)(u)]),_:1}),(0,r.bF)(m,null,{default:(0,r.k6)(()=>[(0,r.bF)(g)]),_:1})]),_:1})])}var i=a(33);const c={class:"navbar"},l={class:"navbar-brand"},u={class:"navbar-menu"},d={class:"navbar-actions"},g={class:"search-box"},m={key:0,class:"user-actions"},h={key:1,class:"user-menu"},f={class:"user-info"},p={class:"username"};function E(e,t,a,o,n,E){const w=(0,r.g2)("VideoCamera"),v=(0,r.g2)("el-icon"),_=(0,r.g2)("router-link"),S=(0,r.g2)("el-menu-item"),I=(0,r.g2)("el-menu"),T=(0,r.g2)("Search"),k=(0,r.g2)("el-input"),y=(0,r.g2)("el-button"),b=(0,r.g2)("User"),A=(0,r.g2)("el-avatar"),L=(0,r.g2)("ArrowDown"),C=(0,r.g2)("el-dropdown-item"),O=(0,r.g2)("el-dropdown-menu"),N=(0,r.g2)("el-dropdown"),R=(0,r.g2)("el-header"),G=(0,r.g2)("el-container");return(0,r.uX)(),(0,r.CE)("div",c,[(0,r.bF)(G,null,{default:(0,r.k6)(()=>[(0,r.bF)(R,{class:"navbar-header"},{default:(0,r.k6)(()=>[(0,r.Lk)("div",l,[(0,r.bF)(_,{to:"/",class:"brand-link"},{default:(0,r.k6)(()=>[(0,r.bF)(v,{class:"brand-icon"},{default:(0,r.k6)(()=>[(0,r.bF)(w)]),_:1}),t[1]||(t[1]=(0,r.Lk)("span",{class:"brand-text"},"电影收藏",-1))]),_:1,__:[1]})]),(0,r.Lk)("div",u,[(0,r.bF)(I,{mode:"horizontal","default-active":n.activeIndex,class:"navbar-menu-items",onSelect:E.handleSelect},{default:(0,r.k6)(()=>[(0,r.bF)(S,{index:"/"},{default:(0,r.k6)(()=>t[2]||(t[2]=[(0,r.eW)("首页")])),_:1,__:[2]}),(0,r.bF)(S,{index:"/movies"},{default:(0,r.k6)(()=>t[3]||(t[3]=[(0,r.eW)("电影")])),_:1,__:[3]}),e.isLoggedIn?((0,r.uX)(),(0,r.Wv)(S,{key:0,index:"/collections"},{default:(0,r.k6)(()=>t[4]||(t[4]=[(0,r.eW)("我的收藏")])),_:1,__:[4]})):(0,r.Q3)("",!0),(0,r.bF)(S,{index:"/rankings"},{default:(0,r.k6)(()=>t[5]||(t[5]=[(0,r.eW)("排行榜")])),_:1,__:[5]})]),_:1},8,["default-active","onSelect"])]),(0,r.Lk)("div",d,[(0,r.Lk)("div",g,[(0,r.bF)(k,{modelValue:n.searchKeyword,"onUpdate:modelValue":t[0]||(t[0]=e=>n.searchKeyword=e),placeholder:"搜索电影...",class:"search-input",onKeyup:(0,s.jR)(E.handleSearch,["enter"])},{suffix:(0,r.k6)(()=>[(0,r.bF)(v,{class:"search-icon",onClick:E.handleSearch},{default:(0,r.k6)(()=>[(0,r.bF)(T)]),_:1},8,["onClick"])]),_:1},8,["modelValue","onKeyup"])]),e.isLoggedIn?((0,r.uX)(),(0,r.CE)("div",h,[(0,r.bF)(N,{onCommand:E.handleUserCommand},{dropdown:(0,r.k6)(()=>[(0,r.bF)(O,null,{default:(0,r.k6)(()=>[(0,r.bF)(C,{command:"profile"},{default:(0,r.k6)(()=>t[8]||(t[8]=[(0,r.eW)("个人资料")])),_:1,__:[8]}),(0,r.bF)(C,{command:"collections"},{default:(0,r.k6)(()=>t[9]||(t[9]=[(0,r.eW)("我的收藏")])),_:1,__:[9]}),(0,r.bF)(C,{command:"settings"},{default:(0,r.k6)(()=>t[10]||(t[10]=[(0,r.eW)("设置")])),_:1,__:[10]}),(0,r.bF)(C,{divided:"",command:"logout"},{default:(0,r.k6)(()=>t[11]||(t[11]=[(0,r.eW)("退出登录")])),_:1,__:[11]})]),_:1})]),default:(0,r.k6)(()=>[(0,r.Lk)("span",f,[(0,r.bF)(A,{src:E.userAvatar,size:32},{default:(0,r.k6)(()=>[(0,r.bF)(v,null,{default:(0,r.k6)(()=>[(0,r.bF)(b)]),_:1})]),_:1},8,["src"]),(0,r.Lk)("span",p,(0,i.v_)(E.userNickname),1),(0,r.bF)(v,{class:"dropdown-icon"},{default:(0,r.k6)(()=>[(0,r.bF)(L)]),_:1})])]),_:1},8,["onCommand"])])):((0,r.uX)(),(0,r.CE)("div",m,[(0,r.bF)(y,{type:"primary",onClick:E.showLogin},{default:(0,r.k6)(()=>t[6]||(t[6]=[(0,r.eW)("登录")])),_:1,__:[6]},8,["onClick"]),(0,r.bF)(y,{onClick:E.showRegister},{default:(0,r.k6)(()=>t[7]||(t[7]=[(0,r.eW)("注册")])),_:1,__:[7]},8,["onClick"])]))])]),_:1})]),_:1})])}var w=a(548),v=a(278),_={name:"NavBar",components:{VideoCamera:w.VideoCamera,Search:w.Search,User:w.User,ArrowDown:w.ArrowDown},data(){return{activeIndex:"/",searchKeyword:""}},computed:{...(0,v.aH)("user",["isLoggedIn","userInfo"]),userNickname(){return this.userInfo?.nickname||"用户"},userAvatar(){return this.userInfo?.avatar||""}},watch:{$route(e){this.activeIndex=e.path}},mounted(){this.activeIndex=this.$route.path},methods:{...(0,v.i0)("user",["logout"]),handleSelect(e){e!==this.$route.path&&this.$router.push(e)},handleSearch(){this.searchKeyword.trim()&&this.$router.push({path:"/movies",query:{search:this.searchKeyword.trim()}})},showLogin(){this.$router.push("/login")},showRegister(){this.$router.push("/register")},handleUserCommand(e){switch(e){case"profile":this.$router.push("/profile");break;case"collections":this.$router.push("/collections");break;case"settings":this.$router.push("/settings");break;case"logout":this.handleLogout();break}},async handleLogout(){try{await this.logout(),this.$message.success("退出登录成功"),this.$router.push("/")}catch(e){this.$message.error("退出登录失败")}}}},S=a(262);const I=(0,S.A)(_,[["render",E],["__scopeId","data-v-f6630256"]]);var T=I;const k={class:"footer"},y={class:"footer-content"},b={class:"footer-section"},A={class:"footer-section"};function L(e,t,a,o,n,i){const c=(0,r.g2)("router-link");return(0,r.uX)(),(0,r.CE)("div",k,[(0,r.Lk)("div",y,[t[8]||(t[8]=(0,r.Lk)("div",{class:"footer-section"},[(0,r.Lk)("h4",null,"电影收藏管理系统"),(0,r.Lk)("p",null,"发现好电影，记录观影时光")],-1)),(0,r.Lk)("div",b,[t[6]||(t[6]=(0,r.Lk)("h4",null,"功能",-1)),(0,r.Lk)("ul",null,[(0,r.Lk)("li",null,[(0,r.bF)(c,{to:"/movies"},{default:(0,r.k6)(()=>t[3]||(t[3]=[(0,r.eW)("电影浏览")])),_:1,__:[3]})]),(0,r.Lk)("li",null,[(0,r.bF)(c,{to:"/collections"},{default:(0,r.k6)(()=>t[4]||(t[4]=[(0,r.eW)("收藏管理")])),_:1,__:[4]})]),(0,r.Lk)("li",null,[(0,r.bF)(c,{to:"/rankings"},{default:(0,r.k6)(()=>t[5]||(t[5]=[(0,r.eW)("热门排行")])),_:1,__:[5]})])])]),(0,r.Lk)("div",A,[t[7]||(t[7]=(0,r.Lk)("h4",null,"关于",-1)),(0,r.Lk)("ul",null,[(0,r.Lk)("li",null,[(0,r.Lk)("a",{href:"#",onClick:t[0]||(t[0]=(0,s.D$)(()=>{},["prevent"]))},"使用帮助")]),(0,r.Lk)("li",null,[(0,r.Lk)("a",{href:"#",onClick:t[1]||(t[1]=(0,s.D$)(()=>{},["prevent"]))},"意见反馈")]),(0,r.Lk)("li",null,[(0,r.Lk)("a",{href:"#",onClick:t[2]||(t[2]=(0,s.D$)(()=>{},["prevent"]))},"联系我们")])])])]),t[9]||(t[9]=(0,r.Lk)("div",{class:"footer-bottom"},[(0,r.Lk)("p",null,"© 2024 电影收藏管理系统. All rights reserved.")],-1))])}var C={name:"FooterBar"};const O=(0,S.A)(C,[["render",L],["__scopeId","data-v-3aeea331"]]);var N=O,R={name:"App",components:{NavBar:T,FooterBar:N},async created(){await this.checkLoginStatus()},methods:{...(0,v.i0)("user",["checkLoginStatus"])}};const G=(0,S.A)(R,[["render",n]]);var P=G,F=a(220),D=a(335);const M=D.A.create({baseURL:"https://movie-collection-api.nantingyouyu.workers.dev/api",timeout:1e4,headers:{"Content-Type":"application/json"}});M.interceptors.request.use(e=>{const t=localStorage.getItem("token");return t&&(e.headers.Authorization=`Bearer ${t}`),e},e=>Promise.reject(e)),M.interceptors.response.use(e=>e,e=>{if(e.response)switch(e.response.status){case 401:localStorage.removeItem("token"),localStorage.removeItem("userInfo"),window.location.href="/login";break;case 403:console.error("没有权限访问该资源");break;case 404:console.error("请求的资源不存在");break;case 500:console.error("服务器内部错误");break;default:console.error("请求失败:",e.response.data?.message||e.message)}else e.request?console.error("网络错误，请检查网络连接"):console.error("请求配置错误:",e.message);return Promise.reject(e)});var $=M;const z={isLoggedIn:!1,userInfo:null,token:localStorage.getItem("token")||null},U={SET_LOGIN_STATUS(e,t){e.isLoggedIn=t},SET_USER_INFO(e,t){e.userInfo=t},SET_TOKEN(e,t){e.token=t,t?localStorage.setItem("token",t):localStorage.removeItem("token")},CLEAR_USER_DATA(e){e.isLoggedIn=!1,e.userInfo=null,e.token=null,localStorage.removeItem("token"),localStorage.removeItem("userInfo")}},x={async login({commit:e},{usernameOrEmail:t,password:a}){try{const s=await $.post("/users/login",{usernameOrEmail:t,password:a});if(s.data.success){const t=s.data.user;return e("SET_LOGIN_STATUS",!0),e("SET_USER_INFO",t),localStorage.setItem("userInfo",JSON.stringify(t)),{success:!0,user:t}}throw new Error(s.data.message)}catch(s){throw new Error(s.response?.data?.message||"登录失败")}},async register({commit:e},{username:t,email:a,password:s,nickname:r}){try{const e=await $.post("/users/register",{username:t,email:a,password:s,nickname:r});if(e.data.success)return{success:!0,message:e.data.message};throw new Error(e.data.message)}catch(o){throw new Error(o.response?.data?.message||"注册失败")}},logout({commit:e}){return e("CLEAR_USER_DATA"),Promise.resolve()},checkLoginStatus({commit:e}){const t=localStorage.getItem("userInfo");if(t)try{const a=JSON.parse(t);e("SET_LOGIN_STATUS",!0),e("SET_USER_INFO",a)}catch(a){e("CLEAR_USER_DATA")}},async updateUserInfo({commit:e,state:t},a){try{const s=await $.put(`/users/${t.userInfo.id}`,a);if(s.data.success){const t=s.data.user;return e("SET_USER_INFO",t),localStorage.setItem("userInfo",JSON.stringify(t)),{success:!0,user:t}}throw new Error(s.data.message)}catch(s){throw new Error(s.response?.data?.message||"更新失败")}}},V={isLoggedIn:e=>e.isLoggedIn,userInfo:e=>e.userInfo,userId:e=>e.userInfo?.id||null,username:e=>e.userInfo?.username||"",userNickname:e=>e.userInfo?.nickname||""};var B={namespaced:!0,state:z,mutations:U,actions:x,getters:V};function j(e){return e?{id:e.id,title:e.title,originalTitle:e.original_title,overview:e.overview,posterPath:e.poster_path,backdropPath:e.backdrop_path,releaseDate:e.release_date,runtime:e.runtime,genres:e.genres,director:e.director,cast:e.cast,country:e.country,language:e.language,imdbId:e.imdb_id,tmdbId:e.tmdb_id,averageRating:e.average_rating,ratingCount:e.rating_count,collectionCount:e.collection_count,createdAt:e.created_at,updatedAt:e.updated_at}:null}function W(e){return Array.isArray(e)?e.map(j):[]}const K={movies:[],currentMovie:null,popularMovies:[],topRatedMovies:[],latestMovies:[],loading:!1,pagination:{currentPage:0,totalPages:0,totalElements:0,size:20}},q={SET_LOADING(e,t){e.loading=t},SET_MOVIES(e,t){e.movies=t},SET_CURRENT_MOVIE(e,t){e.currentMovie=t},SET_POPULAR_MOVIES(e,t){e.popularMovies=t},SET_TOP_RATED_MOVIES(e,t){e.topRatedMovies=t},SET_LATEST_MOVIES(e,t){e.latestMovies=t},SET_PAGINATION(e,t){e.pagination=t}},X={async fetchMovies({commit:e},{page:t=0,size:a=20,sortBy:s="averageRating",sortDir:r="desc",search:o=""}={}){e("SET_LOADING",!0);try{const n={page:t,size:a,sortBy:s,sortDir:r};o&&(n.search=o);const i=await $.get("/movies",{params:n});if(i.data.success){const t=W(i.data.movies);return e("SET_MOVIES",t),e("SET_PAGINATION",{currentPage:i.data.currentPage,totalPages:i.data.totalPages,totalElements:i.data.totalElements,size:i.data.size}),{success:!0,data:i.data}}throw new Error(i.data.message)}catch(n){throw new Error(n.response?.data?.message||"获取电影列表失败")}finally{e("SET_LOADING",!1)}},async fetchMovieById({commit:e},t){e("SET_LOADING",!0);try{const a=await $.get(`/movies/${t}`);if(a.data.success){const t=j(a.data.movie);return e("SET_CURRENT_MOVIE",t),{success:!0,movie:t}}throw new Error(a.data.message)}catch(a){throw new Error(a.response?.data?.message||"获取电影详情失败")}finally{e("SET_LOADING",!1)}},async fetchPopularMovies({commit:e},{page:t=0,size:a=10}={}){try{const s=await $.get("/movies/popular",{params:{page:t,size:a}});if(s.data.success){const t=W(s.data.movies);return e("SET_POPULAR_MOVIES",t),{success:!0,data:s.data}}throw new Error(s.data.message)}catch(s){throw new Error(s.response?.data?.message||"获取热门电影失败")}},async fetchTopRatedMovies({commit:e},{page:t=0,size:a=10,minRating:s=8,minRatingCount:r=1e3}={}){try{const o=await $.get("/movies/top-rated",{params:{page:t,size:a,minRating:s,minRatingCount:r}});if(o.data.success)return e("SET_TOP_RATED_MOVIES",o.data.movies),{success:!0,data:o.data};throw new Error(o.data.message)}catch(o){throw new Error(o.response?.data?.message||"获取高评分电影失败")}},async fetchLatestMovies({commit:e},{page:t=0,size:a=10}={}){try{const s=await $.get("/movies/latest",{params:{page:t,size:a}});if(s.data.success)return e("SET_LATEST_MOVIES",s.data.movies),{success:!0,data:s.data};throw new Error(s.data.message)}catch(s){throw new Error(s.response?.data?.message||"获取最新电影失败")}},async searchMovies({commit:e},{keyword:t,page:a=0,size:s=20}){e("SET_LOADING",!0);try{const r=await $.get("/movies/search",{params:{keyword:t,page:a,size:s}});if(r.data.success)return e("SET_MOVIES",r.data.movies),e("SET_PAGINATION",{currentPage:r.data.currentPage,totalPages:r.data.totalPages,totalElements:r.data.totalElements,size:r.data.size}),{success:!0,data:r.data};throw new Error(r.data.message)}catch(r){throw new Error(r.response?.data?.message||"搜索电影失败")}finally{e("SET_LOADING",!1)}}},H={movies:e=>e.movies,currentMovie:e=>e.currentMovie,popularMovies:e=>e.popularMovies,topRatedMovies:e=>e.topRatedMovies,latestMovies:e=>e.latestMovies,loading:e=>e.loading,pagination:e=>e.pagination};var J={namespaced:!0,state:K,mutations:q,actions:X,getters:H};const Q={collections:[],loading:!1,pagination:{currentPage:0,totalPages:0,totalElements:0,size:20},collectionStats:{totalCollections:0}},Y={SET_LOADING(e,t){e.loading=t},SET_COLLECTIONS(e,t){e.collections=t},SET_PAGINATION(e,t){e.pagination=t},SET_COLLECTION_STATS(e,t){e.collectionStats=t},ADD_COLLECTION(e,t){e.collections.unshift(t),e.collectionStats.totalCollections++},REMOVE_COLLECTION(e,t){const a=e.collections.findIndex(e=>e.id===t);-1!==a&&(e.collections.splice(a,1),e.collectionStats.totalCollections--)}},Z={async addToCollection({commit:e,rootGetters:t},a){try{const e=t["user/userId"];if(!e)throw new Error("请先登录");const s=await $.post("/collections",{userId:e,movieId:a});if(s.data.success)return{success:!0,message:s.data.message};throw new Error(s.data.message)}catch(s){throw new Error(s.response?.data?.message||"收藏失败")}},async removeFromCollection({commit:e,rootGetters:t},a){try{const s=t["user/userId"];if(!s)throw new Error("请先登录");const r=await $.delete(`/collections/${s}/${a}`);if(r.data.success)return e("REMOVE_COLLECTION",a),{success:!0,message:r.data.message};throw new Error(r.data.message)}catch(s){throw new Error(s.response?.data?.message||"取消收藏失败")}},async checkCollection({rootGetters:e},t){try{const a=e["user/userId"];if(!a)return{success:!0,isCollected:!1};const s=await $.get(`/collections/check/${a}/${t}`);if(s.data.success)return{success:!0,isCollected:s.data.isCollected};throw new Error(s.data.message)}catch(a){throw new Error(a.response?.data?.message||"检查收藏状态失败")}},async fetchUserCollections({commit:e,rootGetters:t},{page:a=0,size:s=20,sortBy:r="createdAt",sortDir:o="desc"}={}){e("SET_LOADING",!0);try{const n=t["user/userId"];if(!n)throw new Error("请先登录");const i=await $.get(`/collections/user/${n}`,{params:{page:a,size:s,sortBy:r,sortDir:o}});if(i.data.success)return e("SET_COLLECTIONS",i.data.movies),e("SET_PAGINATION",{currentPage:i.data.currentPage,totalPages:i.data.totalPages,totalElements:i.data.totalElements,size:i.data.size}),{success:!0,data:i.data};throw new Error(i.data.message)}catch(n){throw new Error(n.response?.data?.message||"获取收藏列表失败")}finally{e("SET_LOADING",!1)}},async fetchCollectionStats({commit:e,rootGetters:t}){try{const a=t["user/userId"];if(!a)return{success:!0,stats:{totalCollections:0}};const s=await $.get(`/collections/stats/${a}`);if(s.data.success)return e("SET_COLLECTION_STATS",{totalCollections:s.data.totalCollections}),{success:!0,stats:s.data};throw new Error(s.data.message)}catch(a){throw new Error(a.response?.data?.message||"获取收藏统计失败")}},async fetchPopularCollections({commit:e},{page:t=0,size:a=10}={}){try{const e=await $.get("/collections/popular",{params:{page:t,size:a}});if(e.data.success)return{success:!0,data:e.data};throw new Error(e.data.message)}catch(s){throw new Error(s.response?.data?.message||"获取热门收藏失败")}},async searchCollections({commit:e,rootGetters:t},{keyword:a,page:s=0,size:r=20}){e("SET_LOADING",!0);try{const o=t["user/userId"];if(!o)throw new Error("请先登录");const n=await $.get(`/collections/user/${o}`,{params:{page:s,size:r}});if(n.data.success){const t=n.data.movies.filter(e=>e.title.toLowerCase().includes(a.toLowerCase()));return e("SET_COLLECTIONS",t),{success:!0,data:{...n.data,movies:t}}}throw new Error(n.data.message)}catch(o){throw new Error(o.response?.data?.message||"搜索收藏失败")}finally{e("SET_LOADING",!1)}}},ee={collections:e=>e.collections,loading:e=>e.loading,pagination:e=>e.pagination,collectionStats:e=>e.collectionStats,totalCollections:e=>e.collectionStats.totalCollections,isMovieCollected:e=>t=>e.collections.some(e=>e.id===t)};var te={namespaced:!0,state:Q,mutations:Y,actions:Z,getters:ee};const ae={ratings:[],currentRating:null,loading:!1,pagination:{currentPage:0,totalPages:0,totalElements:0,size:20},ratingStats:{totalRatings:0,averageScore:0}},se={SET_LOADING(e,t){e.loading=t},SET_RATINGS(e,t){e.ratings=t},SET_CURRENT_RATING(e,t){e.currentRating=t},SET_PAGINATION(e,t){e.pagination=t},SET_RATING_STATS(e,t){e.ratingStats=t},ADD_RATING(e,t){e.ratings.unshift(t),e.ratingStats.totalRatings++},UPDATE_RATING(e,t){const a=e.ratings.findIndex(e=>e.id===t.id);-1!==a&&e.ratings.splice(a,1,t)},REMOVE_RATING(e,t){const a=e.ratings.findIndex(e=>e.id===t);-1!==a&&(e.ratings.splice(a,1),e.ratingStats.totalRatings--)}},re={async addOrUpdateRating({commit:e,rootGetters:t},{movieId:a,score:s,comment:r=""}){try{const o=t["user/userId"];if(!o)throw new Error("请先登录");const n=await $.post("/ratings",{userId:o,movieId:a,score:s,comment:r});if(n.data.success)return e("SET_CURRENT_RATING",n.data.rating),{success:!0,rating:n.data.rating,message:n.data.message};throw new Error(n.data.message)}catch(o){throw new Error(o.response?.data?.message||"评分失败")}},async deleteRating({commit:e,rootGetters:t},a){try{const s=t["user/userId"];if(!s)throw new Error("请先登录");const r=await $.delete(`/ratings/${s}/${a}`);if(r.data.success)return e("SET_CURRENT_RATING",null),{success:!0,message:r.data.message};throw new Error(r.data.message)}catch(s){throw new Error(s.response?.data?.message||"删除评分失败")}},async fetchUserMovieRating({commit:e,rootGetters:t},a){try{const s=t["user/userId"];if(!s)return e("SET_CURRENT_RATING",null),{success:!0,rating:null};const r=await $.get(`/ratings/${s}/${a}`);if(r.data.success)return e("SET_CURRENT_RATING",r.data.rating),{success:!0,rating:r.data.rating};throw new Error(r.data.message)}catch(s){throw new Error(s.response?.data?.message||"获取评分失败")}},async fetchUserRatings({commit:e,rootGetters:t},{page:a=0,size:s=20,sortBy:r="updatedAt",sortDir:o="desc"}={}){e("SET_LOADING",!0);try{const n=t["user/userId"];if(!n)throw new Error("请先登录");const i=await $.get(`/ratings/user/${n}`,{params:{page:a,size:s,sortBy:r,sortDir:o}});if(i.data.success)return e("SET_RATINGS",i.data.ratings),e("SET_PAGINATION",{currentPage:i.data.currentPage,totalPages:i.data.totalPages,totalElements:i.data.totalElements,size:i.data.size}),{success:!0,data:i.data};throw new Error(i.data.message)}catch(n){throw new Error(n.response?.data?.message||"获取评分列表失败")}finally{e("SET_LOADING",!1)}},async fetchMovieRatings({commit:e},{movieId:t,page:a=0,size:s=20,sortBy:r="updatedAt",sortDir:o="desc"}={}){e("SET_LOADING",!0);try{const e=await $.get(`/ratings/movie/${t}`,{params:{page:a,size:s,sortBy:r,sortDir:o}});if(e.data.success)return{success:!0,data:e.data};throw new Error(e.data.message)}catch(n){throw new Error(n.response?.data?.message||"获取电影评分失败")}finally{e("SET_LOADING",!1)}},async fetchRatingStats({commit:e,rootGetters:t}){try{const a=t["user/userId"];if(!a)return{success:!0,stats:{totalRatings:0,averageScore:0}};const s=await $.get(`/ratings/stats/${a}`);if(s.data.success)return e("SET_RATING_STATS",{totalRatings:s.data.totalRatings,averageScore:s.data.averageScore}),{success:!0,stats:s.data};throw new Error(s.data.message)}catch(a){throw new Error(a.response?.data?.message||"获取评分统计失败")}},async fetchMovieRatingStats({commit:e},t){try{const e=await $.get(`/ratings/movie/${t}/stats`);if(e.data.success)return{success:!0,stats:e.data};throw new Error(e.data.message)}catch(a){throw new Error(a.response?.data?.message||"获取电影评分统计失败")}},async searchRatings({commit:e,rootGetters:t},{keyword:a,page:s=0,size:r=20}){e("SET_LOADING",!0);try{const o=t["user/userId"];if(!o)throw new Error("请先登录");const n=await $.get(`/ratings/user/${o}`,{params:{page:s,size:r}});if(n.data.success){const t=n.data.ratings.filter(e=>e.movie.title.toLowerCase().includes(a.toLowerCase())||e.comment&&e.comment.toLowerCase().includes(a.toLowerCase()));return e("SET_RATINGS",t),{success:!0,data:{...n.data,ratings:t}}}throw new Error(n.data.message)}catch(o){throw new Error(o.response?.data?.message||"搜索评分失败")}finally{e("SET_LOADING",!1)}}},oe={ratings:e=>e.ratings,currentRating:e=>e.currentRating,loading:e=>e.loading,pagination:e=>e.pagination,ratingStats:e=>e.ratingStats,totalRatings:e=>e.ratingStats.totalRatings,averageScore:e=>e.ratingStats.averageScore,hasRated:e=>t=>e.ratings.some(e=>e.movie.id===t),getMovieRating:e=>t=>e.ratings.find(e=>e.movie.id===t)};var ne={namespaced:!0,state:ae,mutations:se,actions:re,getters:oe},ie=(0,v.y$)({modules:{user:B,movie:J,collection:te,rating:ne}});const ce=()=>a.e(395).then(a.bind(a,395)),le=()=>a.e(277).then(a.bind(a,277)),ue=()=>a.e(129).then(a.bind(a,129)),de=()=>a.e(272).then(a.bind(a,272)),ge=()=>a.e(481).then(a.bind(a,481)),me=()=>a.e(274).then(a.bind(a,274)),he=()=>a.e(976).then(a.bind(a,976)),fe=()=>a.e(184).then(a.bind(a,184)),pe=()=>a.e(160).then(a.bind(a,160)),Ee=[{path:"/",name:"Home",component:ce,meta:{title:"首页"}},{path:"/movies",name:"Movies",component:le,meta:{title:"电影"}},{path:"/movies/:id",name:"MovieDetail",component:ue,meta:{title:"电影详情"}},{path:"/collections",name:"Collections",component:de,meta:{title:"我的收藏",requiresAuth:!0}},{path:"/rankings",name:"Rankings",component:ge,meta:{title:"排行榜"}},{path:"/login",name:"Login",component:me,meta:{title:"登录",hideForAuth:!0}},{path:"/register",name:"Register",component:he,meta:{title:"注册",hideForAuth:!0}},{path:"/profile",name:"Profile",component:fe,meta:{title:"个人资料",requiresAuth:!0}},{path:"/:pathMatch(.*)*",name:"NotFound",component:pe,meta:{title:"页面未找到"}}],we=(0,F.aE)({history:(0,F.LA)(),routes:Ee,scrollBehavior(e,t,a){return a||{top:0}}});we.beforeEach((e,t,a)=>{ie.dispatch("user/checkLoginStatus");const s=ie.getters["user/isLoggedIn"];document.title=e.meta.title?`${e.meta.title} - 电影收藏管理系统`:"电影收藏管理系统",!e.meta.requiresAuth||s?e.meta.hideForAuth&&s?a("/"):a():a("/login")});var ve=we,_e=a(390);a(188);const Se=(0,s.Ef)(P);for(const[Ie,Te]of Object.entries(w))Se.component(Ie,Te);Se.use(ie),Se.use(ve),Se.use(_e.A),Se.mount("#app")}},t={};function a(s){var r=t[s];if(void 0!==r)return r.exports;var o=t[s]={exports:{}};return e[s].call(o.exports,o,o.exports,a),o.exports}a.m=e,function(){var e=[];a.O=function(t,s,r,o){if(!s){var n=1/0;for(u=0;u<e.length;u++){s=e[u][0],r=e[u][1],o=e[u][2];for(var i=!0,c=0;c<s.length;c++)(!1&o||n>=o)&&Object.keys(a.O).every(function(e){return a.O[e](s[c])})?s.splice(c--,1):(i=!1,o<n&&(n=o));if(i){e.splice(u--,1);var l=r();void 0!==l&&(t=l)}}return t}o=o||0;for(var u=e.length;u>0&&e[u-1][2]>o;u--)e[u]=e[u-1];e[u]=[s,r,o]}}(),function(){a.d=function(e,t){for(var s in t)a.o(t,s)&&!a.o(e,s)&&Object.defineProperty(e,s,{enumerable:!0,get:t[s]})}}(),function(){a.f={},a.e=function(e){return Promise.all(Object.keys(a.f).reduce(function(t,s){return a.f[s](e,t),t},[]))}}(),function(){a.u=function(e){return"js/"+e+"."+{129:"f6aed3a5",160:"bbf82b8d",184:"34c8fc60",272:"c2e045c8",274:"865368db",277:"81493380",395:"48ee123a",481:"4ebbc053",976:"29294908"}[e]+".js"}}(),function(){a.miniCssF=function(e){return"css/"+e+"."+{129:"a675e3ed",160:"61ec7384",184:"32e26379",272:"e6a10b0d",274:"a873df9b",277:"74d9fb47",395:"f9727c1c",481:"c42e13b7",976:"9f3e0df0"}[e]+".css"}}(),function(){a.g=function(){if("object"===typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"===typeof window)return window}}()}(),function(){a.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)}}(),function(){var e={},t="movie-collection-frontend:";a.l=function(s,r,o,n){if(e[s])e[s].push(r);else{var i,c;if(void 0!==o)for(var l=document.getElementsByTagName("script"),u=0;u<l.length;u++){var d=l[u];if(d.getAttribute("src")==s||d.getAttribute("data-webpack")==t+o){i=d;break}}i||(c=!0,i=document.createElement("script"),i.charset="utf-8",i.timeout=120,a.nc&&i.setAttribute("nonce",a.nc),i.setAttribute("data-webpack",t+o),i.src=s),e[s]=[r];var g=function(t,a){i.onerror=i.onload=null,clearTimeout(m);var r=e[s];if(delete e[s],i.parentNode&&i.parentNode.removeChild(i),r&&r.forEach(function(e){return e(a)}),t)return t(a)},m=setTimeout(g.bind(null,void 0,{type:"timeout",target:i}),12e4);i.onerror=g.bind(null,i.onerror),i.onload=g.bind(null,i.onload),c&&document.head.appendChild(i)}}}(),function(){a.r=function(e){"undefined"!==typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})}}(),function(){a.p=""}(),function(){if("undefined"!==typeof document){var e=function(e,t,s,r,o){var n=document.createElement("link");n.rel="stylesheet",n.type="text/css",a.nc&&(n.nonce=a.nc);var i=function(a){if(n.onerror=n.onload=null,"load"===a.type)r();else{var s=a&&a.type,i=a&&a.target&&a.target.href||t,c=new Error("Loading CSS chunk "+e+" failed.\n("+s+": "+i+")");c.name="ChunkLoadError",c.code="CSS_CHUNK_LOAD_FAILED",c.type=s,c.request=i,n.parentNode&&n.parentNode.removeChild(n),o(c)}};return n.onerror=n.onload=i,n.href=t,s?s.parentNode.insertBefore(n,s.nextSibling):document.head.appendChild(n),n},t=function(e,t){for(var a=document.getElementsByTagName("link"),s=0;s<a.length;s++){var r=a[s],o=r.getAttribute("data-href")||r.getAttribute("href");if("stylesheet"===r.rel&&(o===e||o===t))return r}var n=document.getElementsByTagName("style");for(s=0;s<n.length;s++){r=n[s],o=r.getAttribute("data-href");if(o===e||o===t)return r}},s=function(s){return new Promise(function(r,o){var n=a.miniCssF(s),i=a.p+n;if(t(n,i))return r();e(s,i,null,r,o)})},r={524:0};a.f.miniCss=function(e,t){var a={129:1,160:1,184:1,272:1,274:1,277:1,395:1,481:1,976:1};r[e]?t.push(r[e]):0!==r[e]&&a[e]&&t.push(r[e]=s(e).then(function(){r[e]=0},function(t){throw delete r[e],t}))}}}(),function(){var e={524:0};a.f.j=function(t,s){var r=a.o(e,t)?e[t]:void 0;if(0!==r)if(r)s.push(r[2]);else{var o=new Promise(function(a,s){r=e[t]=[a,s]});s.push(r[2]=o);var n=a.p+a.u(t),i=new Error,c=function(s){if(a.o(e,t)&&(r=e[t],0!==r&&(e[t]=void 0),r)){var o=s&&("load"===s.type?"missing":s.type),n=s&&s.target&&s.target.src;i.message="Loading chunk "+t+" failed.\n("+o+": "+n+")",i.name="ChunkLoadError",i.type=o,i.request=n,r[1](i)}};a.l(n,c,"chunk-"+t,t)}},a.O.j=function(t){return 0===e[t]};var t=function(t,s){var r,o,n=s[0],i=s[1],c=s[2],l=0;if(n.some(function(t){return 0!==e[t]})){for(r in i)a.o(i,r)&&(a.m[r]=i[r]);if(c)var u=c(a)}for(t&&t(s);l<n.length;l++)o=n[l],a.o(e,o)&&e[o]&&e[o][0](),e[o]=0;return a.O(u)},s=self["webpackChunkmovie_collection_frontend"]=self["webpackChunkmovie_collection_frontend"]||[];s.forEach(t.bind(null,0)),s.push=t.bind(null,s.push.bind(s))}();var s=a.O(void 0,[504],function(){return a(120)});s=a.O(s)})();
//# sourceMappingURL=app.59b631b7.js.map
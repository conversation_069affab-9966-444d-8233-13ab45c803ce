<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>电影详情测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .result {
            margin-top: 10px;
            padding: 10px;
            border-radius: 4px;
            white-space: pre-wrap;
            font-family: monospace;
            max-height: 400px;
            overflow-y: auto;
        }
        .success { background-color: #d4edda; color: #155724; }
        .error { background-color: #f8d7da; color: #721c24; }
        .loading { background-color: #fff3cd; color: #856404; }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background-color: #0056b3; }
        .movie-card {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
            background: #f9f9f9;
        }
        .movie-title {
            font-size: 18px;
            font-weight: bold;
            color: #333;
            margin-bottom: 10px;
        }
        .movie-info {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
            font-size: 14px;
        }
        .movie-info span {
            padding: 2px 0;
        }
        .movie-info .label {
            font-weight: bold;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎬 电影详情API测试</h1>
        
        <div class="test-section">
            <h3>1. 测试电影详情API</h3>
            <button onclick="testMovieDetail(1)">测试电影ID: 1</button>
            <button onclick="testMovieDetail(2)">测试电影ID: 2</button>
            <button onclick="testMovieDetail(3)">测试电影ID: 3</button>
            <button onclick="testMovieDetail(999)">测试不存在的电影</button>
            <div id="detail-result" class="result"></div>
        </div>

        <div class="test-section">
            <h3>2. 所有电影列表</h3>
            <button onclick="testAllMovies()">获取所有电影</button>
            <div id="movies-result" class="result"></div>
        </div>

        <div class="test-section">
            <h3>3. 字段映射测试</h3>
            <button onclick="testFieldMapping()">测试字段映射</button>
            <div id="mapping-result" class="result"></div>
        </div>

        <div class="test-section">
            <h3>4. 前端路由测试</h3>
            <p>点击下面的链接测试前端路由：</p>
            <a href="/movies/1" target="_blank">电影详情页 - 肖申克的救赎</a><br>
            <a href="/movies/2" target="_blank">电影详情页 - 霸王别姬</a><br>
            <a href="/movies/3" target="_blank">电影详情页 - 阿甘正传</a><br>
        </div>
    </div>

    <script>
        const API_BASE = 'https://movie-collection-api.nantingyouyu.workers.dev/api';
        
        function showResult(elementId, type, message) {
            const element = document.getElementById(elementId);
            element.className = `result ${type}`;
            element.textContent = message;
        }

        async function testMovieDetail(movieId) {
            showResult('detail-result', 'loading', `正在获取电影 ${movieId} 的详情...`);
            try {
                const response = await fetch(`${API_BASE}/movies/${movieId}`);
                const data = await response.json();
                
                if (data.success) {
                    const movie = data.movie;
                    const movieInfo = `✅ 电影详情获取成功
电影ID: ${movie.id}
标题: ${movie.title}
原标题: ${movie.original_title}
导演: ${movie.director}
主演: ${movie.cast}
评分: ${movie.average_rating}/10
评分人数: ${movie.rating_count}
收藏人数: ${movie.collection_count}
上映日期: ${movie.release_date}
片长: ${movie.runtime}分钟
国家: ${movie.country}
语言: ${movie.language}
类型: ${movie.genres}
海报: ${movie.poster_path}
背景: ${movie.backdrop_path}
简介: ${movie.overview}`;
                    
                    showResult('detail-result', 'success', movieInfo);
                } else {
                    showResult('detail-result', 'error', `❌ API返回错误: ${data.message}`);
                }
            } catch (error) {
                showResult('detail-result', 'error', `❌ 请求失败: ${error.message}`);
            }
        }

        async function testAllMovies() {
            showResult('movies-result', 'loading', '正在获取所有电影...');
            try {
                const response = await fetch(`${API_BASE}/movies`);
                const data = await response.json();
                
                if (data.success) {
                    const moviesList = data.movies.map(movie => 
                        `${movie.id}. ${movie.title} (${movie.average_rating}分)`
                    ).join('\n');
                    
                    showResult('movies-result', 'success', 
                        `✅ 电影列表获取成功\n共 ${data.movies.length} 部电影:\n${moviesList}`);
                } else {
                    showResult('movies-result', 'error', `❌ API返回错误: ${data.message}`);
                }
            } catch (error) {
                showResult('movies-result', 'error', `❌ 请求失败: ${error.message}`);
            }
        }

        async function testFieldMapping() {
            showResult('mapping-result', 'loading', '正在测试字段映射...');
            try {
                const response = await fetch(`${API_BASE}/movies/1`);
                const data = await response.json();
                
                if (data.success) {
                    const movie = data.movie;
                    const fieldTest = `✅ 字段映射测试
API返回的字段格式:
- poster_path: ${movie.poster_path}
- backdrop_path: ${movie.backdrop_path}
- original_title: ${movie.original_title}
- average_rating: ${movie.average_rating}
- rating_count: ${movie.rating_count}
- collection_count: ${movie.collection_count}
- release_date: ${movie.release_date}
- created_at: ${movie.created_at}
- updated_at: ${movie.updated_at}

前端需要转换为驼峰命名:
- posterPath
- backdropPath
- originalTitle
- averageRating
- ratingCount
- collectionCount
- releaseDate
- createdAt
- updatedAt`;
                    
                    showResult('mapping-result', 'success', fieldTest);
                } else {
                    showResult('mapping-result', 'error', `❌ API返回错误: ${data.message}`);
                }
            } catch (error) {
                showResult('mapping-result', 'error', `❌ 请求失败: ${error.message}`);
            }
        }

        // 页面加载时自动运行测试
        window.onload = function() {
            setTimeout(() => testMovieDetail(1), 500);
            setTimeout(() => testAllMovies(), 1000);
        };
    </script>
</body>
</html>

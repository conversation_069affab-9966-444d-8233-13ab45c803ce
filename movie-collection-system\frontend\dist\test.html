<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>电影收藏管理系统 - 测试页面</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .container {
            background: white;
            border-radius: 12px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            padding: 40px;
            max-width: 600px;
            width: 90%;
            text-align: center;
        }
        
        .logo {
            font-size: 48px;
            margin-bottom: 20px;
        }
        
        h1 {
            color: #333;
            margin-bottom: 20px;
            font-size: 28px;
        }
        
        .status {
            margin: 20px 0;
            padding: 15px;
            border-radius: 8px;
            font-weight: 600;
        }
        
        .status.success {
            background-color: #f0f9ff;
            color: #0369a1;
            border: 1px solid #bae6fd;
        }
        
        .status.error {
            background-color: #fef2f2;
            color: #dc2626;
            border: 1px solid #fecaca;
        }
        
        .status.warning {
            background-color: #fffbeb;
            color: #d97706;
            border: 1px solid #fed7aa;
        }
        
        .feature-list {
            text-align: left;
            margin: 30px 0;
        }
        
        .feature-list h3 {
            color: #333;
            margin-bottom: 15px;
        }
        
        .feature-list ul {
            list-style: none;
        }
        
        .feature-list li {
            padding: 8px 0;
            color: #666;
        }
        
        .feature-list li:before {
            content: "✓ ";
            color: #10b981;
            font-weight: bold;
            margin-right: 8px;
        }
        
        .buttons {
            margin-top: 30px;
        }
        
        .btn {
            display: inline-block;
            padding: 12px 24px;
            margin: 0 10px;
            background-color: #667eea;
            color: white;
            text-decoration: none;
            border-radius: 6px;
            font-weight: 600;
            transition: background-color 0.3s;
        }
        
        .btn:hover {
            background-color: #5a67d8;
        }
        
        .btn.secondary {
            background-color: #6b7280;
        }
        
        .btn.secondary:hover {
            background-color: #4b5563;
        }
        
        .info {
            margin-top: 30px;
            padding: 20px;
            background-color: #f8fafc;
            border-radius: 8px;
            text-align: left;
        }
        
        .info h4 {
            color: #333;
            margin-bottom: 10px;
        }
        
        .info p {
            color: #666;
            line-height: 1.6;
            margin-bottom: 10px;
        }
        
        .code {
            background-color: #1f2937;
            color: #f9fafb;
            padding: 15px;
            border-radius: 6px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            margin: 10px 0;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="logo">🎬</div>
        <h1>电影收藏管理系统</h1>
        
        <div class="status warning">
            <strong>系统状态：</strong> 前端测试页面运行正常
        </div>
        
        <div class="feature-list">
            <h3>系统功能特色：</h3>
            <ul>
                <li>用户注册和登录管理</li>
                <li>海量电影资源浏览</li>
                <li>个人电影收藏管理</li>
                <li>电影评分和评论系统</li>
                <li>热门电影排行榜</li>
                <li>智能电影推荐算法</li>
                <li>数据统计和可视化</li>
            </ul>
        </div>
        
        <div class="info">
            <h4>🔧 系统配置信息：</h4>
            <p><strong>前端端口：</strong> 8080</p>
            <p><strong>后端端口：</strong> 8081</p>
            <p><strong>数据库：</strong> MySQL 9.2 (nantingyouyu)</p>
            <p><strong>技术栈：</strong> Vue.js 3 + Spring Boot + MySQL</p>
        </div>
        
        <div class="info">
            <h4>🚀 启动步骤：</h4>
            <p>1. 初始化数据库（运行 init_database.sql）</p>
            <p>2. 启动后端服务（端口 8081）</p>
            <p>3. 启动前端服务（端口 8080）</p>
            
            <div class="code">
# 数据库初始化
mysql -u root -p nantingyouyu < database/init_database.sql

# 启动后端
cd backend && mvn spring-boot:run

# 启动前端
cd frontend && npm install && npm run serve
            </div>
        </div>
        
        <div class="buttons">
            <a href="#" class="btn" onclick="testBackend()">测试后端连接</a>
            <a href="#" class="btn secondary" onclick="showInstructions()">查看说明</a>
        </div>
        
        <div id="result" style="margin-top: 20px;"></div>
    </div>

    <script>
        function testBackend() {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = '<div class="status warning">正在测试后端连接...</div>';
            
            fetch('http://localhost:8081/api/test/health')
                .then(response => response.json())
                .then(data => {
                    resultDiv.innerHTML = '<div class="status success">✅ 后端连接成功！系统运行正常</div>';
                })
                .catch(error => {
                    resultDiv.innerHTML = `
                        <div class="status error">
                            ❌ 后端连接失败<br>
                            请确保后端服务已启动（端口8081）<br>
                            <small>错误信息: ${error.message}</small>
                        </div>
                    `;
                });
        }
        
        function showInstructions() {
            alert(`
电影收藏管理系统启动说明：

1. 数据库配置：
   - 确保MySQL 9.2服务运行
   - 使用 nantingyouyu 数据库
   - 运行 database/init_database.sql 初始化表结构

2. 后端启动：
   - cd backend
   - mvn spring-boot:run
   - 访问 http://localhost:8081/api/test/health 测试

3. 前端启动：
   - cd frontend
   - npm install
   - npm run serve
   - 访问 http://localhost:8080

4. 测试账户：
   - 用户名: admin, 密码: 123456
   - 用户名: moviefan, 密码: 123456
            `);
        }
        
        // 页面加载时自动测试后端
        window.onload = function() {
            setTimeout(testBackend, 1000);
        };
    </script>
</body>
</html>

# @eslint-community/eslint-utils

[![npm version](https://img.shields.io/npm/v/@eslint-community/eslint-utils.svg)](https://www.npmjs.com/package/@eslint-community/eslint-utils)
[![Downloads/month](https://img.shields.io/npm/dm/@eslint-community/eslint-utils.svg)](http://www.npmtrends.com/@eslint-community/eslint-utils)
[![Build Status](https://github.com/eslint-community/eslint-utils/workflows/CI/badge.svg)](https://github.com/eslint-community/eslint-utils/actions)
[![Coverage Status](https://codecov.io/gh/eslint-community/eslint-utils/branch/main/graph/badge.svg)](https://codecov.io/gh/eslint-community/eslint-utils)

## 🏁 Goal

This package provides utility functions and classes for make ESLint custom rules.

For examples:

-   [`getStaticValue`](https://eslint-community.github.io/eslint-utils/api/ast-utils.html#getstaticvalue) evaluates static value on AST.
-   [`ReferenceTracker`](https://eslint-community.github.io/eslint-utils/api/scope-utils.html#referencetracker-class) checks the members of modules/globals as handling assignments and destructuring.

## 📖 Usage

See [documentation](https://eslint-community.github.io/eslint-utils).

## 📰 Changelog

See [releases](https://github.com/eslint-community/eslint-utils/releases).

## ❤️ Contributing

Welcome contributing!

Please use GitHub's Issues/PRs.

### Development Tools

-   `npm run test-coverage` runs tests and measures coverage.
-   `npm run clean` removes the coverage result of `npm run test-coverage` command.
-   `npm run coverage` shows the coverage result of the last `npm run test-coverage` command.
-   `npm run lint` runs ESLint.
-   `npm run watch` runs tests on each file change.

# Installation
> `npm install --save @types/bonjour`

# Summary
This package contains type definitions for bonjour (https://github.com/watson/bonjour).

# Details
Files were exported from https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/bonjour.

### Additional Details
 * Last updated: Mon, 06 Nov 2023 22:41:05 GMT
 * Dependencies: [@types/node](https://npmjs.com/package/@types/node)

# Credits
These definitions were written by [<PERSON>](https://github.com/quentin-ol).

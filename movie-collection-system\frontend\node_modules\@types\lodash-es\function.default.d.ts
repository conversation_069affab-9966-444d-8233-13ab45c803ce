import after from "./after";
import ary from "./ary";
import before from "./before";
import bind from "./bind";
import bindKey from "./bindKey";
import curry from "./curry";
import curryRight from "./curryRight";
import debounce from "./debounce";
import defer from "./defer";
import delay from "./delay";
import flip from "./flip";
import memoize from "./memoize";
import negate from "./negate";
import once from "./once";
import overArgs from "./overArgs";
import partial from "./partial";
import partialRight from "./partialRight";
import rearg from "./rearg";
import rest from "./rest";
import spread from "./spread";
import throttle from "./throttle";
import unary from "./unary";
import wrap from "./wrap";

declare const defaultExport: {
    after: typeof after;
    ary: typeof ary;
    before: typeof before;
    bind: typeof bind;
    bindKey: typeof bindKey;
    curry: typeof curry;
    curryRight: typeof curryRight;
    debounce: typeof debounce;
    defer: typeof defer;
    delay: typeof delay;
    flip: typeof flip;
    memoize: typeof memoize;
    negate: typeof negate;
    once: typeof once;
    overArgs: typeof overArgs;
    partial: typeof partial;
    partialRight: typeof partialRight;
    rearg: typeof rearg;
    rest: typeof rest;
    spread: typeof spread;
    throttle: typeof throttle;
    unary: typeof unary;
    wrap: typeof wrap;
};
export default defaultExport;

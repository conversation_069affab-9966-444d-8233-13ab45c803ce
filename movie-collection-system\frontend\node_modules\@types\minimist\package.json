{"name": "@types/minimist", "version": "1.2.5", "description": "TypeScript definitions for minimist", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/minimist", "license": "MIT", "contributors": [{"name": "<PERSON>", "githubUsername": "Bartvds", "url": "https://github.com/Bartvds"}, {"name": "Necroskillz", "githubUsername": "Necroskillz", "url": "https://github.com/Necroskillz"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/kamranayub"}, {"name": "<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/peter<PERSON><PERSON><PERSON><PERSON>"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/minimist"}, "scripts": {}, "dependencies": {}, "typesPublisherContentHash": "e7dad9991b8f1cc7aae482ae6c4009a6a44645234b6f94524e2a76425cda2822", "typeScriptVersion": "4.5"}
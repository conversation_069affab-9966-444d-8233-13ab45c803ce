"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.isProxyAvailable = exports.getTarget = exports.getDevtoolsGlobalHook = void 0;
function getDevtoolsGlobalHook() {
    return getTarget().__VUE_DEVTOOLS_GLOBAL_HOOK__;
}
exports.getDevtoolsGlobalHook = getDevtoolsGlobalHook;
function getTarget() {
    // @ts-expect-error navigator and windows are not available in all environments
    return (typeof navigator !== 'undefined' && typeof window !== 'undefined')
        ? window
        : typeof globalThis !== 'undefined'
            ? globalThis
            : {};
}
exports.getTarget = getTarget;
exports.isProxyAvailable = typeof Proxy === 'function';

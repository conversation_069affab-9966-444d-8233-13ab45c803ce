{"name": "@vueuse/metadata", "version": "9.13.0", "description": "Metadata for VueUse functions", "author": "<PERSON> <https://github.com/antfu>", "license": "MIT", "funding": "https://github.com/sponsors/antfu", "homepage": "https://github.com/vueuse/vueuse/tree/main/packages/metadata#readme", "repository": {"type": "git", "url": "git+https://github.com/vueuse/vueuse.git", "directory": "packages/metadata"}, "bugs": {"url": "https://github.com/vueuse/vueuse/issues"}, "keywords": ["vue", "vue-use"], "sideEffects": false, "exports": {".": {"types": "./index.d.ts", "require": "./index.cjs", "import": "./index.mjs"}, "./*": "./*"}, "main": "./index.cjs", "module": "./index.mjs", "types": "./index.d.ts", "files": ["index.*"], "scripts": {"update": "esno scripts/update.ts"}}
{"name": "@webassemblyjs/ast", "version": "1.14.1", "description": "AST utils for webassemblyjs", "keywords": ["webassembly", "javascript", "ast"], "main": "lib/index.js", "module": "esm/index.js", "author": "<PERSON>", "license": "MIT", "dependencies": {"@webassemblyjs/helper-numbers": "1.13.2", "@webassemblyjs/helper-wasm-bytecode": "1.13.2"}, "repository": {"type": "git", "url": "https://github.com/xtuc/webassemblyjs.git"}, "publishConfig": {"access": "public"}, "devDependencies": {"@webassemblyjs/helper-test-framework": "1.14.1", "array.prototype.flatmap": "^1.2.1", "dump-exports": "^0.1.0", "mamacro": "^0.0.7"}, "gitHead": "25d52b1296e151ac56244a7c3886661e6b4a69ea"}
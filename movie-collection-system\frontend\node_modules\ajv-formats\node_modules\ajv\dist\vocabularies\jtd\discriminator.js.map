{"version": 3, "file": "discriminator.js", "sourceRoot": "", "sources": ["../../../lib/vocabularies/jtd/discriminator.ts"], "names": [], "mappings": ";;AAEA,mDAA+D;AAC/D,yCAAwC;AACxC,yCAA8C;AAC9C,mCAAwE;AACxE,kDAAgE;AAOhE,MAAM,KAAK,GAA2B;IACpC,OAAO,EAAE,CAAC,GAAG,EAAE,EAAE;QACf,MAAM,EAAC,MAAM,EAAE,MAAM,EAAC,GAAG,GAAG,CAAA;QAC5B,OAAO,MAAM,CAAC,UAAU;YACtB,CAAC,CAAC,MAAM,CAAC,UAAU,KAAK,kBAAU,CAAC,GAAG;gBACpC,CAAC,CAAC,QAAQ,MAAM,kBAAkB;gBAClC,CAAC,CAAC,iBAAiB,MAAM,sBAAsB;YACjD,CAAC,CAAC,IAAA,wBAAgB,EAAC,GAAG,EAAE,QAAQ,CAAC,CAAA;IACrC,CAAC;IACD,MAAM,EAAE,CAAC,GAAG,EAAE,EAAE;QACd,MAAM,EAAC,MAAM,EAAE,MAAM,EAAC,GAAG,GAAG,CAAA;QAC5B,OAAO,MAAM,CAAC,UAAU;YACtB,CAAC,CAAC,IAAA,WAAC,EAAA,WAAW,MAAM,CAAC,UAAU,UAAU,MAAM,eAAe,MAAM,CAAC,GAAG,GAAG;YAC3E,CAAC,CAAC,IAAA,uBAAe,EAAC,GAAG,EAAE,QAAQ,CAAC,CAAA;IACpC,CAAC;CACF,CAAA;AAED,MAAM,GAAG,GAA0B;IACjC,OAAO,EAAE,eAAe;IACxB,UAAU,EAAE,QAAQ;IACpB,UAAU,EAAE,CAAC,SAAS,CAAC;IACvB,KAAK;IACL,IAAI,CAAC,GAAe;QAClB,IAAA,wBAAa,EAAC,GAAG,CAAC,CAAA;QAClB,MAAM,EAAC,GAAG,EAAE,IAAI,EAAE,MAAM,EAAE,YAAY,EAAC,GAAG,GAAG,CAAA;QAC7C,MAAM,CAAC,KAAK,EAAE,IAAI,CAAC,GAAG,IAAA,8BAAmB,EAAC,GAAG,EAAE,IAAI,CAAC,CAAA;QAEpD,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,CAAA;QACZ,qBAAqB,EAAE,CAAA;QACvB,GAAG,CAAC,MAAM,CAAC,IAAA,aAAG,EAAC,KAAK,CAAC,CAAC,CAAA;QACtB,GAAG,CAAC,KAAK,EAAE,CAAA;QACX,GAAG,CAAC,KAAK,EAAE,CAAA;QACX,GAAG,CAAC,EAAE,CAAC,KAAK,CAAC,CAAA;QAEb,SAAS,qBAAqB;YAC5B,MAAM,GAAG,GAAG,GAAG,CAAC,KAAK,CAAC,KAAK,EAAE,IAAA,WAAC,EAAA,GAAG,IAAI,GAAG,IAAA,qBAAW,EAAC,MAAM,CAAC,EAAE,CAAC,CAAA;YAC9D,GAAG,CAAC,EAAE,CAAC,IAAA,WAAC,EAAA,GAAG,GAAG,gBAAgB,CAAC,CAAA;YAC/B,GAAG,CAAC,KAAK,CAAC,KAAK,EAAE,EAAC,UAAU,EAAE,kBAAU,CAAC,GAAG,EAAE,GAAG,EAAC,CAAC,CAAA;YACnD,GAAG,CAAC,MAAM,CAAC,IAAA,WAAC,EAAA,UAAU,GAAG,cAAc,CAAC,CAAA;YACxC,eAAe,CAAC,GAAG,CAAC,CAAA;YACpB,GAAG,CAAC,IAAI,EAAE,CAAA;YACV,GAAG,CAAC,KAAK,CAAC,KAAK,EAAE,EAAC,UAAU,EAAE,kBAAU,CAAC,GAAG,EAAE,GAAG,EAAC,EAAE,EAAC,YAAY,EAAE,MAAM,EAAC,CAAC,CAAA;YAC3E,GAAG,CAAC,KAAK,EAAE,CAAA;QACb,CAAC;QAED,SAAS,eAAe,CAAC,GAAS;YAChC,GAAG,CAAC,EAAE,CAAC,KAAK,CAAC,CAAA;YACb,KAAK,MAAM,QAAQ,IAAI,YAAY,CAAC,OAAO,EAAE,CAAC;gBAC5C,GAAG,CAAC,MAAM,CAAC,IAAA,WAAC,EAAA,GAAG,GAAG,QAAQ,QAAQ,EAAE,CAAC,CAAA;gBACrC,GAAG,CAAC,MAAM,CAAC,KAAK,EAAE,cAAc,CAAC,QAAQ,CAAC,CAAC,CAAA;YAC7C,CAAC;YACD,GAAG,CAAC,IAAI,EAAE,CAAA;YACV,GAAG,CAAC,KAAK,CACP,KAAK,EACL,EAAC,UAAU,EAAE,kBAAU,CAAC,OAAO,EAAE,GAAG,EAAC,EACrC,EAAC,YAAY,EAAE,MAAM,EAAE,UAAU,EAAE,SAAS,EAAE,YAAY,EAAE,IAAI,EAAC,CAClE,CAAA;YACD,GAAG,CAAC,KAAK,EAAE,CAAA;QACb,CAAC;QAED,SAAS,cAAc,CAAC,UAAkB;YACxC,MAAM,MAAM,GAAG,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,CAAA;YAChC,GAAG,CAAC,SAAS,CACX;gBACE,OAAO,EAAE,SAAS;gBAClB,UAAU;gBACV,gBAAgB,EAAE,MAAM;aACzB,EACD,MAAM,CACP,CAAA;YACD,OAAO,MAAM,CAAA;QACf,CAAC;IACH,CAAC;CACF,CAAA;AAED,kBAAe,GAAG,CAAA"}
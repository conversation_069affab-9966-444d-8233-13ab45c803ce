{"name": "async-validator", "description": "validate form asynchronous", "version": "4.2.5", "license": "MIT", "files": ["dist-*/", "bin/"], "pika": true, "sideEffects": false, "keywords": ["validator", "validate", "async"], "homepage": "https://github.com/yiminghe/async-validator", "bugs": {"url": "https://github.com/yiminghe/async-validator/issues"}, "repository": {"type": "git", "url": "**************:yiminghe/async-validator.git"}, "dependencies": {}, "devDependencies": {"@babel/core": "^7.15.0", "@babel/node": "^7.14.9", "@babel/preset-env": "^7.8.7", "@babel/preset-typescript": "^7.13.0", "@pika/pack": "^0.5.0", "@types/jest": "27.x", "babel-jest": "27.x", "coveralls": "^2.13.1", "jest": "27.x", "lint-staged": "^7.2.0", "np": "7.x", "pika-plugin-build-web-babel": "^0.10.0", "pika-plugin-ts-types": "0.1.x", "pre-commit": "^1.2.2", "prettier": "^1.11.1", "ts-node": "^10.8.1", "typescript": "^4.3.2"}, "types": "dist-types/index.d.ts", "main": "dist-node/index.js", "module": "dist-web/index.js"}
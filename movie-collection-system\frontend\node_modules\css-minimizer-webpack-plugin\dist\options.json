{"definitions": {"Rule": {"description": "Filtering rule as regex or string.", "anyOf": [{"instanceof": "RegExp", "tsType": "RegExp"}, {"type": "string", "minLength": 1}]}, "Rules": {"description": "Filtering rules.", "anyOf": [{"type": "array", "items": {"description": "A rule condition.", "oneOf": [{"$ref": "#/definitions/Rule"}]}}, {"$ref": "#/definitions/Rule"}]}, "MinimizerOptions": {"additionalProperties": true, "type": "object"}}, "title": "CssMinimizerWebpackPluginOptions", "type": "object", "properties": {"test": {"description": "Include all modules that pass test assertion.", "link": "https://github.com/webpack-contrib/css-minimizer-webpack-plugin/#test", "oneOf": [{"$ref": "#/definitions/Rules"}]}, "include": {"description": "Include all modules matching any of these conditions.", "link": "https://github.com/webpack-contrib/css-minimizer-webpack-plugin/#include", "oneOf": [{"$ref": "#/definitions/Rules"}]}, "exclude": {"description": "Exclude all modules matching any of these conditions.", "link": "https://github.com/webpack-contrib/css-minimizer-webpack-plugin/#exclude", "oneOf": [{"$ref": "#/definitions/Rules"}]}, "minimizerOptions": {"description": "Options for `cssMinimizerOptions`.", "link": "https://github.com/webpack-contrib/css-minimizer-webpack-plugin/#minimizeroptions", "anyOf": [{"$ref": "#/definitions/MinimizerOptions"}, {"type": "array", "minItems": 1, "items": {"$ref": "#/definitions/MinimizerOptions"}}]}, "parallel": {"description": "Use multi-process parallel running to improve the build speed.", "link": "https://github.com/webpack-contrib/css-minimizer-webpack-plugin/#parallel", "anyOf": [{"type": "boolean"}, {"type": "integer"}]}, "warningsFilter": {"description": "Allow to filter `css minimizer` warnings.", "link": "https://github.com/webpack-contrib/css-minimizer-webpack-plugin/#warningsfilter", "instanceof": "Function"}, "minify": {"description": "Allows you to override default minify function.", "link": "https://github.com/webpack-contrib/css-minimizer-webpack-plugin/#minify", "anyOf": [{"instanceof": "Function"}, {"type": "array", "minItems": 1, "items": {"instanceof": "Function"}}]}}, "additionalProperties": false}